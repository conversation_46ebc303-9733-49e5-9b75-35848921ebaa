import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import styles from './page.module.css';
import LandingHeader from '../../components/LandingHeader';
import VideoSequence from '../../components/VideoSequence';

export default function TestHome() {
  return (
    <div className={styles.container}>
      <LandingHeader />
      
      <main className={styles.main}>
        <section className={styles.hero}>
          <div className={styles.heroContent}>
            <h1 className={styles.title}>
              Transform Your<br />
              Designs Into<br />
              Engaging Videos
            </h1>
            <p className={styles.description}>
              Create stunning, shareable videos from your print-on-demand designs in minutes. 
              Boost your product visibility and drive more sales with eye-catching video ads.
            </p>
            <div className={styles.ctaButtons}>
              <Link href="/register" className={styles.primaryButton}>
                Get Started
              </Link>
              <Link href="/how-it-works" className={styles.secondaryButton}>
                Learn More
              </Link>
            </div>
          </div>
          
          <div className={styles.heroVideoContainer}>
            <div className={styles.videoFrame}>
              <div className={styles.imageOverlay}>
                <Image 
                  src="/brand-icon.png" 
                  alt="Brand Icon" 
                  width={150}
                  height={150} 
                  priority
                />
              </div>
              
              <div className={styles.videoDecoration}></div>
              <div className={styles.videoWrapper}>
                <VideoSequence />
              </div>
              <div className={styles.videoControls}>
                <div className={styles.controlDot}></div>
                <div className={styles.progressBar}>
                  <div className={styles.progressFill}></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section className={styles.features}>
          <h2 className={styles.sectionTitle}>Why Choose PodVideos?</h2>
          <div className={styles.featureGrid}>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🚀</div>
              <h3>Quick Conversion</h3>
              <p>Turn your static product designs into dynamic video content in just minutes, not hours.</p>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🎨</div>
              <h3>Beautiful Templates</h3>
              <p>Choose from dozens of professionally designed video templates optimized for product advertising.</p>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>📱</div>
              <h3>Multi-Platform Ready</h3>
              <p>Create ads optimized for Facebook, Instagram, TikTok, YouTube, and more.</p>
            </div>
            <div className={styles.featureCard}>
              <div className={styles.featureIcon}>🔍</div>
              <h3>AI-Powered Enhancement</h3>
              <p>Automatically enhance your product images with dynamic effects and engaging animations.</p>
            </div>
          </div>
        </section>

        <section className={styles.cta}>
          <h2>Ready to boost your product sales?</h2>
          <p>Join thousands of print-on-demand sellers who are increasing conversions with video advertising.</p>
          <Link href="/register" className={styles.primaryButton}>
            Start Creating Videos
          </Link>
        </section>
      </main>

      <footer className={styles.footer}>
        <div className={styles.footerContent}>
          <div className={styles.footerLogo}>
            <Image 
              src="/logo.png" 
              alt="PodVideos Logo" 
              width={120} 
              height={30} 
            />
            <p>© 2023 PodVideos. All rights reserved.</p>
          </div>
          <div className={styles.footerLinks}>
            <div className={styles.footerLinkColumn}>
              <h4>Product</h4>
              <Link href="/features">Features</Link>
              <Link href="/pricing">Pricing</Link>
              <Link href="/how-it-works">How It Works</Link>
            </div>
            <div className={styles.footerLinkColumn}>
              <h4>Company</h4>
              <Link href="/about">About Us</Link>
              <Link href="/contact">Contact</Link>
              <Link href="/blog">Blog</Link>
            </div>
            <div className={styles.footerLinkColumn}>
              <h4>Legal</h4>
              <Link href="/terms">Terms of Service</Link>
              <Link href="/privacy">Privacy Policy</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
} 