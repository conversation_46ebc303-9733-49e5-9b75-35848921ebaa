import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import Stripe from 'stripe'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
})

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!

export async function POST(request: NextRequest) {
  console.log('🔵 WEBHOOK: Received webhook request')
  console.log('🔵 WEBHOOK: Headers:', Object.fromEntries(request.headers.entries()))
  
  const body = await request.text()
  console.log('🔵 WEBHOOK: Body length:', body.length)
  
  const sig = request.headers.get('stripe-signature')!
  console.log('🔵 WEBHOOK: Stripe signature present:', !!sig)

  let event: Stripe.Event

  try {
    console.log('🔵 WEBHOOK: Attempting to construct event...')
    event = stripe.webhooks.constructEvent(body, sig, endpointSecret)
    console.log('✅ WEBHOOK: Event constructed successfully:', event.type, 'ID:', event.id)
  } catch (err: any) {
    console.error('❌ WEBHOOK: Signature verification failed:', err.message)
    console.error('❌ WEBHOOK: Endpoint secret exists:', !!endpointSecret)
    console.error('❌ WEBHOOK: Signature:', sig)
    return NextResponse.json(
      { error: 'Webhook signature verification failed' },
      { status: 400 }
    )
  }

  try {
    console.log('🔵 WEBHOOK: Processing event type:', event.type)
    
    switch (event.type) {
      case 'checkout.session.completed':
        console.log('🟢 WEBHOOK: Handling checkout.session.completed')
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session)
        break
      
      case 'payment_intent.succeeded':
        console.log('🟢 WEBHOOK: Handling payment_intent.succeeded')
        await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent)
        break
      
      case 'payment_intent.payment_failed':
        console.log('🟢 WEBHOOK: Handling payment_intent.payment_failed')
        await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent)
        break
      
      case 'invoice.payment_succeeded':
        console.log('🟡 WEBHOOK: Received invoice.payment_succeeded (not handling)')
        // Handle subscription payments if you add subscriptions later
        break
      
      default:
        console.log('🟡 WEBHOOK: Unhandled event type:', event.type)
    }

    console.log('✅ WEBHOOK: Event processed successfully')
    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('❌ WEBHOOK: Error handling webhook:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  console.log('🔵 CHECKOUT: Processing session:', session.id)
  console.log('🔵 CHECKOUT: Session metadata:', session.metadata)
  
  if (!session.metadata?.paymentId) {
    console.error('❌ CHECKOUT: No payment ID in session metadata')
    return
  }

  console.log('🔵 CHECKOUT: Updating payment with ID:', session.metadata.paymentId)

  // Update payment status
  const { data: payment, error: updateError } = await supabase
    .from('payments')
    .update({
      status: 'completed',
      stripe_payment_intent_id: session.payment_intent as string,
      payment_method: session.payment_method_types?.[0] || null,
      metadata: session.metadata
    })
    .eq('id', session.metadata.paymentId)
    .select()
    .single()

  if (updateError) {
    console.error('❌ CHECKOUT: Failed to update payment:', updateError)
    return
  }

  console.log('✅ CHECKOUT: Payment updated successfully:', payment)

  // Complete the payment and grant tokens
  if (payment) {
    console.log('🔵 CHECKOUT: Calling complete_payment function...')
    const result = await supabase.rpc('complete_payment', {
      payment_id_param: payment.id,
      stripe_payment_intent_id_param: session.payment_intent as string
    })

    if (result.error) {
      console.error('❌ CHECKOUT: Failed to complete payment:', result.error)
    } else {
      console.log('✅ CHECKOUT: Payment completed successfully:', result.data)
    }
  }
}

async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  console.log('🔵 PAYMENT_INTENT: Succeeded:', paymentIntent.id)
  
  // Update payment record with payment intent details
  const { error } = await supabase
    .from('payments')
    .update({
      stripe_payment_intent_id: paymentIntent.id,
      status: 'completed'
    })
    .eq('stripe_payment_intent_id', paymentIntent.id)

  if (error) {
    console.error('❌ PAYMENT_INTENT: Update failed:', error)
  } else {
    console.log('✅ PAYMENT_INTENT: Updated successfully')
  }
}

async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  console.log('🔵 PAYMENT_INTENT: Failed:', paymentIntent.id)
  
  // Update payment record to failed status
  const { error } = await supabase
    .from('payments')
    .update({
      stripe_payment_intent_id: paymentIntent.id,
      status: 'failed'
    })
    .eq('stripe_payment_intent_id', paymentIntent.id)

  if (error) {
    console.error('❌ PAYMENT_INTENT: Update failed:', error)
  } else {
    console.log('✅ PAYMENT_INTENT: Updated to failed')
  }
} 