// Test utility functions for web-generations API

// Character validation
export const CHARACTER_TYPES = ['scorpion', 'sub-zero', 'raiden', 'liu-kang', 'kitana', 'johnny-cage', 'mileena', 'kung-lao'] as const;
export type CharacterType = typeof CHARACTER_TYPES[number];

export const isValidCharacter = (character: string): character is CharacterType => {
  return CHARACTER_TYPES.includes(character as CharacterType);
};

// Cost calculation - Updated token pricing structure
export const TOKEN_COSTS = {
  image_generation: 5, // Step 1: Generate image
  approval: 5, // Step 2: Approve transformation
  video_generation: 35 // Step 3: Generate video (same for all resolutions)
};

export const calculateTotalTokens = () => {
  return TOKEN_COSTS.image_generation + TOKEN_COSTS.approval + TOKEN_COSTS.video_generation;
};

export const calculateCostInDollars = (tokens: number) => {
  return tokens * 0.01; // 1 token = $0.01
};

// Legacy cost calculation for backwards compatibility
export const COST_ESTIMATES = {
  image_transform: 0.05, // 5 tokens * $0.01
  video_generation: {
    '360p': 0.35,
    '540p': 0.35, 
    '720p': 0.35,
    '1080p': 0.35
  }
};

export const calculateTotalCost = (resolution: string) => {
  const videoCost = COST_ESTIMATES.video_generation[resolution as keyof typeof COST_ESTIMATES.video_generation] || 0.35;
  return COST_ESTIMATES.image_transform + videoCost;
};

export const calculateTokens = (costInDollars: number) => {
  return Math.ceil(costInDollars * 100); // 1 token = $0.01
};

// Test the utility functions
describe('Web Generations Utils', () => {
  describe('Character Validation', () => {
    it('validates correct character types', () => {
      expect(isValidCharacter('scorpion')).toBe(true);
      expect(isValidCharacter('sub-zero')).toBe(true);
      expect(isValidCharacter('raiden')).toBe(true);
      expect(isValidCharacter('liu-kang')).toBe(true);
      expect(isValidCharacter('kitana')).toBe(true);
      expect(isValidCharacter('johnny-cage')).toBe(true);
      expect(isValidCharacter('mileena')).toBe(true);
      expect(isValidCharacter('kung-lao')).toBe(true);
    });

    it('rejects invalid character types', () => {
      expect(isValidCharacter('invalid')).toBe(false);
      expect(isValidCharacter('')).toBe(false);
      expect(isValidCharacter('SCORPION')).toBe(false);
      expect(isValidCharacter('sub_zero')).toBe(false);
      expect(isValidCharacter('johnnycage')).toBe(false);
      expect(isValidCharacter('kung_lao')).toBe(false);
    });
  });

  describe('New Token-Based Cost Calculation', () => {
    it('calculates correct token costs for each step', () => {
      expect(TOKEN_COSTS.image_generation).toBe(5);
      expect(TOKEN_COSTS.approval).toBe(5);
      expect(TOKEN_COSTS.video_generation).toBe(35);
    });

    it('calculates total token cost', () => {
      expect(calculateTotalTokens()).toBe(45); // 5 + 5 + 35
    });

    it('converts tokens to dollars correctly', () => {
      expect(calculateCostInDollars(5)).toBeCloseTo(0.05);
      expect(calculateCostInDollars(35)).toBeCloseTo(0.35);
      expect(calculateCostInDollars(45)).toBeCloseTo(0.45);
    });
  });

  describe('Legacy Cost Calculation (Backwards Compatibility)', () => {
    it('calculates correct cost for all resolutions (now same)', () => {
      expect(calculateTotalCost('360p')).toBeCloseTo(0.40); // 0.05 + 0.35
      expect(calculateTotalCost('540p')).toBeCloseTo(0.40); // 0.05 + 0.35
      expect(calculateTotalCost('720p')).toBeCloseTo(0.40); // 0.05 + 0.35
      expect(calculateTotalCost('1080p')).toBeCloseTo(0.40); // 0.05 + 0.35
    });

    it('defaults to standard cost for unknown resolutions', () => {
      expect(calculateTotalCost('4k')).toBeCloseTo(0.40); // Default to standard
      expect(calculateTotalCost('unknown')).toBeCloseTo(0.40);
    });

    it('calculates correct token amounts from dollar amounts', () => {
      expect(calculateTokens(0.40)).toBe(40);
      expect(calculateTokens(0.45)).toBe(45);
      expect(calculateTokens(0.123)).toBe(13); // Rounds up
    });
  });

  describe('Integration Tests', () => {
    it('provides complete cost calculation flow for new characters', () => {
      const characters = ['johnny-cage', 'mileena', 'kung-lao'];
      
      characters.forEach(character => {
        // Validate character
        expect(isValidCharacter(character)).toBe(true);
        
        // Calculate costs using new token system
        const totalTokens = calculateTotalTokens();
        const totalCostDollars = calculateCostInDollars(totalTokens);
        
        expect(totalTokens).toBe(45);
        expect(totalCostDollars).toBe(0.45);
      });
    });

    it('maintains backwards compatibility for existing flow', () => {
      const character = 'scorpion';
      const resolution = '1080p';
      
      // Validate character
      expect(isValidCharacter(character)).toBe(true);
      
      // Calculate costs using legacy system
      const totalCost = calculateTotalCost(resolution);
      const tokens = calculateTokens(totalCost);
      
      expect(totalCost).toBeCloseTo(0.40);
      expect(tokens).toBe(40);
    });
  });
});



 
 