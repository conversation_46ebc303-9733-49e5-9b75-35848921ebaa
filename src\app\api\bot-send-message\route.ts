import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client using the service role key (kept secret)
const supabaseServiceRole = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface BotSendMessageRequest {
  conversationId: string;   // ID of the conversation
  text: string;             // Message text content
  imageUrl?: string;        // URL of an image to send (optional)
  imageId?: string;         // ID of a previously generated image to reference (optional)
}

/**
 * API endpoint for a bot to send a message to a user
 * This records the bot's message in the database for tracking/history
 */
export async function POST(request: Request) {
  try {
    const {
      conversationId,
      text,
      imageUrl,
      imageId
    } = await request.json() as BotSendMessageRequest;
    
    // Validate required fields
    if (!conversationId || (!text && !imageUrl && !imageId)) {
      return NextResponse.json({ 
        success: false, 
        error: "Missing required fields: conversationId and at least one of text, imageUrl, or imageId" 
      }, { status: 400 });
    }
    
    // Validate that we're not trying to send both imageUrl and imageId
    if (imageUrl && imageId) {
      return NextResponse.json({ 
        success: false, 
        error: "Cannot specify both imageUrl and imageId. Use only one." 
      }, { status: 400 });
    }
    
    // Step 1: Validate the conversation exists
    const { data: conversationData, error: conversationError } = await supabaseServiceRole
      .from('bot_conversations')
      .select('id, external_identity_id, provider, provider_conversation_id')
      .eq('id', conversationId)
      .single();
      
    if (conversationError) {
      return NextResponse.json({ 
        success: false, 
        error: "Conversation not found: " + conversationError.message 
      }, { status: 404 });
    }
    
    // Step 2: Record the bot message
    const { data: messageData, error: messageError } = await supabaseServiceRole.rpc(
      'record_bot_message',
      {
        p_conversation_id: conversationId,
        p_content: text || null,
        p_image_id: imageId || null
      }
    );
    
    if (messageError) {
      return NextResponse.json({ success: false, error: messageError.message }, { status: 500 });
    }
    
    // Return success response with conversation context for use in sending via the messaging platform
    return NextResponse.json({
      success: true,
      data: {
        messageId: messageData,
        conversationId,
        provider: conversationData.provider,
        providerConversationId: conversationData.provider_conversation_id,
        externalIdentityId: conversationData.external_identity_id,
        text,
        imageUrl,
        imageId
      }
    });
    
  } catch (err: any) {
    console.error('Error in bot-send-message API:', err);
    return NextResponse.json({ success: false, error: err.message }, { status: 500 });
  }
} 