import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client using the service role key (keep this key secret)
const supabaseServiceRole = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: Request) {
  try {
    const { chatId } = await request.json();

    if (!chatId) {
      return NextResponse.json(
        { error: "chatId is required" },
        { status: 400 }
      );
    }

    // Fetch the current chat record (assuming it contains a `phase` field).
    const { data: chatData, error: fetchError } = await supabaseServiceRole
      .from("chats")
      .select("phase")
      .eq("id", chatId)
      .single();

    if (fetchError || !chatData) {
      return NextResponse.json({ error: fetchError?.message || "Chat not found" }, { status: 500 });
    }

    const currentPhase = chatData.phase || 1;
    if (currentPhase <= 1) {
      return NextResponse.json({ error: "Chat is already at phase 1" }, { status: 400 });
    }

    const newPhase = currentPhase - 1;

    // Update the chat record to set the phase to the previous phase.
    const { data: updatedChat, error: updateError } = await supabaseServiceRole
      .from("chats")
      .update({ phase: newPhase })
      .eq("id", chatId)
      .select()
      .single();

    if (updateError) {
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data: updatedChat });
  } catch (err: any) {
    return NextResponse.json({ error: err.message }, { status: 500 });
  }
} 