# Complete Bot Flow with File Upload

This document describes the full flow for bot interactions including file uploads, message sending, image generation, and storage.

## Overview

The bot API supports a complete interaction flow that involves:
1. Receiving messages from users via external platforms (e.g., Telegram, WhatsApp)
2. Handling file uploads from users (e.g., reference images)
3. Generating images based on prompts and reference files
4. Sending messages back to users with generated images
5. Storing bot-generated files in the conversation history

## API Endpoints

The following API endpoints are used in the complete flow:

- `POST /api/bot-receive-message`: Receives a message from a user through an external platform
- `POST /api/bot-upload-file`: Handles file uploads from either users or the bot
- `POST /api/bot-generate-image`: Generates an image based on user prompts and reference images
- `POST /api/bot-send-message`: Sends a message (with optional image) to the user

## Example Flow

Here's a complete example flow:

### 1. User sends a message

```javascript
// POST /api/bot-receive-message
{
  "messageId": "message-123",
  "firstName": "John",
  "lastName": "Doe",
  "languageCode": "en",
  "chatId": "chat-456",
  "text": "Hello! I'd like to share an image with you.",
  "provider": "telegram",
  "providerUserId": "telegram-user-789",
  "providerBotId": "telegram-bot-012"
}

// Response
{
  "success": true,
  "data": {
    "externalIdentityId": "a63f8f30-ba72-41bb-aa14-e92bf45ebe1b",
    "walletId": "614324c6-11bc-4d32-8c0f-4ad28950ae30",
    "botId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "conversationId": "21895339-0c97-410a-a9f1-d4f6a39ec40a",
    "messageId": "message-123",
    "provider": "telegram",
    "providerUserId": "telegram-user-789",
    "balance": 100,
    "userName": "John Doe",
    "languageCode": "en"
  }
}
```

### 2. User uploads a file (e.g., reference image)

```javascript
// POST /api/bot-upload-file
{
  "conversationId": "21895339-0c97-410a-a9f1-d4f6a39ec40a",
  "externalIdentityId": "a63f8f30-ba72-41bb-aa14-e92bf45ebe1b",
  "fileBase64": "iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAKElEQVQ4jWNgYGD4Twzu6FhFFGYYNXDUwGFpIAk2E4dHDRw1cDgaCAASFOffhEIO3gAAAABJRU5ErkJggg==",
  "fileMetadata": {
    "originalFilename": "user_reference_image.png",
    "mimeType": "image/png",
    "size": 1024
  },
  "recordAsMessage": true,
  "sender": "user",
  "textMessage": "Here's a reference image for the mountains I want."
}

// Response
{
  "success": true,
  "data": {
    "uploadId": "5f8e3a12-3b6d-4f1a-9e8b-c7d2b5e4a9f3",
    "messageId": "7a1b2c3d-4e5f-6a7b-8c9d-0e1f2a3b4c5d",
    "conversationId": "21895339-0c97-410a-a9f1-d4f6a39ec40a",
    "externalIdentityId": "a63f8f30-ba72-41bb-aa14-e92bf45ebe1b",
    "fileUrl": "https://example.com/storage/conversations/21895339-0c97-410a-a9f1-d4f6a39ec40a/user_reference_image_1234567890.png",
    "filePath": "conversations/21895339-0c97-410a-a9f1-d4f6a39ec40a/user_reference_image_1234567890.png",
    "fileName": "user_reference_image_1234567890.png",
    "textMessage": "Here's a reference image for the mountains I want.",
    "provider": "telegram",
    "providerConversationId": "chat-456"
  }
}
```

### 3. Bot sends a message acknowledging the upload

```javascript
// POST /api/bot-send-message
{
  "conversationId": "21895339-0c97-410a-a9f1-d4f6a39ec40a",
  "text": "Thanks for sharing your photo! I'll generate something similar to those mountains."
}

// Response
{
  "success": true,
  "data": {
    "messageId": "b1c2d3e4-f5a6-b7c8-d9e0-f1a2b3c4d5e6"
  }
}
```

### 4. Bot generates an image based on the uploaded reference

```javascript
// POST /api/bot-generate-image
{
  "conversationId": "21895339-0c97-410a-a9f1-d4f6a39ec40a",
  "walletId": "614324c6-11bc-4d32-8c0f-4ad28950ae30",
  "tokenCost": 5,
  "operation": "generate",
  "promptText": "Mountains similar to the reference image, but with more snow and a sunset",
  "parameters": {
    "style": "realistic",
    "width": 1024,
    "height": 1024
  }
}

// Response
{
  "success": true,
  "data": {
    "imageId": "c3d4e5f6-a7b8-c9d0-e1f2-a3b4c5d6e7f8",
    "resultUrl": "https://example.com/storage/generated-images/mountains_snow_sunset_1234567890.png",
    "remainingBalance": 95
  }
}
```

### 5. Bot sends the generated image to the user

```javascript
// POST /api/bot-send-message
{
  "conversationId": "21895339-0c97-410a-a9f1-d4f6a39ec40a",
  "text": "Here is your mountain image with added snow and sunset!",
  "imageUrl": "https://example.com/storage/generated-images/mountains_snow_sunset_1234567890.png"
}

// Response
{
  "success": true,
  "data": {
    "messageId": "d5e6f7a8-b9c0-d1e2-f3a4-b5c6d7e8f9a0"
  }
}
```

### 6. Bot saves the generated image as a file in the conversation history

```javascript
// POST /api/bot-upload-file
{
  "conversationId": "21895339-0c97-410a-a9f1-d4f6a39ec40a",
  "externalIdentityId": "a63f8f30-ba72-41bb-aa14-e92bf45ebe1b",
  "fileUrl": "https://example.com/storage/generated-images/mountains_snow_sunset_1234567890.png",
  "fileMetadata": {
    "originalFilename": "generated_mountains.png",
    "mimeType": "image/png"
  },
  "recordAsMessage": true,
  "sender": "bot",
  "textMessage": "I've saved this image to your conversation history."
}

// Response
{
  "success": true,
  "data": {
    "uploadId": "e7f8a9b0-c1d2-e3f4-a5b6-c7d8e9f0a1b2",
    "messageId": "f9a0b1c2-d3e4-f5a6-b7c8-d9e0f1a2b3c4",
    "conversationId": "21895339-0c97-410a-a9f1-d4f6a39ec40a",
    "externalIdentityId": "a63f8f30-ba72-41bb-aa14-e92bf45ebe1b",
    "fileUrl": "https://example.com/storage/conversations/21895339-0c97-410a-a9f1-d4f6a39ec40a/generated_mountains_1234567890.png",
    "filePath": "conversations/21895339-0c97-410a-a9f1-d4f6a39ec40a/generated_mountains_1234567890.png",
    "fileName": "generated_mountains_1234567890.png",
    "textMessage": "I've saved this image to your conversation history.",
    "provider": "telegram",
    "providerConversationId": "chat-456"
  }
}
```

## Database Structure

The complete flow updates the following database tables:
- `external_identities`: Stores user identities from external platforms
- `wallets`: Tracks token balances for users
- `bots`: Records information about different bot instances
- `bot_conversations`: Tracks conversations between users and bots
- `messages`: Stores all messages in conversations
- `uploads`: Records file uploads with metadata
- `images`: Stores generated image information

## Cypress Testing

The complete flow is tested using Cypress end-to-end tests:
- `bot-complete-flow.cy.js`: Tests the basic message and image generation flow
- `bot-complete-flow-with-file.cy.js`: Tests the full flow including file uploads

## Further Considerations

1. **File Storage**: All uploaded and generated files are stored in Supabase Storage with organized paths
2. **Message Context**: All files can be associated with text messages for proper context
3. **Sender Identification**: The `sender` field distinguishes between user and bot uploads
4. **Conversation Organization**: Files are organized by conversation ID for easy retrieval
5. **Database Indexing**: Appropriate indexes ensure fast retrieval of conversation history 