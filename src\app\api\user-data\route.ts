import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { authenticateApiRequest } from './auth-middleware';

// Create a Supabase admin client with service role key for server-side access
// This bypasses Row Level Security (RLS) policies
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

export async function GET(request: NextRequest) {
  return authenticateApiRequest(request, handleGetUserData);
}

// The actual handler function for the GET request
async function handleGetUserData(request: NextRequest) {
  try {
    // Get query parameters from the request URL
    const searchParams = request.nextUrl.searchParams;
    const telegramId = searchParams.get('telegram_id');
    const whatsappId = searchParams.get('whatsapp_id');
    
    // Check if at least one identifier is provided
    if (!telegramId && !whatsappId) {
      return NextResponse.json(
        { error: 'Either telegram_id or whatsapp_id must be provided' }, 
        { status: 400 }
      );
    }
    
    // Find the external identity using admin client that bypasses RLS
    let externalIdentityQuery = supabaseAdmin.from('external_identities').select('*');
    
    if (telegramId) {
      externalIdentityQuery = externalIdentityQuery.eq('telegram_id', telegramId);
    } else if (whatsappId) {
      externalIdentityQuery = externalIdentityQuery.eq('whatsapp_id', whatsappId);
    }
    
    const { data: externalIdentity, error: externalIdentityError } = await externalIdentityQuery.maybeSingle();
    
    // If no user found or there was an error
    if (externalIdentityError || !externalIdentity) {
      return NextResponse.json(
        { 
          exists: false, 
          message: 'User not found',
          data: null
        }, 
        { status: 404 }
      );
    }
    
    // Check if this external identity is linked to a web app user
    const hasWebAccount = !!externalIdentity.user_id;
    
    // Get wallet data for tokens
    const { data: wallet, error: walletError } = await supabaseAdmin
      .from('wallets')
      .select('*')
      .eq('external_identity_id', externalIdentity.id)
      .maybeSingle();
    
    // Get count of images generated by this user
    const { count: imagesGenerated, error: imagesError } = await supabaseAdmin
      .from('images')
      .select('id', { count: 'exact', head: true })
      .eq('wallet_id', wallet?.id || '');
    
    // Get the most recent image generation (if any)
    const { data: mostRecentImage, error: recentImageError } = await supabaseAdmin
      .from('images')
      .select('created_at')
      .eq('wallet_id', wallet?.id || '')
      .order('created_at', { ascending: false })
      .limit(1);
      
    // Get conversation count
    const { count: conversationCount, error: conversationError } = await supabaseAdmin
      .from('bot_conversations')
      .select('id', { count: 'exact', head: true })
      .eq('external_identity_id', externalIdentity.id);
      
    // Construct the response object
    const userData = {
      exists: true,
      external_identity: {
        id: externalIdentity.id,
        telegram_id: externalIdentity.telegram_id,
        whatsapp_id: externalIdentity.whatsapp_id,
        created_at: externalIdentity.created_at,
        telegram_airdrop_granted: externalIdentity.telegram_airdrop_granted,
        whatsapp_airdrop_granted: externalIdentity.whatsapp_airdrop_granted
      },
      has_web_account: hasWebAccount,
      web_account_id: externalIdentity.user_id,
      wallet: wallet ? {
        id: wallet.id,
        balance: wallet.balance,
        updated_at: wallet.updated_at
      } : null,
      stats: {
        images_generated: imagesGenerated || 0,
        last_generation: mostRecentImage?.[0]?.created_at || null,
        conversation_count: conversationCount || 0
      }
    };
    
    return NextResponse.json(userData);
    
  } catch (error) {
    console.error('Unexpected error in user-data API route:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' }, 
      { status: 500 }
    );
  }
} 