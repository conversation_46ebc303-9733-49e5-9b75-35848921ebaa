"use client";

import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabaseClient';

export default function TestSupabasePage() {
  const [testResult, setTestResult] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [testId, setTestId] = useState('telegram-test-id-123');

  async function runTest() {
    setLoading(true);
    setError(null);
    try {
      // Test connection
      const connectionTest = await supabase.from('external_identities').select('id').limit(1);
      
      // Test specific query
      const specificQuery = await supabase
        .from('external_identities')
        .select('*')
        .eq('telegram_id', testId)
        .single();
      
      setTestResult({
        connectionTest: {
          success: !connectionTest.error,
          count: connectionTest.data?.length || 0,
          error: connectionTest.error
        },
        specificQuery: {
          success: !specificQuery.error,
          data: specificQuery.data,
          error: specificQuery.error
        },
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
        supabaseKeyLength: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length || 0,
        actualSupabaseUrl: window.origin.includes('localhost') 
          ? 'https://uhlmctpwxyubiyhakxqs.supabase.co' 
          : process.env.NEXT_PUBLIC_SUPABASE_URL,
        actualSupabaseKeyLength: window.origin.includes('localhost') 
          ? 208
          : process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length || 0
      });
    } catch (e: any) {
      setError(e.message || 'Unknown error');
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    runTest();
  }, []);

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Supabase Connection Test</h1>
      
      <div className="mb-4">
        <label className="block mb-2">Test Telegram ID:</label>
        <div className="flex">
          <input 
            type="text" 
            value={testId} 
            onChange={(e) => setTestId(e.target.value)}
            className="border p-2 rounded mr-2 flex-grow"
          />
          <button 
            onClick={runTest} 
            className="bg-blue-500 text-white px-4 py-2 rounded"
            disabled={loading}
          >
            {loading ? 'Testing...' : 'Test Connection'}
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 p-4 rounded mb-4">
          {error}
        </div>
      )}

      {testResult && (
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">Test Results</h2>
          
          <div className="mb-4">
            <h3 className="font-semibold">Environment:</h3>
            <pre className="bg-gray-200 p-2 rounded">
              {JSON.stringify({
                supabaseUrl: testResult.supabaseUrl,
                supabaseKeyLength: testResult.supabaseKeyLength
              }, null, 2)}
            </pre>
          </div>
          
          <div className="mb-4">
            <h3 className="font-semibold">Actual Environment:</h3>
            <pre className="bg-gray-200 p-2 rounded">
              {JSON.stringify({
                actualSupabaseUrl: testResult.actualSupabaseUrl,
                actualSupabaseKeyLength: testResult.actualSupabaseKeyLength
              }, null, 2)}
            </pre>
          </div>
          
          <div className="mb-4">
            <h3 className="font-semibold">Connection Test:</h3>
            <pre className="bg-gray-200 p-2 rounded">
              {JSON.stringify(testResult.connectionTest, null, 2)}
            </pre>
          </div>
          
          <div>
            <h3 className="font-semibold">Specific Query Test:</h3>
            <pre className="bg-gray-200 p-2 rounded overflow-auto max-h-96">
              {JSON.stringify(testResult.specificQuery, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
} 