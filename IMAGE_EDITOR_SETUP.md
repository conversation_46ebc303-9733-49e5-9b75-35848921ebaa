# Image Editor Setup Guide

This guide explains how to set up and configure the AI-powered Image Editor feature in the admin section.

## Overview

The Image Editor uses the **FLUX.1 Kontext [pro]** API from [fal.ai](https://fal.ai/models/fal-ai/flux-pro/kontext/api) to provide AI-powered image editing capabilities. Users can upload images and use text prompts to make targeted edits and transformations.

## Features

- **Image Upload**: Drag & drop or file picker for image selection
- **AI-Powered Editing**: Text-based prompts for image modifications
- **Advanced Settings**: Configurable guidance scale, safety tolerance, output format, and seed
- **Before/After Comparison**: Side-by-side view of original and edited images
- **Edit History**: Track and download previous edits
- **Download Results**: Save edited images in JPEG or PNG format

## Prerequisites

1. **FAL.ai Account**: You need a FAL.ai account and API key
2. **Admin Access**: Only admin users can access the Image Editor

## Setup Instructions

### Step 1: Get Your FAL.ai API Key

1. Visit [fal.ai](https://fal.ai) and create an account
2. Navigate to your [API Keys section](https://fal.ai/dashboard/keys)
3. Create a new API key with appropriate permissions
4. Copy the API key (starts with `fal_`)

### Step 2: Configure Environment Variables

Add your FAL API key to your environment variables:

#### For Local Development (.env.local)
```bash
FAL_API_KEY=your_fal_api_key_here
```

#### For Production Deployment
Set the environment variable on your hosting platform:

**Vercel:**
1. Go to your project dashboard
2. Navigate to Settings > Environment Variables
3. Add `FAL_API_KEY` with your API key value

**Other Platforms:**
Set the `FAL_API_KEY` environment variable according to your platform's documentation.

### Step 3: Access the Image Editor

1. Log in as an admin user
2. Navigate to the Admin Dashboard
3. Click on "Image Editor" in the navigation menu
4. You should see the image editor interface

## How to Use

### Basic Workflow

1. **Upload an Image**
   - Click the upload area or drag & drop an image
   - Supported formats: JPEG, PNG, WebP
   - Maximum recommended size: 10MB

2. **Enter a Prompt**
   - Describe the changes you want to make
   - Examples:
     - "Add a rainbow in the sky"
     - "Change the background to a forest"
     - "Remove the person from the image"
     - "Make the image black and white"

3. **Adjust Settings (Optional)**
   - Click "Settings" to configure advanced options
   - **Guidance Scale**: How closely the AI follows your prompt (1-10)
   - **Safety Tolerance**: Content filtering level (1-5)
   - **Output Format**: JPEG or PNG
   - **Seed**: For reproducible results (optional)

4. **Edit the Image**
   - Click "Edit Image" to process
   - Wait for the AI to generate the result
   - View the before/after comparison

5. **Download Results**
   - Click the download button to save the edited image
   - Images are saved with timestamp in the filename

### Advanced Features

#### Settings Explained

- **Guidance Scale (1-10)**:
  - Lower values: More creative, less adherence to prompt
  - Higher values: Stricter adherence to prompt
  - Default: 3.5

- **Safety Tolerance (1-5)**:
  - 1: Most strict content filtering
  - 5: Most permissive
  - Default: 2

- **Seed**:
  - Use the same seed for reproducible results
  - Leave empty for random generation

#### Edit History

- All edits in the current session are saved to history
- View thumbnails of before/after images
- Download any previous edit result
- History is cleared when you refresh the page

## API Endpoints

The image editor uses these API endpoints:

### POST /api/image-edit

Processes image editing requests using the FAL API.

**Request Body:**
```json
{
  "prompt": "Add a rainbow in the sky",
  "image_url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABA...",
  "guidance_scale": 3.5,
  "num_images": 1,
  "safety_tolerance": "2",
  "output_format": "jpeg",
  "aspect_ratio": "1:1",
  "seed": 12345
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "images": [
      {
        "url": "https://fal.media/files/tiger/...",
        "width": 1024,
        "height": 1024
      }
    ],
    "timings": {},
    "seed": 12345,
    "has_nsfw_concepts": [false],
    "prompt": "Add a rainbow in the sky"
  },
  "requestId": "764cabcf-b745-4b3e-ae38-1200304cf45b"
}
```

## Troubleshooting

### Common Issues

#### "FAL API key not configured"
- **Cause**: The `FAL_API_KEY` environment variable is not set
- **Solution**: Add the API key to your environment variables and restart the server

#### "Failed to edit image"
- **Cause**: Various issues including network problems, invalid image, or API errors
- **Solution**: 
  - Check your internet connection
  - Ensure the image is valid and not corrupted
  - Verify your FAL.ai account has sufficient credits
  - Check the browser console for detailed error messages

#### "Please select an image and provide a prompt"
- **Cause**: Missing required inputs
- **Solution**: Ensure you've uploaded an image and entered a text prompt

#### Image not uploading
- **Cause**: File size too large or unsupported format
- **Solution**: 
  - Use images smaller than 10MB
  - Ensure the file is JPEG, PNG, or WebP format

### Debug Mode

To enable debug logging, check the browser console when using the image editor. All API requests and responses are logged for troubleshooting.

## Cost Considerations

- Each image edit consumes FAL.ai credits
- Credit usage depends on image size and complexity
- Monitor your FAL.ai dashboard for usage tracking
- Consider setting up billing alerts in your FAL.ai account

## Security Notes

- API keys are stored server-side only
- Images are processed through FAL.ai's secure infrastructure
- No images are permanently stored on your server
- Original images are only kept in memory during the editing session

## Technical Details

### Dependencies Added
- `@fal-ai/client`: Official FAL.ai JavaScript client

### Files Created/Modified
- `src/app/admin/image-editor/page.tsx`: Main image editor component
- `src/app/api/image-edit/route.ts`: Server-side API endpoint
- `src/app/admin/layout.tsx`: Added navigation link
- `src/app/admin/admin.module.css`: Added styling

### Image Processing Flow
1. User uploads image → Converted to base64 data URI
2. User enters prompt → Combined with settings
3. Request sent to `/api/image-edit` → Forwards to FAL API
4. FAL processes the request → Returns edited image URL
5. Result displayed → User can download or edit further

## Support

For issues related to:
- **FAL.ai API**: Check [FAL.ai documentation](https://fal.ai/docs) or contact their support
- **Image Editor UI**: Check the browser console for errors and verify the setup steps
- **Environment Variables**: Ensure they're properly set and the server is restarted

## Changelog

### v1.0.0 (Initial Release)
- Basic image upload and editing functionality
- Integration with FLUX.1 Kontext [pro] API
- Advanced settings panel
- Edit history tracking
- Download functionality
- Responsive design 