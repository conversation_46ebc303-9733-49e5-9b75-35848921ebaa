import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { fal } from '@fal-ai/client';

// Add Sharp for image processing
import sharp from 'sharp';

// Create a Supabase client specifically for server-side use with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

// Configure the FAL client with the API key
fal.config({
  credentials: process.env.FAL_API_KEY || ''
});

interface StartFlowRequest {
  character_type: 'scorpion' | 'sub-zero' | 'raiden' | 'liu-kang' | 'kitana' | 'johnny-cage' | 'mileena' | 'kung-lao' | 'sindel' | 'fujin';
  video_settings: {
    duration: 5 | 8;
    resolution: '360p' | '540p' | '720p' | '1080p';
    style?: 'anime' | '3d_animation' | 'comic';
  };
}

// Character transformation prompts - focused on costume while preserving original person and background
const CHARACTER_PROMPTS = {
  scorpion: "Dress this person as <PERSON>orpion from Mortal Kombat. Keep their face, body, and background unchanged but add: yellow ninja outfit with black trim, Scorpion's signature skull mask covering lower face, kunai weapon with chain rope. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle fire/ember particle effects around the person without changing the background. Photorealistic, professional costume photography.",
  'sub-zero': "Dress this person as Sub-Zero from Mortal Kombat. Keep their face, body, and background unchanged but add: blue ninja outfit with ice-blue accents, Sub-Zero's signature ice mask covering lower face, frost effects around hands only. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle ice particle effects around the person without changing the background. Photorealistic, professional costume photography.",
  raiden: "Dress this person as Raiden from Mortal Kombat. Keep their face, body, and background unchanged but add: white and blue traditional outfit with lightning patterns, Raiden's conical straw hat, subtle blue glow in eyes. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle electrical spark effects around the person without changing the background. Photorealistic, professional costume photography.",
  'liu-kang': "Dress this person as Liu Kang from Mortal Kombat. Keep their face, body, and background unchanged but add: red martial arts outfit with black trim and dragon motifs, red bandana/headband. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle fire particle effects around fists without changing the background. Photorealistic, professional costume photography.",
  kitana: "Dress this person as Kitana from Mortal Kombat. Keep their face, body, and background unchanged but add: royal blue and black outfit with elegant design, decorative mask covering lower face, steel fans as weapons. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle magical sparkle effects around the person without changing the background. Photorealistic, professional costume photography.",
  'johnny-cage': "Dress this person as Johnny Cage from Mortal Kombat. Keep their face, body, and background unchanged but add: designer sunglasses, black military pants, open vest showing chest, confident Hollywood action star look. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle green energy glow around hands without changing the background. Photorealistic, professional costume photography.",
  mileena: "Dress this person as Mileena from Mortal Kombat. Keep their face, body, and background unchanged but add: purple ninja outfit with revealing design, Mileena's signature sai weapons, pink/purple mask covering lower face. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle dark energy effects around the person without changing the background. Photorealistic, professional costume photography.",
  'kung-lao': "Dress this person as Kung Lao from Mortal Kombat. Keep their face, body, and background unchanged but add: traditional Shaolin monk robes in blue and white, Kung Lao's signature razor-rimmed hat. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle wind effects around the hat without changing the background. Photorealistic, professional costume photography.",
  sindel: "Dress this person as Sindel from Mortal Kombat. Keep their face, body, and background unchanged but add: regal purple and black outfit with silver accents, long flowing white hair, Sindel's signature spiked tiara, and mystical aura. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle purple energy waves around the person without changing the background. Photorealistic, professional costume photography.",
  fujin: "Dress this person as Fujin from Mortal Kombat. Keep their face, body, and background unchanged but add: wind god attire with white and blue robes, Fujin's signature long ponytail, silver armor, and a staff. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle wind swirl effects around the person without changing the background. Photorealistic, professional costume photography."
};

// Cost estimation - users always pay 10 tokens for image generation regardless of backend model
const COST_ESTIMATES = {
  image_transform: 0.10, // Users always pay 10 tokens ($0.10) for image generation
  video_generation: {
    '360p': 0.30,
    '540p': 0.30, 
    '720p': 0.40,
    '1080p': 0.80
  }
};

// Transformation function for GPT-Image-1
async function transformWithGPTImage1(imageUrl: string, prompt: string) {
  const result = await fal.subscribe("fal-ai/gpt-image-1/edit-image/byok", {
    input: {
      image_urls: [imageUrl],
      prompt: prompt,
      image_size: "auto",
      num_images: 1,
      quality: "auto",
      openai_api_key: process.env.OPENAI_API_KEY
    },
    logs: true,
    onQueueUpdate: (update) => {
      if (update.status === "IN_PROGRESS") {
        console.log("GPT-Image-1 Processing:", update.logs?.map((log) => log.message).join('\n'));
      }
    },
  });

  return {
    transformedImageUrl: result.data.images[0].url,
    requestId: result.requestId,
    originalResponse: result.data,
    model: 'gpt-image-1/edit-image/byok',
    userTokenCost: 10, // User always pays 10 tokens
    internalCost: 0.04 // Internal API cost $0.04
  };
}

// Transformation function for SeedEdit
async function transformWithSeedEdit(imageUrl: string, prompt: string) {
  const result = await fal.subscribe("fal-ai/bytedance/seededit/v3/edit-image", {
    input: {
      prompt: prompt,
      image_url: imageUrl,
      guidance_scale: 0.5
    },
    logs: true,
    onQueueUpdate: (update) => {
      if (update.status === "IN_PROGRESS") {
        console.log("SeedEdit Processing:", update.logs?.map((log) => log.message).join('\n'));
      }
    },
  });

  return {
    transformedImageUrl: result.data.image.url,
    requestId: result.requestId,
    originalResponse: result.data,
    model: 'fal-ai/bytedance/seededit/v3/edit-image',
    userTokenCost: 10, // User always pays 10 tokens
    internalCost: 0.02 // Internal API cost $0.02 (cheaper for us)
  };
}

// Transformation function for FLUX.1 Kontext [pro]
async function transformWithFluxKontext(imageUrl: string, prompt: string) {
  const result = await fal.subscribe("fal-ai/flux-pro/kontext", {
    input: {
      prompt: prompt,
      image_url: imageUrl,
      guidance_scale: 3.5,
      num_images: 1,
      safety_tolerance: "2",
      output_format: "jpeg"
    },
    logs: true,
    onQueueUpdate: (update) => {
      if (update.status === "IN_PROGRESS") {
        console.log("FLUX Kontext Processing:", update.logs?.map((log) => log.message).join('\n'));
      }
    },
  });

  return {
    transformedImageUrl: result.data.images[0].url,
    requestId: result.requestId,
    originalResponse: result.data,
    model: 'fal-ai/flux-pro/kontext',
    userTokenCost: 10, // User always pays 10 tokens
    internalCost: 0.03 // Internal API cost (estimated)
  };
}

// Get selected image generator from database
async function getSelectedImageGenerator(): Promise<string> {
  const { data, error } = await supabaseAdmin
    .from('config_options')
    .select('value')
    .eq('key', 'mk_image_generator')
    .single();

  if (error || !data || !data.value || !data.value.selected) {
    console.log('No image generator config found, defaulting to gpt-image-1');
    return 'gpt-image-1';
  }

  return data.value.selected;
}

// Utility function to map selectedImageGenerator to fal_model
function getFalModelFromGenerator(selectedImageGenerator: string): string {
  switch (selectedImageGenerator) {
    case 'gpt-image-1':
      return 'gpt-image-1/edit-image/byok';
    case 'fal-seededit-v3':
      return 'fal-ai/bytedance/seededit/v3/edit-image';
    case 'fal-flux-pro-kontext':
      return 'fal-ai/flux-pro/kontext';
    default:
      throw new Error(`Unknown image generator: ${selectedImageGenerator}`);
  }
}

// Add image processing utility function
async function processImageForFAL(imageFile: File): Promise<{ buffer: Buffer; contentType: string; fileName: string }> {
  const MAX_DIMENSION = 3840; // Slightly under 4000 to be safe
  const QUALITY = 85; // JPEG quality when converting

  try {
    // Convert File to Buffer
    const arrayBuffer = await imageFile.arrayBuffer();
    const inputBuffer = Buffer.from(arrayBuffer);
    
    // Get image metadata
    const metadata = await sharp(inputBuffer).metadata();
    const { width = 0, height = 0, format } = metadata;
    
    console.log(`Original image: ${width}x${height}, format: ${format}`);
    
    // Check if resizing is needed
    const needsResize = width > MAX_DIMENSION || height > MAX_DIMENSION;
    
    if (!needsResize) {
      // Image is within limits, return as-is but ensure it's in a supported format
      let processedBuffer = inputBuffer;
      let outputFormat = format;
      
      // Convert HEIC, WEBP, or other formats to JPEG for better compatibility
      if (format && !['jpeg', 'jpg', 'png'].includes(format.toLowerCase())) {
        processedBuffer = await sharp(inputBuffer)
          .rotate() // Auto-rotate based on EXIF orientation
          .jpeg({ quality: QUALITY })
          .toBuffer();
        outputFormat = 'jpeg';
        console.log(`Converted ${format} to JPEG for compatibility`);
      } else {
        // Even for supported formats, apply EXIF rotation if needed
        const needsRotation = metadata.orientation && metadata.orientation !== 1;
        if (needsRotation) {
          processedBuffer = await sharp(inputBuffer)
            .rotate() // Auto-rotate based on EXIF orientation
            .toBuffer();
          console.log(`Applied EXIF orientation correction`);
        }
      }
      
      return {
        buffer: processedBuffer,
        contentType: outputFormat === 'png' ? 'image/png' : 'image/jpeg',
        fileName: imageFile.name.replace(/\.[^/.]+$/, '') + (outputFormat === 'png' ? '.png' : '.jpg')
      };
    }
    
    // Calculate scale factor to keep largest dimension under MAX_DIMENSION
    const maxCurrentDimension = Math.max(width, height);
    const scaleFactor = Math.min(MAX_DIMENSION / maxCurrentDimension, 1.0); // Never scale up
    
    const newWidth = Math.round(width * scaleFactor);
    const newHeight = Math.round(height * scaleFactor);
    
    console.log(`Resizing image from ${width}x${height} to ${newWidth}x${newHeight}`);
    
    // Resize image using proportional scaling (no distortion)
    const resizedBuffer = await sharp(inputBuffer)
      .rotate() // Auto-rotate based on EXIF orientation
      .resize(newWidth, newHeight, {
        fit: 'inside', // Scale to fit inside dimensions, maintaining aspect ratio
        kernel: sharp.kernel.lanczos3,
        withoutEnlargement: true
      })
      .jpeg({ 
        quality: QUALITY,
        progressive: true
      })
      .toBuffer();
    
    const originalSizeMB = (inputBuffer.length / 1024 / 1024).toFixed(2);
    const newSizeMB = (resizedBuffer.length / 1024 / 1024).toFixed(2);
    console.log(`Image size reduced from ${originalSizeMB}MB to ${newSizeMB}MB`);
    
    return {
      buffer: resizedBuffer,
      contentType: 'image/jpeg',
      fileName: imageFile.name.replace(/\.[^/.]+$/, '') + '_resized.jpg'
    };
    
  } catch (error) {
    console.error('Image processing error:', error);
    // Fallback: return original file as buffer
    const arrayBuffer = await imageFile.arrayBuffer();
    return {
      buffer: Buffer.from(arrayBuffer),
      contentType: imageFile.type,
      fileName: imageFile.name
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      return NextResponse.json(
        { error: 'Supabase configuration missing' },
        { status: 500 }
      );
    }

    const formData = await request.formData();
    const imageFile = formData.get('image') as File;
    const settingsJson = formData.get('settings') as string;
    
    if (!imageFile || !settingsJson) {
      return NextResponse.json(
        { error: 'Image file and settings are required' },
        { status: 400 }
      );
    }

    const settings: StartFlowRequest = JSON.parse(settingsJson);
    
    // Validate settings
    if (!CHARACTER_PROMPTS[settings.character_type]) {
      return NextResponse.json(
        { error: 'Invalid character type' },
        { status: 400 }
      );
    }

    // Get user from auth header or cookie
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required - no token found' },
        { status: 401 }
      );
    }

    // Get user info
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      console.error('Auth error:', authError);
      return NextResponse.json(
        { error: 'Invalid authentication', details: authError?.message },
        { status: 401 }
      );
    }

    // Get user's wallet
    const { data: wallet, error: walletError } = await supabaseAdmin
      .from('wallets')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (walletError || !wallet) {
      return NextResponse.json(
        { error: 'User wallet not found' },
        { status: 404 }
      );
    }

    // Get selected image generator
    const selectedImageGenerator = await getSelectedImageGenerator();
    console.log('Using image generator:', selectedImageGenerator);

    // Calculate estimated cost
    const estimatedCost = COST_ESTIMATES.image_transform + 
                         COST_ESTIMATES.video_generation[settings.video_settings.resolution];

    // Check if user has sufficient balance (convert to tokens, assume 1 token = $0.01)
    const requiredTokens = Math.ceil(estimatedCost * 100);
    
    if (wallet.balance < requiredTokens) {
      return NextResponse.json(
        { error: 'Insufficient token balance', required: requiredTokens, available: wallet.balance },
        { status: 402 }
      );
    }

    // Check image dimensions first
    const imageArrayBuffer = await imageFile.arrayBuffer();
    const imageBuffer = Buffer.from(imageArrayBuffer);
    const imageMetadata = await sharp(imageBuffer).metadata();
    const { width = 0, height = 0 } = imageMetadata;
    
    console.log(`Image dimensions: ${width}x${height}`);
    
    // If image is larger than 4000px, return resized preview for user confirmation
    const MAX_DIMENSION = 4000;
    const needsResizing = width > MAX_DIMENSION || height > MAX_DIMENSION;
    
    if (needsResizing) {
      console.log(`Image needs resizing from ${width}x${height}`);
      
      // Calculate scale factor to keep largest dimension under 3840px
      const maxDimension = Math.max(width, height);
      const scaleFactor = Math.min(3840 / maxDimension, 1.0); // Never scale up
      
      const newWidth = Math.round(width * scaleFactor);
      const newHeight = Math.round(height * scaleFactor);
      
      console.log(`Will scale by ${(scaleFactor * 100).toFixed(1)}% to: ${newWidth}x${newHeight}`);
      
      // Create resized preview using proportional scaling (no distortion)
      const resizedPreviewBuffer = await sharp(imageBuffer)
        .rotate() // Auto-rotate based on EXIF orientation
        .resize(newWidth, newHeight, {
          fit: 'inside', // Scale to fit inside dimensions, maintaining aspect ratio
          kernel: sharp.kernel.lanczos3,
          withoutEnlargement: true
        })
        .jpeg({ quality: 85, progressive: true })
        .toBuffer();
      
      // Upload original image for later use
      const originalFileName = `mortal-kombat/${user.id}/${Date.now()}-original-${imageFile.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
      const { data: originalUpload, error: originalUploadError } = await supabaseAdmin.storage
        .from('generated-images')
        .upload(originalFileName, imageBuffer, {
          contentType: imageFile.type,
          upsert: false
        });
      
      if (originalUploadError) {
        console.error('Original upload error:', originalUploadError);
        return NextResponse.json(
          { error: 'Failed to upload original image' },
          { status: 500 }
        );
      }
      
      // Upload resized preview
      const previewFileName = `mortal-kombat/${user.id}/${Date.now()}-preview-${imageFile.name.replace(/[^a-zA-Z0-9.-]/g, '_')}.jpg`;
      const { data: previewUpload, error: previewUploadError } = await supabaseAdmin.storage
        .from('generated-images')
        .upload(previewFileName, resizedPreviewBuffer, {
          contentType: 'image/jpeg',
          upsert: false
        });
      
      if (previewUploadError) {
        console.error('Preview upload error:', previewUploadError);
        return NextResponse.json(
          { error: 'Failed to upload preview image' },
          { status: 500 }
        );
      }
      
      // Get public URLs
      const { data: { publicUrl: originalUrl } } = supabaseAdmin.storage
        .from('generated-images')
        .getPublicUrl(originalFileName);
        
      const { data: { publicUrl: previewUrl } } = supabaseAdmin.storage
        .from('generated-images')
        .getPublicUrl(previewFileName);
      
      // Return image resize confirmation needed
      return NextResponse.json({
        requiresResizeConfirmation: true,
        originalImage: {
          url: originalUrl,
          width: width,
          height: height,
          size: imageFile.size
        },
        resizedPreview: {
          url: previewUrl,
          width: newWidth,
          height: newHeight,
          size: resizedPreviewBuffer.length
        },
        settings: settings,
        message: 'Image is larger than 4000 pixels and needs to be resized. Please review the preview.'
      });
    }

    // Process image normally for smaller images
    const { buffer, contentType, fileName: processedFileName } = await processImageForFAL(imageFile);

    // Upload image to Supabase storage with proper path
    const uploadFileName = `mortal-kombat/${user.id}/${Date.now()}-${processedFileName.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
    
    const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
      .from('generated-images')
      .upload(uploadFileName, buffer, {
        contentType: contentType,
        upsert: false
      });

    if (uploadError) {
      console.error('Upload error:', uploadError);
      return NextResponse.json(
        { error: 'Failed to upload image', details: uploadError.message, fileName: uploadFileName, bucket: 'generated-images' },
        { status: 500 }
      );
    }

    // Get public URL for the uploaded image
    const { data: { publicUrl } } = supabaseAdmin.storage
      .from('generated-images')
      .getPublicUrl(uploadFileName);

    // Create generation request record
    const { data: requestData, error: requestError } = await supabaseAdmin
      .from('web_generation_requests')
      .insert({
        user_id: user.id,
        wallet_id: wallet.id,
        flow_type: 'mortal_kombat',
        status: 'pending',
        current_step: 1,
        total_steps: 3,
        estimated_cost: estimatedCost,
        input_data: {
          character_type: settings.character_type,
          video_settings: settings.video_settings,
          uploaded_image_url: publicUrl,
          transformation_prompt: CHARACTER_PROMPTS[settings.character_type],
          image_generator: selectedImageGenerator
        }
      })
      .select()
      .single();

    if (requestError) {
      console.error('Request creation error:', requestError);
      return NextResponse.json(
        { error: 'Failed to create generation request' },
        { status: 500 }
      );
    }

    // Create first step record (image upload)
    const { error: stepError } = await supabaseAdmin
      .from('web_generation_steps')
      .insert({
        web_request_id: requestData.id,
        step_number: 1,
        step_type: 'image_upload',
        status: 'completed',
        input_data: {
          original_file_name: imageFile.name,
          file_size: imageFile.size,
          content_type: imageFile.type
        },
        output_data: {
          uploaded_image_url: publicUrl
        },
        started_at: new Date().toISOString(),
        completed_at: new Date().toISOString()
      });

    if (stepError) {
      console.error('Step creation error:', stepError);
      // Don't fail the request, just log the error
    }

    // Check required environment variables for transformation
    if (!process.env.FAL_API_KEY) {
      return NextResponse.json(
        { error: 'FAL API key not configured' },
        { status: 500 }
      );
    }

    // Check if OpenAI API key is required for GPT-Image-1
    if (selectedImageGenerator === 'gpt-image-1' && !process.env.OPENAI_API_KEY) {
      return NextResponse.json(
        { error: 'OpenAI API key not configured' },
        { status: 500 }
      );
    }

    // Automatically trigger transformation
    try {
      // Update request status to in_progress
      await supabaseAdmin
        .from('web_generation_requests')
        .update({ 
          status: 'in_progress',
          current_step: 2,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestData.id);

      // Create step record for transformation
      const { data: stepData, error: transformStepError } = await supabaseAdmin
        .from('web_generation_steps')
        .insert({
          web_request_id: requestData.id,
          step_number: 2,
          step_type: 'image_transform',
          status: 'in_progress',
          fal_model: getFalModelFromGenerator(selectedImageGenerator),
          requires_approval: true,
          input_data: {
            image_url: publicUrl,
            prompt: CHARACTER_PROMPTS[settings.character_type],
            character_type: settings.character_type,
            image_generator: selectedImageGenerator
          },
          started_at: new Date().toISOString()
        })
        .select()
        .single();

      if (transformStepError) {
        console.error('Transformation step creation error:', transformStepError);
        return NextResponse.json(
          { error: 'Failed to create transformation step' },
          { status: 500 }
        );
      }

      // Call the appropriate transformation function based on selected generator
      console.log('Calling transformation API with params:', {
        image_url: publicUrl,
        prompt: CHARACTER_PROMPTS[settings.character_type],
        character_type: settings.character_type,
        generator: selectedImageGenerator
      });

      let result;
      if (selectedImageGenerator === 'gpt-image-1') {
        result = await transformWithGPTImage1(publicUrl, CHARACTER_PROMPTS[settings.character_type]);
      } else if (selectedImageGenerator === 'fal-seededit-v3') {
        result = await transformWithSeedEdit(publicUrl, CHARACTER_PROMPTS[settings.character_type]);
      } else if (selectedImageGenerator === 'fal-flux-pro-kontext') {
        result = await transformWithFluxKontext(publicUrl, CHARACTER_PROMPTS[settings.character_type]);
      } else {
        throw new Error(`Unsupported image generator: ${selectedImageGenerator}`);
      }

      // Update transformation step with success
      await supabaseAdmin
        .from('web_generation_steps')
        .update({
          status: 'awaiting_approval',
          fal_request_id: result.requestId,
          output_data: {
            transformed_image_url: result.transformedImageUrl,
            original_response: result.originalResponse
          },
          completed_at: new Date().toISOString(),
          token_cost: result.userTokenCost
        })
        .eq('id', stepData.id);

      // Update main request with transformation result
      const updatedOutputData = {
        transformed_image_url: result.transformedImageUrl
      };

      await supabaseAdmin
        .from('web_generation_requests')
        .update({ 
          status: 'awaiting_approval',
          awaiting_approval_for_step: 2,
          output_data: updatedOutputData,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestData.id);

      // Create approval step record (now step 3, but renumbered as step 2)
      await supabaseAdmin
        .from('web_generation_steps')
        .insert({
          web_request_id: requestData.id,
          step_number: 3,
          step_type: 'user_approval',
          status: 'pending',
          requires_approval: true,
          input_data: {
            transformation_step_id: stepData.id,
            original_image_url: publicUrl,
            transformed_image_url: result.transformedImageUrl
          }
        });

      // After the transformedImageUrl is obtained and before returning the response
      const imageGenerationCost = 10; // 10 tokens
      if (wallet.balance < imageGenerationCost) {
        return NextResponse.json(
          { error: 'Insufficient token balance for image generation', required: imageGenerationCost, available: wallet.balance },
          { status: 402 }
        );
      }
      // Deduct tokens
      await supabaseAdmin
        .from('wallets')
        .update({ 
          balance: wallet.balance - imageGenerationCost,
          updated_at: new Date().toISOString()
        })
        .eq('id', wallet.id);
      // Create token transaction record
      await supabaseAdmin
        .from('token_transactions')
        .insert({
          wallet_id: wallet.id,
          amount: -imageGenerationCost,
          description: `Mortal Kombat image generation (${selectedImageGenerator})`,
          transaction_type: 'consumption'
        });

      // Before inserting into web_images
      console.log('Inserting into web_images:', {
        user_id: user.id,
        wallet_id: wallet.id,
        prompt_text: CHARACTER_PROMPTS[settings.character_type],
        result_image_url: result.transformedImageUrl,
        created_at: new Date().toISOString(),
        token_cost: 10
      });
      const { error: webImagesInsertError } = await supabaseAdmin
        .from('web_images')
        .insert({
          user_id: user.id,
          wallet_id: wallet.id,
          prompt_text: CHARACTER_PROMPTS[settings.character_type],
          result_image_url: result.transformedImageUrl,
          created_at: new Date().toISOString(),
          token_cost: 10
        });
      if (webImagesInsertError) {
        console.error('Error inserting into web_images:', webImagesInsertError);
      } else {
        console.log('Successfully inserted image into web_images');
      }

      return NextResponse.json({
        request_id: requestData.id,
        status: 'awaiting_approval',
        current_step: 2,
        transformation: {
          original_image_url: publicUrl,
          transformed_image_url: result.transformedImageUrl,
          character_type: settings.character_type,
          prompt_used: CHARACTER_PROMPTS[settings.character_type],
          image_generator: selectedImageGenerator
        },
        estimated_total_cost: estimatedCost,
        estimated_tokens: requiredTokens,
        step_cost: COST_ESTIMATES.image_transform,
        step_tokens: result.userTokenCost,
        next_action: 'approve_transformation'
      });

    } catch (transformError: any) {
      if (transformError?.response || transformError?.body || transformError?.status) {
        console.error(`${selectedImageGenerator} transformation error:`, {
          message: transformError.message,
          status: transformError.status,
          body: transformError.body,
          response: transformError.response,
          stack: transformError.stack
        });
      }
      console.error('Automatic transformation error:', transformError);
      console.error('Error details:', {
        message: transformError.message,
        status: transformError.status,
        body: transformError.body,
        stack: transformError.stack
      });

      const errorMessage = transformError.message || transformError.toString();
      const errorDetails = transformError.body || transformError.status || 'Unknown error';

      // Update the request and step with failure
      await supabaseAdmin
        .from('web_generation_requests')
        .update({ 
          status: 'failed',
          error_message: `Transformation failed (${selectedImageGenerator}): ${errorMessage}`,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestData.id);

      return NextResponse.json(
        { 
          error: 'Automatic image transformation failed', 
          details: errorMessage,
          error_body: errorDetails,
          request_id: requestData.id,
          image_generator: selectedImageGenerator,
          can_retry: false
        },
        { status: 500 }
      );
    }

  } catch (error: any) {
    console.error('Start flow error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to start generation flow', 
        details: error.message || error.toString() 
      },
      { status: 500 }
    );
  }
} 