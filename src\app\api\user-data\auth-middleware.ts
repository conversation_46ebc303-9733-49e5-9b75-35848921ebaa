import { NextRequest, NextResponse } from 'next/server';

// A simple middleware to authenticate API requests
export function authenticateApiRequest(
  request: NextRequest,
  handler: (request: NextRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  // Get the API key from environment variable (should be set in .env.local)
  const validApiKey = process.env.BOT_API_KEY;
  
  // If no API key is configured, default to a development key
  if (!validApiKey) {
    console.warn('BOT_API_KEY environment variable is not set - using insecure default');
  }
  
  // Get API key from request header
  const apiKey = request.headers.get('x-api-key');
  
  // If no API key is provided or it doesn't match, return 401 Unauthorized
  if (!apiKey || (validApiKey && apiKey !== validApiKey)) {
    return Promise.resolve(
      NextResponse.json(
        { error: 'Unauthorized - Invalid or missing API key' },
        { status: 401 }
      )
    );
  }
  
  // If API key is valid, proceed to the actual handler
  return handler(request);
} 