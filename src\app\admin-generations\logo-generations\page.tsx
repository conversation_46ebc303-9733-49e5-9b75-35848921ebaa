"use client";

import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store';
import { useRouter } from 'next/navigation';
import { supabase } from '../../../lib/supabaseClient';
import Button from 'react-bootstrap/Button';
import Table from 'react-bootstrap/Table';
import Modal from 'react-bootstrap/Modal';
import Badge from 'react-bootstrap/Badge';
import styles from '../../admin/admin.module.css';

interface LogoGeneration {
  id: string;
  user_id: string;
  prompt: string;
  style: string;
  image_url: string;
  fal_request_id: string;
  model_used: string;
  token_cost: number;
  status: string;
  created_at: string;
  updated_at: string;
}

export default function LogoGenerationsAdmin() {
  const router = useRouter();
  const { user } = useSelector((state: RootState) => state.user);
  const [logoGenerations, setLogoGenerations] = useState<LogoGeneration[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLogo, setSelectedLogo] = useState<LogoGeneration | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const pageSize = 20;

  // Redirect non-admin users
  useEffect(() => {
    if (user && user.role !== 'admin') {
      router.push('/');
    }
  }, [user, router]);

  useEffect(() => {
    if (user?.role === 'admin') {
      fetchLogoGenerations();
    }
  }, [user, currentPage]);

  const fetchLogoGenerations = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) return;

      const response = await fetch('/api/admin/logo-generations', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setLogoGenerations(data.logoGenerations || []);
        setTotalPages(Math.ceil((data.total || 0) / pageSize));
      }
    } catch (error) {
      console.error('Error fetching logo generations:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewLogo = (logo: LogoGeneration) => {
    setSelectedLogo(logo);
    setShowModal(true);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge bg="success">Completed</Badge>;
      case 'pending':
        return <Badge bg="warning">Pending</Badge>;
      case 'failed':
        return <Badge bg="danger">Failed</Badge>;
      default:
        return <Badge bg="secondary">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatStyle = (style: string) => {
    const styleMap: Record<string, string> = {
      'minimalist': 'Minimalist',
      'black_white': 'Black & White',
      'modern': 'Modern',
      'vintage': 'Vintage',
      'colorful': 'Colorful',
      'corporate': 'Corporate'
    };
    return styleMap[style] || style;
  };

  if (user?.role !== 'admin') {
    return <div>Access denied</div>;
  }

  return (
    <div className={styles.adminContainer}>
      <div className={styles.header}>
        <h1>Logo Generations</h1>
        <p>Manage and view all logo generations</p>
      </div>

      {loading ? (
        <div className={styles.loading}>Loading...</div>
      ) : (
        <>
          <div className={styles.tableContainer}>
            <Table striped bordered hover responsive>
              <thead>
                <tr>
                  <th>ID</th>
                  <th>User ID</th>
                  <th>Prompt</th>
                  <th>Style</th>
                  <th>Status</th>
                  <th>Cost</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {logoGenerations.map((logo) => (
                  <tr key={logo.id}>
                    <td className={styles.idColumn}>{logo.id.slice(0, 8)}...</td>
                    <td className={styles.idColumn}>{logo.user_id.slice(0, 8)}...</td>
                    <td className={styles.promptColumn}>
                      {logo.prompt.length > 50 
                        ? `${logo.prompt.slice(0, 50)}...`
                        : logo.prompt
                      }
                    </td>
                    <td>{formatStyle(logo.style)}</td>
                    <td>{getStatusBadge(logo.status)}</td>
                    <td>{logo.token_cost} tokens</td>
                    <td>{formatDate(logo.created_at)}</td>
                    <td>
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={() => handleViewLogo(logo)}
                      >
                        View
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>

          {totalPages > 1 && (
            <div className={styles.pagination}>
              <Button
                variant="outline-primary"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}
              >
                Previous
              </Button>
              <span className={styles.pageInfo}>
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline-primary"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(currentPage + 1)}
              >
                Next
              </Button>
            </div>
          )}

          <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
            <Modal.Header closeButton>
              <Modal.Title>Logo Generation Details</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              {selectedLogo && (
                <div className={styles.logoDetails}>
                  <div className={styles.logoInfo}>
                    <h5>Generation Information</h5>
                    <p><strong>ID:</strong> {selectedLogo.id}</p>
                    <p><strong>User ID:</strong> {selectedLogo.user_id}</p>
                    <p><strong>Prompt:</strong> {selectedLogo.prompt}</p>
                    <p><strong>Style:</strong> {formatStyle(selectedLogo.style)}</p>
                    <p><strong>Status:</strong> {getStatusBadge(selectedLogo.status)}</p>
                    <p><strong>Token Cost:</strong> {selectedLogo.token_cost} tokens</p>
                    <p><strong>Model Used:</strong> {selectedLogo.model_used}</p>
                    <p><strong>FAL Request ID:</strong> {selectedLogo.fal_request_id}</p>
                    <p><strong>Created:</strong> {formatDate(selectedLogo.created_at)}</p>
                    <p><strong>Updated:</strong> {formatDate(selectedLogo.updated_at)}</p>
                  </div>
                  
                  <div className={styles.logoImage}>
                    <h5>Generated Logo</h5>
                    <img 
                      src={selectedLogo.image_url} 
                      alt="Generated Logo" 
                      className={styles.modalImage}
                    />
                    <div className={styles.imageActions}>
                      <Button
                        variant="primary"
                        onClick={() => window.open(selectedLogo.image_url, '_blank')}
                      >
                        Open Full Size
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={() => setShowModal(false)}>
                Close
              </Button>
            </Modal.Footer>
          </Modal>
        </>
      )}
    </div>
  );
} 