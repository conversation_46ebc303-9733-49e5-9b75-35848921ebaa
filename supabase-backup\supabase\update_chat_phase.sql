-- Create or replace the function that checks the chat replies and updates the phase if necessary.
CREATE OR REPLACE FUNCTION update_chat_phase_if_conditions_met()
RETURNS trigger AS $$
BEGIN
  -- Only consider chats in phase 1.
  IF (SELECT phase FROM chats WHERE id = NEW.chat_id) = 1 THEN
    -- Check if there's at least one reply with non-empty text
    -- and at least one reply with a non-null image_url.
    IF EXISTS (
         SELECT 1
         FROM chat_replies 
         WHERE chat_id = NEW.chat_id 
           AND message IS NOT NULL 
           AND TRIM(message) <> ''
       )
       AND EXISTS (
         SELECT 1
         FROM chat_replies 
         WHERE chat_id = NEW.chat_id 
           AND image_url IS NOT NULL
       ) THEN
         -- Upgrade chat phase to 2.
         UPDATE chats 
         SET phase = 2 
         WHERE id = NEW.chat_id;
         -- Insert a system reply notifying the user.
         INSERT INTO chat_replies (chat_id, sender_role, message)
         VALUES (NEW.chat_id, 'system', 'Thank you for telling us you require for your video. We will process your request and get back to you');
    END IF;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Remove any existing trigger to avoid duplicates.
DROP TRIGGER IF EXISTS trg_update_chat_phase ON chat_replies;

-- Create a trigger that fires AFTER INSERT on chat_replies.
CREATE TRIGGER trg_update_chat_phase
AFTER INSERT ON chat_replies
FOR EACH ROW
EXECUTE FUNCTION update_chat_phase_if_conditions_met(); 