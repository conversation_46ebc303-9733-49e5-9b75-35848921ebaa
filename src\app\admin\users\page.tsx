"use client";

import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { supabase } from "@/lib/supabaseClient";
import { fetchUserProfile } from "@/store/userSlice";
import { AppDispatch } from "@/store";
import styles from "../admin.module.css";

interface UserRow {
  id: string;
  email: string;
  telegram_id: string | null;
  telegram_username: string | null;
  whatsapp_id: string | null;
  whatsapp_name: string | null;
  tokens: number;
  message_count: number;
  last_active: string | null;
  created_at: string;
  is_admin: boolean;
  is_bot_user: boolean;
  raw_user_meta_data: any;
}

export default function AdminUsersPage() {
  const dispatch = useDispatch<AppDispatch>();
  const [users, setUsers] = useState<UserRow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showTopUpModal, setShowTopUpModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserRow | null>(null);
  const [topUpAmount, setTopUpAmount] = useState(0);
  const [topUpLoading, setTopUpLoading] = useState(false);
  const [topUpError, setTopUpError] = useState<string | null>(null);
  const [topUpSuccess, setTopUpSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    setError(null);
    try {
      // Get the current session to send the authorization header
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.access_token) {
        throw new Error("No authentication session found");
      }

      // Call the new API endpoint
      const response = await fetch('/api/admin/users', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch users');
      }

      const data = await response.json();
      
      // Process the data to format dates and add display fields
      const processedUsers = data.users.map((user: UserRow) => ({
        ...user,
        // Format the last_active date
        last_active: user.last_active 
          ? new Date(user.last_active).toLocaleString() 
          : "Never"
      }));

      setUsers(processedUsers);
    } catch (err: any) {
      console.error("Error fetching users:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenTopUpModal = (user: UserRow) => {
    setSelectedUser(user);
    setTopUpAmount(0);
    setTopUpError(null);
    setTopUpSuccess(null);
    setShowTopUpModal(true);
  };

  const handleCloseTopUpModal = () => {
    setShowTopUpModal(false);
    setSelectedUser(null);
    setTopUpAmount(0);
    setTopUpError(null);
    setTopUpSuccess(null);
  };

  const handleTopUp = async () => {
    if (!selectedUser || topUpAmount <= 0) {
      setTopUpError("Please enter a valid amount.");
      return;
    }
    setTopUpLoading(true);
    setTopUpError(null);
    setTopUpSuccess(null);

    try {
      // Determine which ID to use for the API call
      const requestBody: any = {
        tokensToAdd: topUpAmount,
        reason: `Admin top-up for ${selectedUser.email || selectedUser.telegram_id || selectedUser.whatsapp_id || 'Unknown'}`
      };

      // For the new API structure, we always use the user's id
      // If it's a bot user, the id is the external_identity_id
      // If it's a web user, the id is the auth.users id
      if (selectedUser.is_bot_user) {
        requestBody.externalIdentityId = selectedUser.id;
      } else {
        requestBody.userId = selectedUser.id;
      }

      const response = await fetch('/api/add-tokens', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (response.ok) {
        setTopUpSuccess(`Successfully added ${data.tokensAdded} tokens! New balance: ${data.newBalance}`);
        // Refresh the users list to show updated balance
        await fetchUsers();
        
        // If admin is topping up their own account, refresh their profile in Redux
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user?.id === selectedUser.id && !selectedUser.is_bot_user) {
          dispatch(fetchUserProfile());
        }
      } else {
        setTopUpError(data.error || 'Failed to add tokens');
      }
    } catch (error: any) {
      console.error('Top-up error:', error);
      setTopUpError('Network error. Please try again.');
    } finally {
      setTopUpLoading(false);
    }
  };

  return (
    <div>
      <h2>All Users</h2>
      {loading ? (
        <p>Loading...</p>
      ) : error ? (
        <p className={styles.errorText}>Error: {error}</p>
      ) : (
        <div>
          <p>Total Users: {users.length}</p>
          <table className={styles.table}>
            <thead>
              <tr>
                <th>User</th>
                <th>Platform</th>
                <th>Balance</th>
                <th>Messages</th>
                <th>Last Active</th>
                <th>Top Up</th>
              </tr>
            </thead>
            <tbody>
              {users.map((user) => (
                <tr key={user.id} className={styles.clickableRow}>
                  <td>
                    {user.email || user.telegram_username || user.whatsapp_name || user.telegram_id || user.whatsapp_id || "Unknown"}
                  </td>
                  <td>
                    <span className={`${styles.platformBadge} ${
                      user.telegram_id ? styles.telegram : 
                      user.whatsapp_id ? styles.whatsapp : 
                      styles.web
                    }`}>
                      {user.telegram_id ? 'Telegram' : 
                       user.whatsapp_id ? 'WhatsApp' : 
                       'Web'}
                    </span>
                  </td>
                  <td>{user.tokens}</td>
                  <td>{user.message_count}</td>
                  <td>{user.last_active}</td>
                  <td>
                    <button
                      className={styles.refreshButton}
                      onClick={() => handleOpenTopUpModal(user)}
                    >
                      Top up account
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Top Up Modal */}
          {showTopUpModal && selectedUser && (
            <div className={styles.imageModalOverlay}>
              <div className={styles.imageModal} style={{ minWidth: 400, maxWidth: 420, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                <button
                  className={styles.closeModalButton}
                  onClick={handleCloseTopUpModal}
                >
                  ×
                </button>
                <div className={styles.modalDetails} style={{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center', padding: '2rem 1.5rem' }}>
                  <h3 style={{ marginBottom: 24, fontSize: '1.6rem', textAlign: 'center' }}>Top up account for user</h3>
                  <p style={{ marginBottom: 24, fontSize: '1.1rem', textAlign: 'center' }}>
                    <strong>User:</strong> {selectedUser.email || selectedUser.telegram_username || selectedUser.whatsapp_name || selectedUser.telegram_id || selectedUser.whatsapp_id || "Unknown"}
                  </p>
                  <label style={{ width: '100%', textAlign: 'center', marginBottom: 24, fontSize: '1.1rem' }}>
                    Credits to add:
                    <input
                      type="number"
                      min={1}
                      value={topUpAmount}
                      onChange={e => setTopUpAmount(Number(e.target.value))}
                      style={{
                        marginLeft: 12,
                        width: 120,
                        padding: '0.6rem 1rem',
                        borderRadius: 8,
                        border: '1px solid #d1d5db',
                        fontSize: '1.1rem',
                        outline: 'none',
                        boxShadow: '0 1px 2px rgba(0,0,0,0.04)',
                        marginTop: 8,
                        background: '#f9f9fb',
                        transition: 'border 0.2s',
                        textAlign: 'center',
                      }}
                      onFocus={e => e.currentTarget.style.border = '1.5px solid #0070f3'}
                      onBlur={e => e.currentTarget.style.border = '1px solid #d1d5db'}
                    />
                  </label>
                  <div style={{ display: 'flex', justifyContent: 'center', gap: 16, width: '100%', marginTop: 8 }}>
                    <button
                      className={styles.refreshButton}
                      style={{
                        background: '#0070f3',
                        color: 'white',
                        borderRadius: 8,
                        fontWeight: 600,
                        fontSize: '1.1rem',
                        padding: '0.7rem 1.5rem',
                        border: 'none',
                        boxShadow: '0 2px 8px rgba(0,112,243,0.08)',
                        cursor: topUpLoading ? 'not-allowed' : 'pointer',
                        opacity: topUpLoading ? 0.7 : 1,
                        transition: 'background 0.2s',
                      }}
                      onClick={handleTopUp}
                      disabled={topUpLoading}
                    >
                      {topUpLoading ? "Adding..." : "Add credits"}
                    </button>
                    <button
                      className={styles.refreshButton}
                      style={{
                        background: '#f3f4f6',
                        color: '#333',
                        borderRadius: 8,
                        fontWeight: 500,
                        fontSize: '1.1rem',
                        padding: '0.7rem 1.5rem',
                        border: 'none',
                        boxShadow: '0 1px 3px rgba(0,0,0,0.04)',
                        cursor: topUpLoading ? 'not-allowed' : 'pointer',
                        opacity: topUpLoading ? 0.7 : 1,
                        transition: 'background 0.2s',
                      }}
                      onClick={handleCloseTopUpModal}
                      disabled={topUpLoading}
                    >
                      Cancel
                    </button>
                  </div>
                  {topUpError && <p className={styles.errorText} style={{ marginTop: 18 }}>{topUpError}</p>}
                  {topUpSuccess && <p style={{ color: 'green', marginTop: 18 }}>{topUpSuccess}</p>}
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
} 