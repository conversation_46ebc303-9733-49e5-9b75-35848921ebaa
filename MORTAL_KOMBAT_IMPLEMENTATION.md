# Mortal Kombat AI Video Generator - Implementation Guide

## 🎯 Overview

A complete multi-step AI flow that transforms user photos into Mortal Kombat characters and generates transition videos using FAL APIs (GPT-Image-1 and PixVerse).

## 🏗️ Architecture

### **Multi-Step Approval Flow**
1. **Image Upload** → User uploads photo + selects character/settings
2. **Generate Transformation** → AI transforms photo to MK character (GPT-Image-1)
3. **User Approval** → User approves/rejects transformation (cost control gate)
4. **Generate Video** → Creates transition video from original to transformed (PixVerse)

### **Database Schema**

```sql
-- Main requests table for any AI flow type
web_generation_requests (
  id, user_id, wallet_id, flow_type, status, current_step,
  awaiting_approval_for_step, approved_steps, rejected_steps,
  estimated_cost, actual_cost, input_data, output_data
)

-- Individual steps within each flow
web_generation_steps (
  id, web_request_id, step_number, step_type, status,
  fal_model, fal_request_id, requires_approval, token_cost,
  input_data, output_data, approved_at, rejection_reason
)

-- Video outputs
web_videos (
  id, user_id, wallet_id, web_request_id, video_url,
  duration, resolution, fal_model, token_cost
)
```

## 🔗 API Endpoints

### **Start Flow**
```
POST /api/web-generations/mortal-kombat/start
- Uploads image to Supabase storage
- Creates generation request record
- Validates token balance
- Returns request_id for subsequent calls
```

### **Generate Transformation**
```
POST /api/web-generations/[id]/generate-transformation
- Calls fal-ai/gpt-image-1/edit-image/byok
- Uses character-specific prompts
- Updates status to 'awaiting_approval'
- Requires OpenAI API key
```

### **Approve/Reject Transformation**
```
POST /api/web-generations/[id]/approve-transformation
- Handles user approval/rejection
- Deducts transformation cost (4 tokens) on approval
- Creates token transaction records
- Updates approval step status
```

### **Generate Video**
```
POST /api/web-generations/[id]/generate-video
- Calls fal-ai/pixverse/v4.5/image-to-video
- Deducts video cost (30-80 tokens based on resolution)
- Creates video record
- Handles automatic refunds on failure
```

### **Status Polling**
```
GET /api/web-generations/[id]
- Returns complete generation status
- Includes step details and progress
- Provides next_action guidance
```

## ⚔️ Character Options

| Character | Description | Transformation Prompt |
|-----------|-------------|----------------------|
| Scorpion  | Fire ninja  | Yellow/black outfit, skull mask, fire effects |
| Sub-Zero  | Ice warrior | Blue/silver armor, ice effects, frost |
| Raiden    | Thunder god | White/blue robes, lightning effects |
| Liu Kang  | Dragon monk | Red/black attire, dragon fire effects |
| Kitana    | Princess    | Blue/silver royal outfit, fan weapons |

## 💰 Cost Structure

| Operation | Tokens | USD | Notes |
|-----------|---------|-----|-------|
| Image Transformation | 4 | $0.04 | GPT-Image-1 cost |
| Video 360p/540p | 30 | $0.30 | PixVerse standard |
| Video 720p | 40 | $0.40 | Recommended quality |
| Video 1080p | 80 | $0.80 | Premium quality |

## 🎨 Frontend Features

### **Multi-Step UI**
- **Step 1**: Character selection, video settings, image upload
- **Step 2**: Transformation generation with progress
- **Step 3**: Side-by-side approval interface
- **Step 4**: Video generation with real-time polling
- **Step 5**: Final results with download options

### **User Experience**
- Real-time status polling during video generation
- Image previews at each step
- Cost transparency before each action
- Error handling with user-friendly messages
- Responsive design for mobile/desktop

## 🔧 Environment Variables Required

```env
# FAL AI
FAL_API_KEY=your_fal_api_key

# OpenAI (for GPT-Image-1)
OPENAI_API_KEY=your_openai_api_key

# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## 🚀 Usage Flow

1. **Access**: Navigate to `/mortal-kombat-video` (link in header)
2. **Configure**: Select character, video duration/resolution
3. **Upload**: Add your photo for transformation
4. **Review Cost**: See estimated token cost before starting
5. **Generate**: AI transforms your photo to MK character
6. **Approve**: Review and approve/reject transformation
7. **Create Video**: Generate transition video (3-5 minutes)
8. **Download**: Save final video with all assets

## 🔄 Scalability

The system is designed for multiple AI flows:
- Generic `web_generation_requests` table supports any flow type
- Modular step system allows different approval patterns
- Can easily add "babify", "anime-style", or other transformation flows
- Separation of web vs bot generations for multi-platform support

## 📊 Monitoring & Analytics

- All generations tracked in database with costs
- Failed operations automatically refund tokens
- Step-by-step tracking for debugging
- User approval/rejection patterns for optimization

## 🛡️ Security & Error Handling

- Row-level security (RLS) on all tables
- Automatic token refunds on API failures
- Input validation and file type checking
- Rate limiting and balance verification
- Graceful error messages for users

## 🎯 Next Steps

1. **Test the complete flow** with real images
2. **Add more character options** based on user feedback
3. **Implement retry mechanisms** for failed generations
4. **Add video quality previews** before final generation
5. **Create analytics dashboard** for admin monitoring

---

**Ready to Battle!** The Mortal Kombat AI video generator is fully implemented and ready for epic transformations! ⚔️🔥❄️⚡ 