.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  font-family: system-ui, -apple-system, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eaeaea;
}

.header h1 {
  font-size: 1.8rem;
  margin: 0;
  color: #333;
}

.backLink {
  color: #0070f3;
  text-decoration: none;
  font-weight: 500;
}

.backLink:hover {
  text-decoration: underline;
}

.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.instructions {
  background-color: #f9f9f9;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #0070f3;
}

.instructions h2 {
  margin-top: 0;
  color: #333;
}

.testCases {
  margin-top: 1.5rem;
}

.testCases h3 {
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

.testCases ul {
  padding-left: 1.5rem;
}

.testCases li {
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.testForm {
  background-color: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.testForm h2 {
  margin-top: 0;
  color: #333;
  margin-bottom: 1.5rem;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.formGroup input {
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.submitButton {
  background-color: #0070f3;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.submitButton:hover {
  background-color: #0051a8;
}

.submitButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.success, .error {
  margin-top: 1.5rem;
  padding: 1rem;
  border-radius: 4px;
}

.success {
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #2e7d32;
}

.error {
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  color: #d32f2f;
}

@media (max-width: 768px) {
  .content {
    grid-template-columns: 1fr;
  }
} 