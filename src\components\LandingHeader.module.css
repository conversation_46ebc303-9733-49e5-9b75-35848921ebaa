.header {
  position: sticky;
  top: 0;
  width: 100%;
  background-color: #333333;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  z-index: 100;
  padding: 0.25rem 0;
  /* background:red !important; */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  /* max-width: 50px; */
}

.logo a {
  display: flex;
  align-items: center;
  /* width: 30px; */
}

.navigation {
  flex: 1;
  display: flex;
  justify-content: center;
}

.navList {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2.5rem;
}

.navItem {
  position: relative;
}

.navLink {
  color: #ffffff;
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  transition: color 0.2s ease;
  padding: 0.5rem 0;
  position: relative;
}

.navLink:hover {
  color: #0070f3;
}

.navLink::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: #0070f3;
  transition: width 0.3s ease;
}

.navLink:hover::after {
  width: 100%;
}

.authButtons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.loginButton {
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.1);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.loginButton:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.signupButton {
  background-color: #0070f3;
  color: white;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  border: 1px solid #0070f3;
}

.signupButton:hover {
  background-color: #005bb5;
  border-color: #005bb5;
}

/* Hamburger Menu */
.hamburger {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 200;
}

.hamburgerLine {
  display: block;
  width: 100%;
  height: 3px;
  background-color: #ffffff;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.hamburger.active .hamburgerLine:nth-child(1) {
  transform: translateY(9px) rotate(45deg);
}

.hamburger.active .hamburgerLine:nth-child(2) {
  opacity: 0;
}

.hamburger.active .hamburgerLine:nth-child(3) {
  transform: translateY(-9px) rotate(-45deg);
}

/* Responsive styles */
@media (max-width: 992px) {
  .container {
    position: relative;
  }
  
  .hamburger {
    display: flex;
    order: 3;
  }
  
  .navigation {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #333333;
    z-index: 150;
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none;
    opacity: 0;
    transform: translateY(-100%);
    transition: all 0.3s ease;
  }
  
  .navigation.menuOpen {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
  }
  
  .navList {
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }
  
  .navItem {
    text-align: center;
  }
  
  .navLink {
    font-size: 1.5rem;
    padding: 0.5rem 1rem;
  }
  
  .authButtons {
    position: fixed;
    bottom: 15%;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    opacity: 0;
    pointer-events: none;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 150;
  }
  
  .authButtons.menuOpen {
    opacity: 1;
    pointer-events: auto;
    transform: translateY(0);
  }
  
  .loginButton, 
  .signupButton {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
  }
}

/* Mobile styles - additional tweaks for very small screens */
@media (max-width: 768px) {
  .container {
    padding: 0.5rem 1rem;
  }
  
  .navLink {
    font-size: 1.25rem;
  }
  
  .loginButton, 
  .signupButton {
    padding: 0.6rem 1.25rem;
    font-size: 1rem;
  }
} 