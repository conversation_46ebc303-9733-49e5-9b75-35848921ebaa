import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// POST /api/login

export async function POST(request: Request) {
    try {
      const data = await request.json();
      const { email, password } = data;

      // Create data object to store
      const userData = {
        email,
        password,
        timestamp: new Date().toISOString()
      };

      // Define path for users.json file
      const filePath = path.join(process.cwd(), 'data', 'users.json');
      
      // Create directory if it doesn't exist
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)){
        fs.mkdirSync(dir, { recursive: true });
      }

      // Read existing users or create empty array
      let users = [];
      if (fs.existsSync(filePath)) {
        const fileContent = fs.readFileSync(filePath, 'utf8').trim();
        if (fileContent) {
          try {
            users = JSON.parse(fileContent);
          } catch (err) {
            console.error("Error parsing JSO<PERSON> from file:", err);
            users = [];
          }
        }
      }

      // Add new user
      users.push(userData);

      // Write back to file
      fs.writeFileSync(filePath, JSON.stringify(users, null, 2));

      return NextResponse.json({ success: true, message: 'User data stored successfully' });
    } catch (error: any) {
      return NextResponse.json({ success: false, message: error.message }, { status: 500 });
    }
  } 