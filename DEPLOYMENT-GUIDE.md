# Deployment Guide for Aivis Web Application

This guide will walk you through deploying the Aivis web application on a VPS with DirectAdmin and LAMP stack.

## Prerequisites

1. A VPS with DirectAdmin and LAMP stack installed
2. Domain (aivis.ro) pointing to the VPS IP address
3. SSH access to the VPS
4. Node.js and npm installed on your local machine

## Deployment Options

There are two main approaches to deploy this Next.js application:

### Option 1: Static Export (Recommended for LAMP stack)

Since your server is running LAMP stack (Linux, Apache, MySQL, PHP), the easiest method is to use Next.js static export:

1. **Prepare for static export:**

   Modify your Next.js configuration to use static export by editing `next.config.ts`:

   ```typescript
   const nextConfig = {
     output: 'export',
     // Existing config options remain unchanged
   };

   export default nextConfig;
   ```

2. **Build the application locally:**

   ```bash
   # Install dependencies
   npm install

   # Build and export as static files
   npm run build
   ```

   This will generate a static version of your site in the `out` directory.

3. **Upload via DirectAdmin:**

   - Login to DirectAdmin control panel
   - Navigate to File Manager for your domain (aivis.ro)
   - Upload the contents of the `out` directory to the `public_html` folder

4. **Configure Apache (if needed):**

   You may need to create an `.htaccess` file in the root directory with:

   ```apache
   <IfModule mod_rewrite.c>
     RewriteEngine On
     RewriteBase /
     RewriteRule ^index\.html$ - [L]
     RewriteCond %{REQUEST_FILENAME} !-f
     RewriteCond %{REQUEST_FILENAME} !-d
     RewriteRule . /index.html [L]
   </IfModule>
   ```

### Option 2: Node.js Server (For better performance)

For a full-featured Next.js deployment, you'd need to install Node.js on your VPS. This is more complex but offers better performance:

1. **Install Node.js on your VPS:**

   SSH into your server and install Node.js:

   ```bash
   # Connect to your server
   ssh user@your-server-ip

   # Update system
   sudo apt update
   sudo apt upgrade -y

   # Install Node.js and npm
   curl -sL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt install -y nodejs
   ```

2. **Set up the application on the server:**

   ```bash
   # Create directory for your app
   mkdir -p /var/www/aivis

   # Navigate to the directory
   cd /var/www/aivis

   # Clone your repository or upload files
   git clone https://your-repository-url.git .
   # OR upload files manually and extract

   # Install dependencies
   npm install

   # Build the app
   npm run build
   ```

3. **Install PM2 for process management:**

   ```bash
   # Install PM2 globally
   sudo npm install pm2 -g

   # Start your Next.js app with PM2
   pm2 start npm --name "aivis" -- start

   # Make PM2 start on system reboot
   pm2 startup
   pm2 save
   ```

4. **Configure Apache as a reverse proxy:**

   Create a virtual host configuration in Apache:

   ```apache
   <VirtualHost *:80>
     ServerName aivis.ro
     ServerAlias www.aivis.ro

     ProxyPreserveHost On
     ProxyPass / http://localhost:3000/
     ProxyPassReverse / http://localhost:3000/

     ErrorLog ${APACHE_LOG_DIR}/aivis-error.log
     CustomLog ${APACHE_LOG_DIR}/aivis-access.log combined
   </VirtualHost>
   ```

   Enable required Apache modules:

   ```bash
   sudo a2enmod proxy
   sudo a2enmod proxy_http
   sudo systemctl restart apache2
   ```

## Environment Variables

For either deployment option, you'll need to set environment variables:

### For Static Export:

Environment variables must be built into the application at build time. Create a `.env` file locally:

```
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
MAILCHIMP_API_KEY=your-mailchimp-api-key
MAILCHIMP_SERVER_PREFIX=your-server-prefix
MAILCHIMP_LIST_ID=your-list-id
```

### For Node.js Server:

Environment variables can be set directly on the server, either in a `.env` file or through PM2:

```bash
# Using PM2 environment variables
pm2 restart aivis --update-env --env production
```

## SSL Configuration

To enable HTTPS (recommended):

1. Log in to DirectAdmin
2. Navigate to "SSL Certificates"
3. Choose "Let's Encrypt™ SSL" for free SSL certificates
4. Follow the prompts to install the certificate

## Troubleshooting

- **404 errors on page refresh with static export**: Make sure your `.htaccess` file is correctly configured for client-side routing
- **API endpoints not working**: Static exports don't support API routes; you'll need to use external API services
- **Permission issues**: Ensure proper file permissions (typically 755 for directories and 644 for files)
- **Database connection errors**: Check that your Supabase environment variables are correctly set

## Maintenance

### Updates

To update your application:

1. Make changes locally and test
2. Rebuild the application
3. Upload the new build to your server (replacing existing files)

### Monitoring

For Node.js deployments, use PM2's monitoring:

```bash
pm2 monit
pm2 logs aivis
```

### Backup

Regularly backup your application files and database:

```bash
# For DirectAdmin
# Use the Backup/Restore feature in the control panel
```

## Additional Resources

- [Next.js Static Export Documentation](https://nextjs.org/docs/pages/building-your-application/deploying/static-exports)
- [PM2 Documentation](https://pm2.keymetrics.io/docs/usage/quick-start/)
- [DirectAdmin Documentation](https://www.directadmin.com/features.php?id=343) 