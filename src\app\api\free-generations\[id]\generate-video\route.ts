import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { fal } from '@fal-ai/client';

// Create a Supabase client specifically for server-side use with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

// Configure the FAL client with the API key
fal.config({
  credentials: process.env.FAL_API_KEY || ''
});

interface VideoGenerationRequest {
  custom_video_prompt?: string;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: requestId } = await params;
    const body: VideoGenerationRequest = await request.json();

    // Get the generation request
    const { data: generationRequest, error: requestError } = await supabaseAdmin
      .from('web_generation_requests')
      .select('*')
      .eq('id', requestId)
      .is('user_id', null) // Ensure this is a free generation (user_id is null)
      .single();

    if (requestError || !generationRequest) {
      return NextResponse.json(
        { error: 'Free generation request not found' },
        { status: 404 }
      );
    }

    // Validate request state
    if (generationRequest.status !== 'awaiting_approval' && generationRequest.status !== 'approved') {
      return NextResponse.json(
        { error: 'Request must be approved before video generation' },
        { status: 400 }
      );
    }

    // Check if transformed image exists
    if (!generationRequest.output_data?.transformed_image_url) {
      return NextResponse.json(
        { error: 'Transformed image not found' },
        { status: 400 }
      );
    }

    // Check required environment variables
    if (!process.env.FAL_API_KEY) {
      return NextResponse.json(
        { error: 'FAL API key not configured' },
        { status: 500 }
      );
    }

    // Get video settings from input data
    const videoSettings = generationRequest.input_data.settings?.video_settings;
    if (!videoSettings) {
      return NextResponse.json(
        { error: 'Video settings not found' },
        { status: 400 }
      );
    }

    const resolution = videoSettings.resolution;
    const duration = videoSettings.duration || 5;

    // Update request status to in_progress
    await supabaseAdmin
      .from('web_generation_requests')
      .update({ 
        status: 'in_progress',
        current_step: 3,
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId);

    // Create video generation step
    const character = generationRequest.output_data?.character || 
                     generationRequest.input_data.settings?.character_type;
                     
    const videoPrompt = body.custom_video_prompt || 
      `Seamless transition from normal person to ${character} from Mortal Kombat with dramatic transformation effects, mystical energy, and fighting stance`;

    const { data: stepData, error: stepError } = await supabaseAdmin
      .from('web_generation_steps')
      .insert({
        web_request_id: requestId,
        step_number: 3,
        step_type: 'video_generate',
        status: 'in_progress',
        fal_model: 'fal-ai/pixverse/v4.5/transition',
        input_data: {
          first_image_url: generationRequest.input_data.original_image_url,
          last_image_url: generationRequest.output_data.transformed_image_url,
          prompt: videoPrompt,
          duration: duration,
          resolution: resolution,
          style: videoSettings.style
        },
        token_cost: 0, // Free generation
        started_at: new Date().toISOString()
      })
      .select()
      .single();

    if (stepError) {
      console.error('Video step creation error:', stepError);
      return NextResponse.json(
        { error: 'Failed to create video generation step' },
        { status: 500 }
      );
    }

    // Call FAL API for video generation using transition model (same as regular generation)
    try {
      const result = await fal.subscribe("fal-ai/pixverse/v4.5/transition", {
        input: {
          prompt: videoPrompt,
          first_image_url: generationRequest.input_data.original_image_url,
          last_image_url: generationRequest.output_data.transformed_image_url,
          duration: duration.toString(),
          resolution: resolution,
          ...(videoSettings.style && {
            style: videoSettings.style
          })
        },
        logs: true,
        onQueueUpdate: (update) => {
          if (update.status === "IN_PROGRESS") {
            console.log("Processing video:", update.logs?.map((log) => log.message).join('\n'));
          }
        },
      });

      if (!result.data || !result.data.video) {
        throw new Error('No video returned from API');
      }

      const videoUrl = result.data.video.url;

      // Update step with success
      await supabaseAdmin
        .from('web_generation_steps')
        .update({
          status: 'completed',
          fal_request_id: result.requestId,
          output_data: {
            video_url: videoUrl,
            original_response: result.data
          },
          completed_at: new Date().toISOString()
        })
        .eq('id', stepData.id);

      // Create video record
      await supabaseAdmin
        .from('web_videos')
        .insert({
          user_id: null, // Free generation has no user
          wallet_id: null, // Free generation has no wallet
          web_request_id: requestId,
          video_url: videoUrl,
          duration: duration,
          resolution: resolution,
          prompt_text: videoPrompt,
          parameters: {
            style: videoSettings.style,
            character_type: character
          },
          source_image_urls: [
            generationRequest.input_data.original_image_url,
            generationRequest.output_data.transformed_image_url
          ],
          fal_request_id: result.requestId,
          fal_model: 'fal-ai/pixverse/v4.5/transition',
          token_cost: 0 // Free generation
        });

      // Update main request to completed
      const finalOutputData = {
        ...generationRequest.output_data,
        video_url: videoUrl
      };

      await supabaseAdmin
        .from('web_generation_requests')
        .update({ 
          status: 'completed',
          current_step: 4,
          output_data: finalOutputData,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      return NextResponse.json({
        request_id: requestId,
        status: 'completed',
        current_step: 4,
        video: {
          url: videoUrl,
          duration: duration,
          resolution: resolution,
          prompt: videoPrompt
        },
        final_output: {
          original_image_url: generationRequest.input_data.original_image_url,
          transformed_image_url: generationRequest.output_data.transformed_image_url,
          video_url: videoUrl
        },
        total_cost: 0, // Free generation
        tokens_charged: 0, // Free generation
        remaining_balance: 0 // Free generation
      });

    } catch (falError: any) {
      console.error('FAL video generation error:', falError);

      // Update step with failure
      await supabaseAdmin
        .from('web_generation_steps')
        .update({
          status: 'failed',
          error_message: falError.message || falError.toString(),
          completed_at: new Date().toISOString()
        })
        .eq('id', stepData.id);

      // Update main request
      await supabaseAdmin
        .from('web_generation_requests')
        .update({ 
          status: 'failed',
          error_message: `Video generation failed: ${falError.message || falError.toString()}`,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      return NextResponse.json(
        { 
          error: 'Video generation failed', 
          details: falError.message || falError.toString(),
          request_id: requestId
        },
        { status: 500 }
      );
    }

  } catch (error: any) {
    console.error('Generate video error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to generate video', 
        details: error.message || error.toString() 
      },
      { status: 500 }
    );
  }
} 