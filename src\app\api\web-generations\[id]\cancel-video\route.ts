import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client specifically for server-side use with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: requestId } = await params;

    // Get user from auth header or cookie
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '') || 
                  request.cookies.get('supabase-auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user info
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    // Get the generation request
    const { data: generationRequest, error: requestError } = await supabaseAdmin
      .from('web_generation_requests')
      .select('*')
      .eq('id', requestId)
      .eq('user_id', user.id)
      .single();

    if (requestError || !generationRequest) {
      return NextResponse.json(
        { error: 'Generation request not found' },
        { status: 404 }
      );
    }

    // Validate request state - can only cancel if in_progress on step 3
    if (generationRequest.status !== 'in_progress' || generationRequest.current_step !== 3) {
      return NextResponse.json(
        { error: 'No video generation to cancel' },
        { status: 400 }
      );
    }

    // Get the video generation step to find token cost
    const { data: videoStep, error: videoStepError } = await supabaseAdmin
      .from('web_generation_steps')
      .select('*')
      .eq('web_request_id', requestId)
      .eq('step_type', 'video_generate')
      .eq('status', 'in_progress')
      .single();

    if (videoStep && !videoStepError) {
      // Mark video step as cancelled
      await supabaseAdmin
        .from('web_generation_steps')
        .update({
          status: 'cancelled',
          completed_at: new Date().toISOString()
        })
        .eq('id', videoStep.id);

      // Refund video tokens if they were charged
      if (videoStep.token_cost > 0) {
        const { data: wallet, error: walletError } = await supabaseAdmin
          .from('wallets')
          .select('*')
          .eq('id', generationRequest.wallet_id)
          .single();

        if (!walletError && wallet) {
          // Refund tokens
          await supabaseAdmin
            .from('wallets')
            .update({ 
              balance: wallet.balance + videoStep.token_cost,
              updated_at: new Date().toISOString()
            })
            .eq('id', wallet.id);

          // Create refund transaction record
          await supabaseAdmin
            .from('token_transactions')
            .insert({
              wallet_id: wallet.id,
              amount: videoStep.token_cost,
              description: `Refund: Cancelled Mortal Kombat video generation`,
              transaction_type: 'refund'
            });
        }
      }
    }

    // Reset request status back to awaiting approval for step 2 (so user can re-approve and start video generation)
    await supabaseAdmin
      .from('web_generation_requests')
      .update({ 
        status: 'awaiting_approval',
        current_step: 2,
        awaiting_approval_for_step: 2,
        actual_cost: (generationRequest.actual_cost || 0) - ((videoStep?.token_cost || 0) / 100),
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId);

    return NextResponse.json({
      request_id: requestId,
      status: 'awaiting_approval',
      current_step: 2,
      cancelled: true,
      tokens_refunded: videoStep?.token_cost || 0,
      next_action: 'approve_transformation'
    });

  } catch (error: any) {
    console.error('Cancel video error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to cancel video generation', 
        details: error.message || error.toString() 
      },
      { status: 500 }
    );
  }
} 