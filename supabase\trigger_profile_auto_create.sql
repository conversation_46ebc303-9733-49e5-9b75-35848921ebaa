-- Create the function that inserts a profile row when a new user is created.
CREATE OR REPLACE FUNCTION public.insert_user_profile()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  INSERT INTO profiles (user_id, tokens, role)
  VALUES (NEW.id, 0, 'user');  -- You may change 0 to an initial token balance (e.g. 10) if desired.
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Remove any existing trigger to avoid duplicates.
DROP TRIGGER IF EXISTS trigger_insert_user_profile ON auth.users;

-- Create a trigger that fires after a new user is inserted.
CREATE TRIGGER trigger_insert_user_profile
AFTER INSERT ON auth.users
FOR EACH ROW EXECUTE FUNCTION public.insert_user_profile(); 