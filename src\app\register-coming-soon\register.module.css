.container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f7f7f7;
}

.main {
  max-width: 500px;
  width: 100%;
  padding: 3rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  margin-bottom: 2rem;
}

.title {
  margin-bottom: 2rem;
  font-size: 2.2rem;
  color: #333;
  position: relative;
}

.comingSoonContainer {
  margin: 2rem 0;
  padding: 1.5rem;
  background-color: #f0f7ff;
  border-radius: 8px;
  border-left: 4px solid #0070f3;
}

.comingSoonBadge {
  display: inline-block;
  background-color: #f2994a;
  color: white;
  font-size: 1rem;
  padding: 0.4rem 1rem;
  border-radius: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
  text-transform: uppercase;
}

.message {
  color: #4a4a4a;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.message:last-child {
  margin-bottom: 0;
}

.backButton {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background-color: #0070f3;
  color: white;
  border-radius: 0.5rem;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
  margin-top: 1rem;
}

.backButton:hover {
  background-color: #0051a8;
}

@media (max-width: 600px) {
  .main {
    padding: 2rem;
    margin: 1rem;
  }
  
  .title {
    font-size: 1.8rem;
  }
  
  .message {
    font-size: 1rem;
  }
  
  .inputGroup {
    flex-direction: column;
  }
  
  .emailInput {
    border-bottom: 1px solid #e9ecef;
    border-radius: 0.5rem 0.5rem 0 0;
  }
  
  .subscribeButton {
    border-radius: 0 0 0.5rem 0.5rem;
  }
}

.subscribeContainer {
  width: 100%;
  margin: 1rem 0 2rem;
}

.subscribeTitle {
  font-size: 1.4rem;
  margin-bottom: 1.2rem;
  color: #333;
}

.subscribeForm {
  width: 100%;
}

.inputGroup {
  display: flex;
  margin-bottom: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
  overflow: hidden;
}

.emailInput {
  flex: 1;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  border: none;
  outline: none;
}

.subscribeButton {
  padding: 1rem 1.5rem;
  background-color: #0070f3;
  color: white;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.subscribeButton:hover {
  background-color: #0051a8;
}

.subscribeButton:disabled {
  background-color: #99c4f7;
  cursor: not-allowed;
}

.formDisclaimer {
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 1rem;
  text-align: center;
}

.disclaimerLink {
  color: #0070f3;
  text-decoration: none;
}

.disclaimerLink:hover {
  text-decoration: underline;
}

.successMessage {
  color: #4caf50;
  font-weight: 500;
  margin: 1rem 0;
  padding: 0.5rem;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: 0.5rem;
}

.errorMessage {
  color: #f44336;
  font-weight: 500;
  margin: 1rem 0;
  padding: 0.5rem;
  background-color: rgba(244, 67, 54, 0.1);
  border-radius: 0.5rem;
} 