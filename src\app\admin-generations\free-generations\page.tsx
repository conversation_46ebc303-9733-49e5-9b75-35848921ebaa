'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Spinner, Form } from 'react-bootstrap';
import styles from '../../admin/admin.module.css';

interface FreeGeneration {
  id: string;
  flow_type: string;
  status: string;
  created_at: string;
  updated_at: string;
  web_generation_request_id: string | null;
  link: string;
  web_generation: any;
  character: string | null;
  generation_status: string | null;
  has_generated: boolean;
}

export default function FreeGenerationsPage() {
  const [generatedLink, setGeneratedLink] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Table state
  const [freeGenerations, setFreeGenerations] = useState<FreeGeneration[]>([]);
  const [tableLoading, setTableLoading] = useState(true);
  const [tableError, setTableError] = useState<string | null>(null);

  useEffect(() => {
    fetchFreeGenerations();
  }, []);

  const fetchFreeGenerations = async () => {
    setTableLoading(true);
    setTableError(null);

    try {
      const response = await fetch('/api/admin/free-generations');
      const data = await response.json();

      if (response.ok) {
        setFreeGenerations(data.freeGenerations);
      } else {
        setTableError(data.error || 'Failed to fetch free generations');
      }
    } catch (err: any) {
      setTableError('An unexpected error occurred: ' + err.message);
    } finally {
      setTableLoading(false);
    }
  };

  const handleCreateLink = async () => {
    setIsLoading(true);
    setError(null);
    setGeneratedLink(null);

    try {
      const response = await fetch('/api/admin/free-generations/create', {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        setGeneratedLink(data.freeLink);
        // Refresh the table to show the new link
        fetchFreeGenerations();
      } else {
        setError(data.error || 'Failed to create link. Make sure you are logged in as an admin.');
      }
    } catch (err: any) {
      setError('An unexpected error occurred: ' + err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyLink = () => {
    if (generatedLink) {
      navigator.clipboard.writeText(generatedLink);
      // Optional: Show a temporary "Copied!" message
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const handleCopyGenerationLink = (link: string) => {
    navigator.clipboard.writeText(link);
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'new': return '#28a745'; // green
      case 'claimed': return '#ffc107'; // yellow
      case 'used': return '#6c757d'; // gray
      default: return '#17a2b8'; // blue
    }
  };

  return (
    <div className={styles.conversationsSection}>
      <h2>Create Free Mortal Kombat Generation Link</h2>
      <p>
        Click the button below to generate a unique, single-use link that allows a user to try the
        Mortal Kombat video generation flow for free, without needing an account.
      </p>

      <Card>
        <Card.Body>
          <div className="d-flex justify-content-center">
            <Button
              variant="primary"
              size="lg"
              onClick={handleCreateLink}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" />
                  <span className="ms-2">Generating...</span>
                </>
              ) : (
                'Generate New Free Link'
              )}
            </Button>
          </div>

          {error && (
            <Alert variant="danger" className="mt-4">
              <strong>Error:</strong> {error}
            </Alert>
          )}

          {generatedLink && (
            <div className="mt-4">
              <Alert variant="success">
                <Alert.Heading>Link Generated Successfully!</Alert.Heading>
                <p>Share this link with a user to grant them one free generation.</p>
                <hr />
                <Form.Group>
                  <Form.Control
                    type="text"
                    value={generatedLink}
                    readOnly
                    className="mb-2"
                  />
                  <Button variant="outline-success" onClick={handleCopyLink}>
                    Copy Link
                  </Button>
                </Form.Group>
              </Alert>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Free Generations Table */}
      <div className="mt-5">
        <h3>All Free Generations</h3>
        
        {tableLoading && <div className={styles.loading}>Loading free generations...</div>}
        
        {tableError && (
          <Alert variant="danger">
            <strong>Error:</strong> {tableError}
            <br />
            <Button variant="outline-danger" size="sm" onClick={fetchFreeGenerations} className="mt-2">
              Retry
            </Button>
          </Alert>
        )}

        {!tableLoading && !tableError && (
          <div className={styles.tableContainer}>
            <table className={styles.adminTable}>
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Status</th>
                  <th>Character</th>
                  <th>Generated Link</th>
                  <th>Created</th>
                  <th>Generation Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {freeGenerations.map((fg) => (
                  <tr key={fg.id}>
                    <td>
                      <small style={{ fontFamily: 'monospace' }}>
                        {fg.id.substring(0, 8)}...
                      </small>
                    </td>
                    <td>
                      <span 
                        style={{ 
                          padding: '2px 8px', 
                          borderRadius: '4px', 
                          backgroundColor: getStatusBadgeColor(fg.status),
                          color: 'white',
                          fontSize: '12px',
                          fontWeight: 'bold'
                        }}
                      >
                        {fg.status}
                      </span>
                    </td>
                    <td>
                      {fg.character ? (
                        <span style={{ textTransform: 'capitalize' }}>{fg.character}</span>
                      ) : (
                        <span style={{ color: '#999' }}>-</span>
                      )}
                    </td>
                    <td>
                      <div style={{ maxWidth: '200px', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        <small style={{ fontFamily: 'monospace' }}>
                          {fg.link}
                        </small>
                      </div>
                    </td>
                    <td>
                      <small>{formatDate(fg.created_at)}</small>
                    </td>
                    <td>
                      {fg.generation_status ? (
                        <span style={{ 
                          padding: '2px 6px', 
                          borderRadius: '3px', 
                          backgroundColor: fg.generation_status === 'completed' ? '#d4edda' : '#fff3cd',
                          color: fg.generation_status === 'completed' ? '#155724' : '#856404',
                          fontSize: '11px'
                        }}>
                          {fg.generation_status}
                        </span>
                      ) : (
                        <span style={{ color: '#999' }}>Not used</span>
                      )}
                    </td>
                    <td>
                      <div className={styles.actionButtons}>
                        <button 
                          onClick={() => handleCopyGenerationLink(fg.link)}
                          className={styles.actionButton}
                          title="Copy Link"
                        >
                          📋
                        </button>
                        <button 
                          onClick={() => navigator.clipboard.writeText(fg.id)}
                          className={styles.actionButton}
                          title="Copy ID"
                        >
                          🆔
                        </button>
                        {fg.web_generation_request_id && (
                          <button 
                            onClick={() => window.open(`/mk-free/${fg.id}`, '_blank')}
                            className={styles.actionButton}
                            title="View Generation"
                          >
                            👁️
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            {freeGenerations.length === 0 && (
              <div className={styles.emptyState}>
                <p>No free generations found.</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
} 