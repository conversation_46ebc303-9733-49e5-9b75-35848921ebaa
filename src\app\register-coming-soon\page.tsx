"use client";
import React, { useState, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import styles from './register.module.css';

export default function RegisterComingSoon() {
  const [submitting, setSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState<{ text: string; isError: boolean } | null>(null);
  const emailInputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!emailInputRef.current?.value) return;
    
    try {
      setSubmitting(true);
      setSubmitMessage(null);
      
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email: emailInputRef.current.value 
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'A apărut o eroare. Te rugăm să încerci din nou.');
      }
      
      setSubmitMessage({
        text: 'Mulțumim pentru abonare! Te vom notifica când platforma va fi disponibilă.',
        isError: false
      });
      
      // Reset the form
      e.currentTarget.reset();
      
    } catch (error: any) {
      setSubmitMessage({
        text: error.message || 'A apărut o eroare. Te rugăm să încerci din nou.',
        isError: true
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className={styles.container}>
      <main className={styles.main}>
        <div className={styles.logo}>
          <Image 
            src="/aivis-wide-small.png" 
            alt="Logo Aivis" 
            width={180} 
            height={50} 
          />
        </div>
        
        <h1 className={styles.title}>Înregistrare</h1>
        
        <div className={styles.comingSoonContainer}>
          <span className={styles.comingSoonBadge}>În curând</span>
          <p className={styles.message}>
            Momentan, serviciul nostru este în dezvoltare.
          </p>
          <p className={styles.message}>
            Vă invităm să vă abonați pentru a fi notificat când platforma va fi disponibilă.
          </p>
        </div>

        <div className={styles.subscribeContainer}>
          <h2 className={styles.subscribeTitle}>Abonează-te pentru notificări</h2>
          <form className={styles.subscribeForm} onSubmit={handleSubmit}>
            <div className={styles.inputGroup}>
              <input 
                type="email" 
                placeholder="Adresa ta de email" 
                required 
                className={styles.emailInput}
                ref={emailInputRef}
                disabled={submitting}
              />
              <button 
                type="submit" 
                className={styles.subscribeButton}
                disabled={submitting}
              >
                {submitting ? 'Se trimite...' : 'Abonează-te'}
              </button>
            </div>
            {submitMessage && (
              <p className={submitMessage.isError ? styles.errorMessage : styles.successMessage}>
                {submitMessage.text}
              </p>
            )}
            <p className={styles.formDisclaimer}>
              Prin abonare, ești de acord să primești notificări despre lansare și actualizări. 
              Nu facem spam și respectăm <Link href="/privacy" className={styles.disclaimerLink}>politica de confidențialitate</Link>.
            </p>
          </form>
        </div>
        
        <Link href="/" className={styles.backButton}>
          Înapoi la pagina principală
        </Link>
      </main>
    </div>
  );
} 