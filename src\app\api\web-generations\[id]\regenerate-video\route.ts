import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { fal } from '@fal-ai/client';

// Create a Supabase client specifically for server-side use with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

// Configure the FAL client with the API key
fal.config({
  credentials: process.env.FAL_API_KEY || ''
});

// Video cost mapping (in tokens) - 35 tokens for all resolutions to total 40 tokens with approval
const VIDEO_COSTS = {
  '360p': 35, // $0.35 = 35 tokens
  '540p': 35, // $0.35 = 35 tokens
  '720p': 35, // $0.35 = 35 tokens
  '1080p': 35 // $0.35 = 35 tokens
};

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: requestId } = await params;
    
    // Get user from auth header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user info
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    console.log('Regenerate video - Request ID:', requestId, 'User ID:', user.id);

    // Get the generation request
    const { data: generationRequest, error: requestError } = await supabaseAdmin
      .from('web_generation_requests')
      .select('*')
      .eq('id', requestId)
      .eq('user_id', user.id)
      .single();

    if (requestError || !generationRequest) {
      return NextResponse.json(
        { error: 'Generation request not found' },
        { status: 404 }
      );
    }

    // Debug: Log the current status
    console.log('Current request status for video regeneration:', {
      status: generationRequest.status,
      currentStep: generationRequest.current_step,
      totalSteps: generationRequest.total_steps,
      hasTransformedImage: !!generationRequest.output_data?.transformed_image_url
    });

    // Validate request state - allow regeneration for completed or failed video generation
    const canRegenerateVideo = (generationRequest.status === 'completed' || generationRequest.status === 'failed') 
                               && generationRequest.output_data?.transformed_image_url;
    
    if (!canRegenerateVideo) {
      return NextResponse.json(
        { 
          error: 'Can only regenerate videos for completed or failed video generations with transformed image',
          currentStatus: generationRequest.status,
          currentStep: generationRequest.current_step,
          hasTransformedImage: !!generationRequest.output_data?.transformed_image_url
        },
        { status: 400 }
      );
    }

    // Check required environment variables
    if (!process.env.FAL_API_KEY) {
      console.error('FAL_API_KEY environment variable is not set');
      return NextResponse.json(
        { error: 'FAL API key not configured. Please contact support.' },
        { status: 500 }
      );
    }

    // Get the video settings and calculate cost
    const resolution = generationRequest.input_data.video_settings.resolution;
    const videoCost = VIDEO_COSTS[resolution as keyof typeof VIDEO_COSTS];

    // Check wallet balance
    const { data: wallet, error: walletError } = await supabaseAdmin
      .from('wallets')
      .select('*')
      .eq('id', generationRequest.wallet_id)
      .single();

    if (walletError || !wallet) {
      return NextResponse.json(
        { error: 'Wallet not found' },
        { status: 404 }
      );
    }

    if (wallet.balance < videoCost) {
      return NextResponse.json(
        { error: 'Insufficient balance for video regeneration', required: videoCost, available: wallet.balance },
        { status: 402 }
      );
    }

    // Find the existing video generation step to get the original prompt
    const { data: existingVideoStep, error: stepFetchError } = await supabaseAdmin
      .from('web_generation_steps')
      .select('*')
      .eq('web_request_id', requestId)
      .eq('step_type', 'video_generate')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    let videoPrompt;
    if (existingVideoStep && existingVideoStep.input_data?.prompt) {
      // Use the same prompt from the previous attempt
      videoPrompt = existingVideoStep.input_data.prompt;
      console.log('Using existing video prompt:', videoPrompt);
    } else {
      // Generate enhanced video prompt for transition effect based on character
      const characterPrompts = {
        'scorpion': 'Seamless transformation from normal person to Scorpion from Mortal Kombat with mystical fire effects, ninja movements, and supernatural energy',
        'sub-zero': 'Seamless transformation from normal person to Sub-Zero from Mortal Kombat with ice and frost effects, martial arts stance, and freezing energy',
        'raiden': 'Seamless transformation from normal person to Raiden from Mortal Kombat with lightning and electrical effects, godly presence, and thunder energy',
        'liu-kang': 'Seamless transformation from normal person to Liu Kang from Mortal Kombat with dragon fire effects, martial arts movements, and ancient energy',
        'kitana': 'Seamless transformation from normal person to Kitana from Mortal Kombat with royal elegance, fan weapon effects, and mystical energy',
        'johnny-cage': 'Seamless transformation from normal person to Johnny Cage from Mortal Kombat with Hollywood flair, green energy effects, and confident action star movements',
        'mileena': 'Seamless transformation from normal person to Mileena from Mortal Kombat with savage ferocity, dark energy effects, and predatory movements',
        'kung-lao': 'Seamless transformation from normal person to Kung Lao from Mortal Kombat with monk discipline, wind effects around razor hat, and martial arts mastery'
      };
      
      videoPrompt = characterPrompts[generationRequest.input_data.character_type as keyof typeof characterPrompts] || 
        `Seamless transformation from normal person to ${generationRequest.input_data.character_type} from Mortal Kombat with dramatic fighting effects and mystical energy`;
      console.log('Using character-specific fallback video prompt:', videoPrompt);
    }

    // Deduct video cost from wallet
    await supabaseAdmin
      .from('wallets')
      .update({ 
        balance: wallet.balance - videoCost,
        updated_at: new Date().toISOString()
      })
      .eq('id', wallet.id);

    // Create token transaction record
    await supabaseAdmin
      .from('token_transactions')
      .insert({
        wallet_id: wallet.id,
        amount: -videoCost,
        description: `Mortal Kombat video regeneration - ${resolution}`,
        transaction_type: 'consumption'
      });

    // Update request status to in_progress
    await supabaseAdmin
      .from('web_generation_requests')
      .update({ 
        status: 'in_progress',
        current_step: 3,
        error_message: null, // Clear any previous error
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId);

    // Create new video generation step
    const { data: stepData, error: stepError } = await supabaseAdmin
      .from('web_generation_steps')
      .insert({
        web_request_id: requestId,
        step_number: 3,
        step_type: 'video_generate',
        status: 'in_progress',
        fal_model: 'fal-ai/pixverse/v4.5/transition',
        input_data: {
          first_image_url: generationRequest.input_data.uploaded_image_url,
          last_image_url: generationRequest.output_data.transformed_image_url,
          prompt: videoPrompt,
          duration: generationRequest.input_data.video_settings.duration,
          resolution: resolution,
          style: generationRequest.input_data.video_settings.style,
          regeneration: true // Mark as regeneration
        },
        token_cost: videoCost,
        started_at: new Date().toISOString()
      })
      .select()
      .single();

    if (stepError) {
      console.error('Video regeneration step creation error:', stepError);
      
      // Refund the tokens since we failed before calling the API
      await supabaseAdmin
        .from('wallets')
        .update({ 
          balance: wallet.balance,
          updated_at: new Date().toISOString()
        })
        .eq('id', wallet.id);

      // Create refund transaction
      await supabaseAdmin
        .from('token_transactions')
        .insert({
          wallet_id: wallet.id,
          amount: videoCost,
          description: `Refund: Video regeneration step creation failed - ${resolution}`,
          transaction_type: 'refund'
        });

      // Reset status back to original state
      await supabaseAdmin
        .from('web_generation_requests')
        .update({
          status: generationRequest.status, // Reset to original status
          error_message: 'Failed to create video regeneration step',
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      return NextResponse.json(
        { error: 'Failed to create video regeneration step' },
        { status: 500 }
      );
    }

    // Call FAL API for video generation
    try {
      console.log('Calling FAL API for video regeneration with:', {
        prompt: videoPrompt,
        imageUrl: generationRequest.output_data.transformed_image_url,
        duration: generationRequest.input_data.video_settings.duration,
        resolution: resolution,
        style: generationRequest.input_data.video_settings.style
      });

      const result = await fal.subscribe("fal-ai/pixverse/v4.5/transition", {
        input: {
          prompt: videoPrompt,
          first_image_url: generationRequest.input_data.uploaded_image_url,
          last_image_url: generationRequest.output_data.transformed_image_url,
          duration: generationRequest.input_data.video_settings.duration.toString(),
          resolution: resolution,
          ...(generationRequest.input_data.video_settings.style && {
            style: generationRequest.input_data.video_settings.style
          })
        },
        logs: true,
        onQueueUpdate: (update) => {
          if (update.status === "IN_PROGRESS") {
            console.log("Processing video regeneration:", update.logs?.map((log) => log.message).join('\n'));
          }
        },
      });

      console.log('FAL video regeneration result:', result);

      if (!result.data || !result.data.video) {
        throw new Error('No video returned from API');
      }

      const videoUrl = result.data.video.url;

      // Update step with success
      await supabaseAdmin
        .from('web_generation_steps')
        .update({
          status: 'completed',
          fal_request_id: result.requestId,
          output_data: {
            video_url: videoUrl,
            original_response: result.data
          },
          completed_at: new Date().toISOString()
        })
        .eq('id', stepData.id);

      // Always create a new video record (keep all video generations)
      await supabaseAdmin
        .from('web_videos')
        .insert({
          user_id: user.id,
          wallet_id: wallet.id,
          web_request_id: requestId,
          video_url: videoUrl,
          duration: generationRequest.input_data.video_settings.duration,
          resolution: resolution,
          prompt_text: videoPrompt,
          parameters: {
            style: generationRequest.input_data.video_settings.style,
            character_type: generationRequest.input_data.character_type,
            regeneration_number: true // Mark as regenerated video
          },
          source_image_urls: [
            generationRequest.input_data.uploaded_image_url,
            generationRequest.output_data.transformed_image_url
          ],
          fal_request_id: result.requestId,
          fal_model: 'fal-ai/pixverse/v4.5/transition',
          token_cost: videoCost
        });

      // Update main request to completed with new video
      const finalOutputData = {
        ...generationRequest.output_data,
        video_url: videoUrl
      };

      await supabaseAdmin
        .from('web_generation_requests')
        .update({ 
          status: 'completed',
          current_step: 4,
          output_data: finalOutputData,
          error_message: null,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      return NextResponse.json({
        request_id: requestId,
        status: 'completed',
        current_step: 4,
        video: {
          url: videoUrl,
          duration: generationRequest.input_data.video_settings.duration,
          resolution: resolution,
          prompt: videoPrompt
        },
        final_output: {
          original_image_url: generationRequest.input_data.uploaded_image_url,
          transformed_image_url: generationRequest.output_data.transformed_image_url,
          video_url: videoUrl
        },
        tokens_charged: videoCost,
        remaining_balance: wallet.balance - videoCost,
        regenerated: true
      });

    } catch (falError: any) {
      console.error('FAL video regeneration error:', falError);

      // Update step with failure
      await supabaseAdmin
        .from('web_generation_steps')
        .update({
          status: 'failed',
          error_message: falError.message || falError.toString(),
          completed_at: new Date().toISOString()
        })
        .eq('id', stepData.id);

      // Reset main request status (don't mark as failed, allow retry)
      await supabaseAdmin
        .from('web_generation_requests')
        .update({ 
          status: 'completed', // Reset to completed so they can try again
          current_step: 4,
          error_message: `Video regeneration failed: ${falError.message || falError.toString()}`,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      // Refund the video cost
      await supabaseAdmin
        .from('wallets')
        .update({ 
          balance: wallet.balance,
          updated_at: new Date().toISOString()
        })
        .eq('id', wallet.id);

      // Create refund transaction
      await supabaseAdmin
        .from('token_transactions')
        .insert({
          wallet_id: wallet.id,
          amount: videoCost,
          description: `Refund: Video regeneration failed - ${falError.message || 'Unknown error'}`,
          transaction_type: 'refund'
        });

      return NextResponse.json(
        { 
          error: 'Video regeneration failed', 
          details: falError.message || falError.toString(),
          request_id: requestId,
          refunded_tokens: videoCost
        },
        { status: 500 }
      );
    }

  } catch (error: any) {
    console.error('Regenerate video error:', error);
    
    // Try to reset status if we have the requestId
    const { id: requestId } = await params;
    if (requestId) {
      try {
        await supabaseAdmin
          .from('web_generation_requests')
          .update({
            status: 'completed', // Reset to allow retry
            error_message: `Video regeneration error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            updated_at: new Date().toISOString()
          })
          .eq('id', requestId);
      } catch (updateError) {
        console.error('Failed to reset status after video regeneration error:', updateError);
      }
    }
    
    return NextResponse.json(
      { 
        error: 'Failed to regenerate video', 
        details: error.message || error.toString() 
      },
      { status: 500 }
    );
  }
} 