This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## 🔧 Recent Fixes

### Image Processing for MK Video Generator

**Issue:** Users uploading images larger than 4000 pixels experienced failures in the Mortal Kombat video generator (both free and paid versions).

**Root Cause:** FAL AI services (GPT-Image-1, SeedEdit, FLUX, PixVerse) have undocumented limitations on image dimensions.

**Solution:** Implemented preview & confirm workflow with automatic image preprocessing:
- **Max Dimension**: 3840 pixels (safely under the 4000 pixel limit)
- **Smart Resizing**: Maintains aspect ratio when resizing
- **EXIF Orientation**: Automatically corrects image rotation based on EXIF metadata
- **Format Conversion**: Converts HEIC, WEBP, and other formats to JPEG/PNG for better compatibility
- **Quality Optimization**: 85% JPEG quality for optimal file size vs quality balance
- **User Preview Modal**: Shows side-by-side comparison of original vs optimized image
- **Transparent Process**: Displays dimensions, file sizes, and optimization benefits
- **User Control**: Requires explicit user approval before proceeding with generation

**Technical Details:**
- Added `processImageForFAL()` function to both paid and free MK routes
- Uses Sharp's Lanczos3 kernel for high-quality resizing
- **EXIF Auto-Rotation**: `.rotate()` without parameters reads EXIF orientation data
- Preserves original images under 3840px to maintain quality
- Automatic format detection and conversion
- Comprehensive error handling with fallback to original file

**New API Endpoints:**
- `/api/web-generations/mortal-kombat/confirm-resize` - Handles user confirmation for paid generations
- `/api/free-generations/mortal-kombat/confirm-resize` - Handles user confirmation for free generations

**Files Modified:**
- `src/app/api/web-generations/mortal-kombat/start/route.ts` - Added image size detection and preview generation
- `src/app/api/free-generations/mortal-kombat/start/route.ts` - Added image size detection and preview generation
- `src/app/api/web-generations/mortal-kombat/confirm-resize/route.ts` - New confirmation endpoint for paid users
- `src/app/api/free-generations/mortal-kombat/confirm-resize/route.ts` - New confirmation endpoint for free users
- `src/components/MortalKombatVideoFlow.tsx` - Added preview modal and confirmation workflow
- Added Sharp dependency to `package.json`

Now users can upload high-resolution images without failures, and the system automatically optimizes them for FAL API compatibility while maintaining visual quality and correct orientation.
