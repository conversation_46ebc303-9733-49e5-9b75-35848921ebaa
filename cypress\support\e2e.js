// ***********************************************************
// This example support/e2e.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Load test fixtures
before(() => {
  cy.fixture('test-data.json').then((testData) => {
    Cypress.env('testData', testData);
  });
});

// Global beforeEach hook
beforeEach(() => {
  // We can add setup code here if needed
  cy.log('Running test against API endpoints');
});

// Global afterAll hook - clean up test data after all tests
after(() => {
  // Only run cleanup if enabled in config
  if (Cypress.env('CLEANUP_AFTER_TESTS') !== false) {
    cy.log('Running test data cleanup after all tests');
    
    // Just call the cleanup command and let Cypress handle the async behavior
    // Don't chain further or return anything from the after hook
    cy.cleanupTestData({
      conversationPrefix: 'test-',
      olderThan: 1 // Clean up test data older than 1 hour
    });
  }
});

// Mock service responses if needed
Cypress.on('window:before:load', (win) => {
  // This runs before each test - can be used to inject mocks
}); 