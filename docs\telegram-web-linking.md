# Telegram-Web User Linking & Wallet Unification

## Overview
This document describes how Telegram users and web users are linked in the Aivis platform, how credits (tokens) are unified, and how wallet selection works for both bot and web interactions.

---

## 1. One-Time Code Linking Flow

- **Web user** clicks "Link Telegram" in the dashboard, generating a one-time code (stored in `link_codes`).
- **User sends the code** to the Telegram bot.
- **<PERSON><PERSON> calls** the `/api/link-telegram` endpoint with `{ code, telegram_id }` and the `x-api-key` header.
- **API validates** the code, links the Telegram `external_identities` row to the web user's `user_id`, and marks the code as used.

---

## 2. Wallet Structure
- Each user (web or external) has a wallet in the `wallets` table.
- A wallet is owned by either a `user_id` (web user) or an `external_identity_id` (Telegram/WhatsApp-only user).
- Only one owner per wallet.

---

## 3. Wallet Selection Logic (Bot Side)

When the bot receives a message from a Telegram user:

1. **Find the `external_identities` row** for the Telegram user.
2. **If `user_id` is set (linked):**
    - Use the wallet where `user_id` matches (the web user's wallet).
3. **If not linked:**
    - Use the wallet where `external_identity_id` matches (Telegram-only wallet).

**This ensures that after linking, all credits are unified and consumed from the web account.**

---

## 4. API Endpoints

### `/api/link-telegram` (POST)
- Accepts `{ code, telegram_id }` in JSON body.
- Requires `x-api-key` header.
- Links the Telegram user to the web user if the code is valid and not expired.

### `/api/bot-receive-message` (POST)
- Handles incoming messages from Telegram/WhatsApp.
- Uses the wallet selection logic above to determine which wallet to use for token consumption.

---

## 5. Credit Consumption
- **All token-consuming actions (e.g., image generation) use the wallet selected as above.**
- If a Telegram user is linked to a web user, all credits are unified and deducted from the web user's wallet, regardless of whether the action is performed via the web app or the Telegram bot.

---

## 6. Security
- The `/api/link-telegram` endpoint is protected by an `x-api-key` header to prevent unauthorized linking.
- One-time codes expire after 30 minutes and can only be used once.

---

## 7. Extensibility
- The same logic can be applied to WhatsApp or other external providers by following the same linking and wallet selection pattern. 