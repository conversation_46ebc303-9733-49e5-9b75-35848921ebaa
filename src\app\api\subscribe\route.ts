import { NextResponse } from 'next/server';
import mailchimp, { getListId } from '@/utils/mailchimpConfig';

// Check if we're in test/development mode - only for test page
// For homepage, we'll always use production mode
const isTestMode = process.env.MAILCHIMP_TEST_MODE === 'true';

export async function POST(request: Request) {
  try {
    const { email, testMode = false } = await request.json();

    if (!email || !email.includes('@')) {
      return NextResponse.json(
        { error: 'Adresa de email este invalidă.' },
        { status: 400 }
      );
    }

    // Only use test mode if specifically requested via the testMode parameter
    // This ensures homepage always uses production mode
    if (isTestMode && testMode) {
      console.log('TEST MODE: Would have added subscriber:', email);
      
      // Simulate a delay like a real API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate already subscribed for emails ending with @test.com
      if (email.endsWith('@test.com')) {
        return NextResponse.json(
          { error: 'Această adresă de email este deja abonată.' },
          { status: 400 }
        );
      }
      
      // Return success response for test mode
      return NextResponse.json(
        { success: true, message: 'Mulțumim pentru abonare! Te vom notifica când platforma va fi disponibilă. (TEST MODE)' },
        { status: 200 }
      );
    }

    // Production mode - real subscription
    try {
      // Add the subscriber to your Mailchimp list

      const listId = getListId();
      console.log('List ID:', listId);
      console.log('Adding subscriber to Mailchimp:', email);

      await mailchimp.lists.addListMember(listId, {
        email_address: email,
        status: 'subscribed',
      });

      // Return success response
      return NextResponse.json(
        { success: true, message: 'Mulțumim pentru abonare! Te vom notifica când platforma va fi disponibilă.' },
        { status: 200 }
      );
    } catch (mailchimpError: any) {
      console.error('Mailchimp error:', mailchimpError);

      // Handle case where user is already subscribed
      if (
        mailchimpError.response && 
        mailchimpError.response.body && 
        mailchimpError.response.body.title === 'Member Exists'
      ) {
        return NextResponse.json(
          { error: 'Această adresă de email este deja abonată.' },
          { status: 400 }
        );
      }

      throw mailchimpError; // Re-throw for general error handling
    }
  } catch (error: any) {
    console.error('Error subscribing to newsletter:', error);

    return NextResponse.json(
      { error: 'A apărut o eroare. Te rugăm să încerci din nou.' },
      { status: 500 }
    );
  }
} 