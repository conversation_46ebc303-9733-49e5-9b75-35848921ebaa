import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export async function GET(request: NextRequest) {
  try {
    // Get session from request headers
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'No authorization header' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token);

    if (userError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('user_id', user.id)
      .single();

    console.log('Admin check:', { user_id: user.id, profile, profileError });

    if (profileError || profile?.role !== 'admin') {
      console.log('Admin access denied:', { profileError, role: profile?.role });
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const filter = searchParams.get('filter') || 'all';
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Fetch image-to-video requests with user details
    let query = supabaseAdmin
      .from('image_to_video_requests')
      .select('*')
      .order(sortBy, { ascending: sortOrder === 'asc' });

    if (filter !== 'all') {
      query = query.eq('status', filter);
    }

    const { data: requestsData, error: requestsError } = await query;

    if (requestsError) {
      console.error('Error fetching image-to-video requests:', requestsError);
      return NextResponse.json({ error: 'Failed to fetch requests' }, { status: 500 });
    }

    // Fetch user data for each request separately
    const requestsWithUserData = await Promise.all(
      (requestsData || []).map(async (request) => {
        if (request.user_id) {
          const { data: profileData } = await supabaseAdmin
            .from('profiles')
            .select('email, role')
            .eq('user_id', request.user_id)
            .single();
          
          return {
            ...request,
            profiles: profileData ? {
              email: profileData.email,
              full_name: profileData.email // Using email as full_name since full_name doesn't exist
            } : null
          };
        }
        return {
          ...request,
          profiles: null
        };
      })
    );

    const requests = requestsWithUserData;

    // Calculate statistics
    const stats = {
      total: requests?.length || 0,
      completed: requests?.filter(r => r.status === 'completed').length || 0,
      processing: requests?.filter(r => r.status === 'processing').length || 0,
      failed: requests?.filter(r => r.status === 'failed').length || 0,
      pending: requests?.filter(r => r.status === 'pending').length || 0
    };

    return NextResponse.json({
      requests: requests || [],
      stats
    });

  } catch (error) {
    console.error('Admin image-to-video generations API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Get session from request headers
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'No authorization header' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token);

    if (userError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('user_id', user.id)
      .single();

    if (profileError || profile?.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    const { requestId, is_public } = body;

    if (!requestId || typeof is_public !== 'boolean') {
      return NextResponse.json({ error: 'Invalid request data' }, { status: 400 });
    }

    // Update the is_public status
    const { data, error } = await supabaseAdmin
      .from('image_to_video_requests')
      .update({ is_public })
      .eq('id', requestId)
      .select()
      .single();

    if (error) {
      console.error('Error updating image-to-video request:', error);
      return NextResponse.json({ error: 'Failed to update request' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      request: data
    });

  } catch (error) {
    console.error('Admin image-to-video PATCH API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
