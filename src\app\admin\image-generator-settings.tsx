"use client";

import React, { useEffect, useState } from "react";
import { supabase } from "@/lib/supabaseClient";

const CONFIG_KEY = "image_generator";
const OPTIONS = [
  { value: "default", label: "Default" },
  { value: "fal_seededit", label: "fal.ai SeedEdit" },
];

export default function ImageGeneratorSettingsPage() {
  const [selected, setSelected] = useState<string>(OPTIONS[0].value);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<string | null>(null);

  useEffect(() => {
    fetchCurrentSetting();
  }, []);

  const fetchCurrentSetting = async () => {
    setLoading(true);
    setMessage(null);
    const { data, error } = await supabase
      .from("config_options")
      .select("value")
      .eq("key", CONFIG_KEY)
      .single();
    if (error) {
      setMessage("Could not load current setting. Using default.");
      setSelected(OPTIONS[0].value);
    } else if (data && data.value) {
      setSelected(data.value.option || OPTIONS[0].value);
    }
    setLoading(false);
  };

  const handleSave = async () => {
    setSaving(true);
    setMessage(null);
    const { error } = await supabase
      .from("config_options")
      .upsert([
        { key: CONFIG_KEY, value: { option: selected }, updated_at: new Date().toISOString() },
      ]);
    if (error) {
      setMessage("Failed to save setting.");
    } else {
      setMessage("Setting saved successfully.");
    }
    setSaving(false);
  };

  return (
    <div style={{ maxWidth: 500, margin: "2rem auto", padding: 24, background: "#fff", borderRadius: 8, boxShadow: "0 2px 8px #0001" }}>
      <h2>Image Generator Settings</h2>
      <p>Select which image generator to use for the Mortal Kombat generator.</p>
      <div style={{ margin: "1rem 0" }}>
        <select
          value={selected}
          onChange={e => setSelected(e.target.value)}
          disabled={loading || saving}
          style={{ padding: 8, fontSize: 16 }}
        >
          {OPTIONS.map(opt => (
            <option key={opt.value} value={opt.value}>{opt.label}</option>
          ))}
        </select>
      </div>
      <button onClick={handleSave} disabled={saving || loading} style={{ padding: "8px 16px", fontSize: 16 }}>
        {saving ? "Saving..." : "Save"}
      </button>
      {message && <div style={{ marginTop: 16, color: message.includes("success") ? "green" : "red" }}>{message}</div>}
    </div>
  );
} 