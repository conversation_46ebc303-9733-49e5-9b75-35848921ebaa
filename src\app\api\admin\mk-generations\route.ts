import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }
    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('user_id', user.id)
      .single();
    if (profileError || profile?.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }
    // Fetch only Mortal Kombat generations
    const { data: requests, error } = await supabaseAdmin
      .from('web_generation_requests')
      .select('*')
      .eq('flow_type', 'mortal_kombat')
      .order('created_at', { ascending: false });
    if (error) {
      return NextResponse.json({ error: 'Failed to fetch requests' }, { status: 500 });
    }
    // Fetch user profiles
    const userIds = [...new Set(requests?.map(r => r.user_id) || [])].filter(Boolean);
    const { data: profiles, error: profilesError } = await supabaseAdmin
      .from('profiles')
      .select('user_id, email, role')
      .in('user_id', userIds);
    const profilesMap = new Map();
    profiles?.forEach(profile => {
      profilesMap.set(profile.user_id, profile);
    });
    // Process requests to include original and generated photo URLs
    const processedRequests = requests?.map(request => {
      const userProfile = profilesMap.get(request.user_id);
      const userEmail = userProfile?.email;
      const userRole = userProfile?.role;
      return {
        id: request.id,
        user_id: request.user_id,
        user_email: userEmail || 'Unknown',
        user_role: userRole || 'user',
        status: request.status,
        character: request.input_data?.character_type || request.character,
        created_at: request.created_at,
        original_photo_url: request.input_data?.uploaded_image_url || null,
        generated_photo_url: request.output_data?.transformed_image_url || null,
        is_public: request.is_public ?? false
      };
    }) || [];
    return NextResponse.json({ requests: processedRequests });
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { id, is_public } = await request.json();
    if (!id || typeof is_public !== 'boolean') {
      return NextResponse.json({ error: 'id and is_public required' }, { status: 400 });
    }
    // Auth check (reuse from GET)
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }
    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('user_id', user.id)
      .single();
    if (profileError || profile?.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }
    // Update is_public
    const { error } = await supabaseAdmin
      .from('web_generation_requests')
      .update({ is_public })
      .eq('id', id);
    if (error) {
      return NextResponse.json({ error: 'Failed to update is_public' }, { status: 500 });
    }
    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 