"use client";

import React, { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from 'next/navigation';
import { supabase } from "@/lib/supabaseClient";
import styles from "../page.module.css";
import Header from '@/components/Header';
import { logger } from '@/utils/logger';

function PaymentSuccessContent() {
  const [loading, setLoading] = useState(true);
  const [payment, setPayment] = useState<any>(null);
  const [error, setError] = useState<string>("");
  const router = useRouter();
  const searchParams = useSearchParams();
  const sessionId = searchParams?.get('session_id');

  useEffect(() => {
    const handlePaymentSuccess = async () => {
      try {
        // Get the current user
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        
        if (userError || !user) {
          router.push('/login');
          return;
        }

        if (!sessionId) {
          setError('Invalid payment session');
          setLoading(false);
          return;
        }

        // Fetch payment details
        const { data: paymentData, error: paymentError } = await supabase
          .from('payments')
          .select(`
            *,
            token_packages (
              name,
              description,
              token_amount
            )
          `)
          .eq('stripe_session_id', sessionId)
          .eq('user_id', user.id)
          .single();

        if (paymentError || !paymentData) {
          setError('Payment not found');
          setLoading(false);
          return;
        }

        setPayment(paymentData);
        setLoading(false);

        // Redirect to tokens page after 5 seconds
        setTimeout(() => {
          router.push('/dashboard/tokens');
        }, 5000);

      } catch (err: any) {
        logger.error('Error handling payment success:', err);
        setError('Failed to process payment success');
        setLoading(false);
      }
    };

    handlePaymentSuccess();
  }, [sessionId, router]);

  const formatPrice = (cents: number, currency: string): string => {
    const amount = cents / 100;
    return new Intl.NumberFormat('ro-RO', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <Header />
        <div className={styles.loading}>
          Processing your payment...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <Header />
        <div className={styles.content}>
          <div className={styles.error}>
            {error}
          </div>
          <button 
            onClick={() => router.push('/dashboard/tokens')}
            className={styles.purchaseButton}
          >
            Go to Tokens Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <Header />
      
      <div className={styles.content}>
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <div style={{ 
            fontSize: '4rem', 
            color: '#22c55e', 
            marginBottom: '1rem' 
          }}>
            ✅
          </div>
          
          <h1 style={{ color: '#22c55e', marginBottom: '2rem' }}>
            Payment Successful!
          </h1>
          
          {payment && (
            <div className={styles.tokenBalance} style={{ margin: '2rem auto', maxWidth: '500px' }}>
              <h3>Purchase Details</h3>
              <p><strong>{payment.token_packages.name}</strong></p>
              <p>{payment.token_packages.description}</p>
              <p>
                <strong>{payment.token_packages.token_amount} tokens</strong> 
                for {formatPrice(payment.amount_cents, payment.currency)}
              </p>
              <p style={{ fontSize: '0.9rem', opacity: 0.9 }}>
                Tokens have been added to your account!
              </p>
            </div>
          )}
          
          <p style={{ color: '#666', marginBottom: '2rem' }}>
            You will be redirected to your tokens page in a few seconds...
          </p>
          
          <button 
            onClick={() => router.push('/dashboard/tokens')}
            className={styles.purchaseButton}
            style={{ maxWidth: '200px' }}
          >
            View My Tokens
          </button>
        </div>
      </div>
    </div>
  );
}

export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PaymentSuccessContent />
    </Suspense>
  );
} 