import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client specifically for server-side use with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: requestId } = await params;

    // Get user from auth header or cookie
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '') || 
                  request.cookies.get('supabase-auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user info
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    // Get the generation request
    const { data: generationRequest, error: requestError } = await supabaseAdmin
      .from('web_generation_requests')
      .select('*')
      .eq('id', requestId)
      .eq('user_id', user.id)
      .single();

    if (requestError || !generationRequest) {
      return NextResponse.json(
        { error: 'Generation request not found' },
        { status: 404 }
      );
    }

    // Get all steps for this request
    const { data: steps, error: stepsError } = await supabaseAdmin
      .from('web_generation_steps')
      .select('*')
      .eq('web_request_id', requestId)
      .order('step_number', { ascending: true });

    if (stepsError) {
      console.error('Steps fetch error:', stepsError);
      return NextResponse.json(
        { error: 'Failed to fetch generation steps' },
        { status: 500 }
      );
    }

    // Get all videos for this request
    const { data: allVideos, error: videosError } = await supabaseAdmin
      .from('web_videos')
      .select('*')
      .eq('web_request_id', requestId)
      .order('created_at', { ascending: true });

    if (videosError) {
      console.error('Videos fetch error:', videosError);
      // Don't fail the request, just log the error
    }

    // Debug logging for status issues
    console.log('Debug - Generation Request Details:', {
      requestId,
      status: generationRequest.status,
      currentStep: generationRequest.current_step,
      totalSteps: generationRequest.total_steps,
      awaitingApprovalForStep: generationRequest.awaiting_approval_for_step,
      errorMessage: generationRequest.error_message,
      stepsCount: steps?.length || 0,
      steps: steps?.map(s => ({
        stepNumber: s.step_number,
        stepType: s.step_type,
        status: s.status,
        hasError: !!s.error_message,
        startedAt: s.started_at,
        completedAt: s.completed_at
      }))
    });

    // Format steps for response
    const formattedSteps = steps.map(step => ({
      step_number: step.step_number,
      step_type: step.step_type,
      status: step.status,
      requires_approval: step.requires_approval,
      fal_model: step.fal_model,
      fal_request_id: step.fal_request_id,
      token_cost: step.token_cost,
      started_at: step.started_at,
      completed_at: step.completed_at,
      approved_at: step.approved_at,
      rejection_reason: step.rejection_reason,
      error_message: step.error_message,
      retry_count: step.retry_count,
      input_data: step.input_data,
      output_data: step.output_data
    }));

    // Calculate progress percentage
    const completedSteps = steps.filter(step => 
      step.status === 'completed' || step.status === 'approved'
    ).length;
    const progress = Math.round((completedSteps / generationRequest.total_steps) * 100);

    // Determine next action based on current state (updated for new flow)
    let nextAction = null;
    
    switch (generationRequest.status) {
      case 'pending':
        nextAction = 'processing_upload'; // Upload and transformation are automatic now
        break;
      case 'in_progress':
        if (generationRequest.current_step === 2) {
          nextAction = 'generating_transformation'; // Transformation in progress
        } else if (generationRequest.current_step === 3) {
          nextAction = 'poll_status'; // Video is generating
        }
        break;
      case 'awaiting_approval':
        nextAction = 'approve_transformation';
        break;
      case 'approved':
        nextAction = 'generate_video';
        break;
      case 'rejected':
        nextAction = 'restart_flow'; // Since we auto-generate, user needs to restart
        break;
      case 'completed':
        nextAction = 'download_results';
        break;
      case 'failed':
        nextAction = 'retry_or_contact_support';
        break;
    }

    // Build final output if available
    let finalOutput = null;
    if (generationRequest.output_data) {
      const outputData = generationRequest.output_data;
      if (outputData.video_url) {
        finalOutput = {
          original_image_url: generationRequest.input_data.uploaded_image_url,
          transformed_image_url: outputData.transformed_image_url,
          video_url: outputData.video_url
        };
      }
    }

    return NextResponse.json({
      request_id: requestId,
      status: generationRequest.status,
      current_step: generationRequest.current_step,
      total_steps: generationRequest.total_steps,
      progress_percentage: progress,
      flow_type: generationRequest.flow_type,
      
      // Input settings
      input_settings: {
        character_type: generationRequest.input_data.character_type,
        video_settings: generationRequest.input_data.video_settings,
        uploaded_image_url: generationRequest.input_data.uploaded_image_url
      },

      // Approval tracking
      awaiting_approval_for_step: generationRequest.awaiting_approval_for_step,
      approved_steps: generationRequest.approved_steps,
      rejected_steps: generationRequest.rejected_steps,

      // Cost information
      estimated_cost: generationRequest.estimated_cost,
      actual_cost: generationRequest.actual_cost,

      // Current transformation result (if available)
      transformation: generationRequest.output_data?.transformed_image_url ? {
        original_image_url: generationRequest.input_data.uploaded_image_url,
        transformed_image_url: generationRequest.output_data.transformed_image_url,
        character_type: generationRequest.input_data.character_type,
        prompt_used: generationRequest.input_data.transformation_prompt
      } : null,

      // Video result (if available) - latest video
      video: generationRequest.output_data?.video_url ? {
        url: generationRequest.output_data.video_url,
        duration: generationRequest.input_data.video_settings.duration,
        resolution: generationRequest.input_data.video_settings.resolution
      } : null,

      // All videos generated for this request
      all_videos: allVideos?.map(video => ({
        id: video.id,
        video_url: video.video_url,
        duration: video.duration,
        resolution: video.resolution,
        prompt_text: video.prompt_text,
        token_cost: video.token_cost,
        fal_request_id: video.fal_request_id,
        created_at: video.created_at,
        is_regeneration: video.parameters?.regeneration_number || false
      })) || [],

      // Final complete output
      final_output: finalOutput,

      // All steps with details
      steps: formattedSteps,

      // Error information
      error_message: generationRequest.error_message,

      // Next action suggestion
      next_action: nextAction,

      // Timestamps
      created_at: generationRequest.created_at,
      updated_at: generationRequest.updated_at
    });

  } catch (error: any) {
    console.error('Get generation status error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get generation status', 
        details: error.message || error.toString() 
      },
      { status: 500 }
    );
  }
} 