// src/app/api/admin/free-generations/create/route.ts
import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';
import { logger } from '@/utils/logger';

export async function POST() {
  // Create a Supabase client specifically for server-side use with service role key
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    process.env.SUPABASE_SERVICE_ROLE_KEY || ''
  );

  // TODO: Add robust authentication to ensure only admins can access this.
  // For now, we'll proceed, but this is a critical security step.
  // Example:
  // const { data: { user } } = await supabase.auth.getUser();
  // if (!user || user.role !== 'admin') {
  //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  // }

  try {
    const { data, error } = await supabase
      .from('free_generations')
      .insert([{ flow_type: 'mortal_kombat', status: 'new' }])
      .select('id')
      .single();

    if (error) {
      logger.error('Error creating free generation:', error);
      return NextResponse.json({ error: 'Failed to create free generation link.' }, { status: 500 });
    }

    const freeGenerationId = data.id;
    
    // Get base URL with proper fallbacks
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 
                   (process.env.NODE_ENV === 'production' ? 'https://aivis.ro' : 'http://localhost:3000');
    
    const freeLink = `${baseUrl}/mk-free/${freeGenerationId}`;

    return NextResponse.json({
      message: 'Free generation link created successfully.',
      freeGenerationId,
      freeLink,
    });
  } catch (error) {
    logger.error('Unexpected error in /api/admin/free-generations/create:', error);
    return NextResponse.json({ error: 'An unexpected error occurred.' }, { status: 500 });
  }
}
