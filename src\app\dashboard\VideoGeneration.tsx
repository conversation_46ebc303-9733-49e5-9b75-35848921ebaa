import React from 'react';
import Button from 'react-bootstrap/Button';
import { format } from 'date-fns';
import styles from './VideoGeneration.module.css';

interface VideoGenerationProps {
  video: any;
  t: (key: string, params?: any) => string;
  onViewVideo: (video: any) => void;
}

const VideoGeneration: React.FC<VideoGenerationProps> = ({ video, t, onViewVideo }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#28a745';
      case 'processing': return '#ffc107';
      case 'failed': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return t('video_generation_status_completed');
      case 'processing': return t('video_generation_status_processing');
      case 'pending': return t('video_generation_status_pending');
      case 'failed': return t('video_generation_status_failed');
      default: return status;
    }
  };

  return (
    <div className={styles.videoGenCard}>
      <div className={styles.videoGenLeft}>
        {video.image_url && (
          <img src={video.image_url} alt="Source" className={styles.videoGenImage} />
        )}
        <div className={styles.videoGenInfoBlock}>
          <div className={styles.videoGenPrompt}>
            {video.prompt || 'Video Generation'}
          </div>
          <div className={styles.videoGenInfo}>
            <span style={{ color: getStatusColor(video.status), fontWeight: 500 }}>
              {getStatusText(video.status)}
            </span>
          </div>
          <div className={styles.videoGenInfo}>
            Durată: {video.duration}s | Rezoluție: {video.resolution}
          </div>
          <div className={styles.videoGenInfo}>
            {video.created_at ? format(new Date(video.created_at), 'dd MMM yyyy, HH:mm') : '-'}
          </div>
        </div>
      </div>
      <Button 
        variant={video.status === 'completed' ? 'primary' : 'secondary'} 
        className={styles.videoGenButton} 
        onClick={() => onViewVideo(video)}
        disabled={video.status !== 'completed'}
      >
        {video.status === 'completed' ? t('video_generation_view_video') : getStatusText(video.status)}
      </Button>
    </div>
  );
};

export default VideoGeneration;
