"use client";

import React, { useEffect, useState, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { supabase } from '../../../lib/supabaseClient';
import Header from '@/components/Header';
import styles from './page.module.css';
import classNames from 'classnames';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../../store';
import { fetchUserProfile } from '../../../store/userSlice';
import Toast from "react-bootstrap/Toast";
import ToastContainer from "react-bootstrap/ToastContainer";
import SignedUrlImage from '@/components/SignedUrlImage';
import SignedUrlVideo from '@/components/SignedUrlVideo';
import { logger } from '@/utils/logger';

const phaseDescriptions: { [key: number]: string } = {
  1: "You tell us what you need from the video",
  2: "Processing your request",
  3: "We give you a couple of options for the initial starting frame",
  4: "Processing your request",
  5: "We deliver the final video",
};

const ChatPage: React.FC = () => {
  const router = useRouter();
  const { chatId } = useParams() as { chatId: string };
  const [chat, setChat] = useState<any>(null);
  const [replies, setReplies] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [newMessage, setNewMessage] = useState("");
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [modalImage, setModalImage] = useState<string | null>(null);
  const [phase3Modal, setPhase3Modal] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.user);
  const [messages, setMessages] = useState<any[]>([]);
  const [replyToDelete, setReplyToDelete] = useState<any>(null);
  const [selectedStartingImage, setSelectedStartingImage] = useState<string | null>(null);
  const [allowStartingImage, setAllowStartingImage] = useState(false);
  const [notification, setNotification] = useState<string | null>(null);

  const showNotification = (message: string) => setNotification(message);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setImageFile(file);
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);
    }
  };

  useEffect(() => {
    if (!chatId) return;
    async function fetchChat() {
      // Fetch the chat record along with the creator's email
      const { data: chatData, error: chatError } = await supabase
        .from('chats')
        .select("*")
        .eq('id', chatId)
        .single();

      if (chatError) {
        logger.error("Error fetching chat:", chatError.message);
      } else {
        setChat(chatData);
        // Set the starting image selection from the chat record if it exists.
        if (chatData.starting_image_reply_id) {
          setSelectedStartingImage(chatData.starting_image_reply_id);
        }
      }

      // Fetch chat replies ordered by creation time
      const { data: repliesData, error: repliesError } = await supabase
        .from('chat_replies_with_user')
        .select("*")
        .eq('chat_id', chatId)
        .order('created_at', { ascending: true });

      if (repliesError) {
        logger.error("Error fetching replies:", repliesError.message);
      } else {
        setReplies(repliesData || []);
      }

      setLoading(false);
    }
    fetchChat();

  }, [chatId]);

  useEffect(() => {
    if (!user) {
      dispatch(fetchUserProfile());
    }
  }, [user, dispatch]);

  // Initial fetch of chat messages
  useEffect(() => {
    // Fetch messages on mount
    async function fetchMessages() {
      const { data, error } = await supabase
        .from('chat_replies')
        .select('*')
        .eq('chat_id', chatId)
        .order('created_at', { ascending: true });
  
      if (error) {
        logger.error('Error loading messages:', error);
      } else {
        setMessages(data);
      }
    }

    fetchMessages();
    
    // Subscribe using the new channel API for INSERT and DELETE events on chat_replies
    const channel = supabase.channel('realtime-chat-replies')
      .on('postgres_changes', { 
         event: 'INSERT', 
         schema: 'public', 
         table: 'chat_replies', 
         filter: `chat_id=eq.${chatId}` 
       }, (payload) => {

         if (user && payload.new.user_id !== user.id) {
           const msg = payload.new.image_url
             ? "New image received!"
             : "New message received!";
           showNotification(msg);
         }
         setMessages((prevMessages) => [...prevMessages, payload.new]);
 
         // After each reply, if the chat is still in phase 1, call check-chat-phase endpoint.
         if (chat && chat.phase === 1) {
           fetch("/api/check-chat-phase", {
             method: "POST",
             headers: { "Content-Type": "application/json" },
             body: JSON.stringify({ chatId }),
           })
             .then((r) => r.json())
             .then((data) => {
               logger.log("check-chat-phase response: ", data);
               if (data.success && data.data) {
                 setChat(data.data);
               }
             })
             .catch((error) => logger.error("Error in check-chat-phase:", error));
         }
       })
      .on('postgres_changes', { 
         event: 'DELETE', 
         schema: 'public', 
         table: 'chat_replies', 
         filter: `chat_id=eq.${chatId}` 
       }, (payload) => {
         logger.log('Reply deleted:', payload.old);
         setMessages((prevMessages) => 
           prevMessages.filter((m) => m.id !== payload.old.id)
         );
       })
      .subscribe((status) => logger.log('Subscription status:', status));
    
    // Cleanup subscription on component unmount
    return () => {
      channel.unsubscribe();
    };
  }, [chatId, chat, user]);

  const sendTextMessage = async () => {
    if (!newMessage.trim()) return;

    const { data: replyData, error } = await supabase
      .from('chat_replies')
      .insert({
        chat_id: chatId,
        user_id: user?.id,
        sender_role: 'user',
        message: newMessage,
      })
      .select();

    if (error) {
      logger.error("Error sending text message:", error.message);
      return;
    }

    if (replyData && replyData.length > 0) {
      // Do not update messages locally; subscription will update the UI.
      setNewMessage("");
    }
  };

  const sendAttachment = async () => {
    if (!imageFile) return;

    // Generate a unique file path using user's id as folder
    const filePath = `${user?.id}/${chatId}-${Date.now()}-${imageFile.name}`;
    const { error: uploadError } = await supabase
      .storage
      .from('chat-images')
      .upload(filePath, imageFile);

    if (uploadError) {
      logger.error("Error uploading image:", uploadError.message);
      return;
    }

    // Instead of storing a signed URL that expires,
    // store the file path. Later, when rendering the image,
    // we will fetch a fresh signed URL.
    const imageUrl = filePath;

    // Insert chat reply with attachment only (no text)
    const { data: replyData, error } = await supabase
      .from('chat_replies')
      .insert({
        chat_id: chatId,
        user_id: user?.id,
        sender_role: 'user',
        message: "",
        image_url: imageUrl, // storing the file path now
        allow_starting_image: allowStartingImage,
      })
      .select();

    if (error) {
      logger.error("Error sending attachment:", error.message);
      return;
    }

    if (replyData && replyData.length > 0) {
      // Reset attachment state after sending
      setImageFile(null);
      setImagePreview(null);
      setAllowStartingImage(false);
    }
  };

  // Only allow deletion if no replies exist.
  const deleteChat = async () => {
    const { error } = await supabase
      .from('chats')
      .delete()
      .eq('id', chatId);
    if (error) {
      logger.error("Error deleting chat:", error.message);
    } else {
      router.push('/dashboard');
    }
  };

  const handleDeleteReply = async (reply: any) => {
    const { error } = await supabase
      .from('chat_replies')
      .delete()
      .eq('id', reply.id);
    if (error) {
      logger.error("Error deleting reply:", error.message);
    } else {
      setMessages((prevMessages) => prevMessages.filter((m) => m.id !== reply.id));
    }
    setReplyToDelete(null);
  };

  const moveToNextPhase = async () => {
    try {
      const res = await fetch("/api/move-to-next-phase", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ chatId }),
      });
      const result = await res.json();
      if (result.error) {
        logger.error("Error moving to next phase:", result.error);
        showNotification(`Error: ${result.error}`);
      } else {
        if (result.data) {
          setChat(result.data);
          showNotification("Moved to next phase successfully");
        } else {
          showNotification("No update available for chat phase");
        }
      }
    } catch (error: any) {
      logger.error("Error moving to next phase:", error.message);
      showNotification("Error moving to next phase");
    }
  };

  const selectStartingImage = async (replyId: string) => {
    // Prevent doing an API call if the image is already marked as the starting image
    if (selectedStartingImage === replyId) {
      logger.log("This image is already selected as the starting image. No API call needed.");
      return;
    }

    try {
      const res = await fetch("/api/select-starting-image", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ chatId, replyId }),
      });
      const result = await res.json();
      if (result.error) {
        logger.error("Error updating starting image:", result.error);
      } else {
        setSelectedStartingImage(replyId);
        sendSystemStartingImageReply(replyId);
      }
    } catch (error: any) {
      logger.error("Error during starting image selection:", error.message);
    }
  };

  const sendSystemStartingImageReply = async (replyId: string) => {
    try {
      const res = await fetch("/api/send-system-starting-image-reply", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          chatId,
          replyId,
          // Optionally, you could pass in a custom message here:
          // message: `Your custom system message.`,
        }),
      });
      const result = await res.json();
      if (result.error) {
        logger.error("Error sending system starting image reply:", result.error);
      } else {
        logger.log("System starting image reply sent:", result.data);
      }
    } catch (error: any) {
      logger.error("Error during sending system reply:", error.message);
    }
  };

  // Function to move the chat to the previous phase.
  const moveToPreviousPhase = async () => {
    try {
      const res = await fetch("/api/move-to-previous-phase", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ chatId }),
      });
      const result = await res.json();
      if (result.error) {
        logger.error("Error moving to previous phase:", result.error);
        showNotification(`Error: ${result.error}`);
      } else {
        if (result.data) {
          setChat(result.data);
          showNotification("Moved to previous phase successfully");
        } else {
          showNotification("No update available for chat phase");
        }
      }
    } catch (error: any) {
      logger.error("Error moving to previous phase:", error.message);
      showNotification("Error moving to previous phase");
    }
  };

  // Subscribe to realtime updates for the current chat record
  useEffect(() => {
    if (!chatId) return;
    const chatChannel = supabase.channel('chat-phase-update')
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'chats',
        filter: `id=eq.${chatId}`
      }, (payload) => {
        logger.log("Chat updated:", payload.new);
        alert('chat phase updated');
        setChat(payload.new);
      })
      .subscribe((status) => logger.log("Chat phase subscription status:", status));

    return () => {
      supabase.removeChannel(chatChannel);
    };
  }, [chatId]);

  return (
    <>
      <Header />
      <main style={{ padding: '2rem' }}>
        {loading ? (
          <div>Loading chat...</div>
        ) : (
          <>
            <h1>Chat Conversation</h1>
            {chat && (
              <div className={styles.chatHeaderBox}>
                <div className={styles.chatHeaderRow}>
                  <div className={styles.chatId}>
                    <strong>Chat ID: </strong> {chat.id}
                  </div>
                  <div className={styles.chatPhaseNumber}>
                    {chat.phase}
                  </div>
                </div>
                <div className={styles.progressContainer}>
                  {(() => {
                    const progressItems = [];
                    for (let i = 1; i <= 5; i++) {
                      progressItems.push(
                        <div key={`phase-${i}`} className={styles.progressCircleContainer}>
                          <div 
                            className={`${styles.progressCircle} ${i <= chat.phase ? styles.active : styles.inactive}`}
                          >
                            {i}
                          </div>
                          <div className={styles.tooltip}>
                            {phaseDescriptions[i]}
                          </div>
                        </div>
                      );
                      if (i < 5) {
                        progressItems.push(
                          <div key={`line-${i}`} className={`${styles.progressLine} ${i < chat.phase ? styles.active : styles.inactive}`} />
                        );
                      }
                    }
                    return progressItems;
                  })()}
                </div>
                <div className={styles.phaseDescription}>
                  {phaseDescriptions[chat.phase]}
                </div>
                {/* Conditional sections based on the phase */}
                {chat.phase === 1 && (
                  <div className={styles.phaseSection}>
                    <p>Phase 1: Please describe what you need from the video.</p>
                    <p>Please also upload the image of your design that you want to use on the video</p>
                    <div className={styles.attachmentContainer}>
                      <button 
                        onClick={() => {
                          if (!imageFile) {
                            fileInputRef.current?.click();
                          } else {
                            sendAttachment();
                          }
                        }}
                        style={{ padding: '0.5rem 1rem', marginTop: '0.5rem' }}
                      >
                        {imageFile ? "Send Attachment" : "Upload Your Design"}
                      </button>
                      {imageFile && (
                        <div>
                          <p>Attached file:</p>
                          <p style={{ marginTop: '0.5rem' }}>{imageFile.name}</p>

                        </div>
                      )}
                      <input 
                        type="file" 
                        accept="image/*, video/*"
                        ref={fileInputRef}
                        onChange={handleFileChange}
                        style={{ display: 'none' }}
                      />
                    </div>
                  </div>
                )}
                {chat?.phase == 2 && (
                  <div className={styles.phaseSection} >
                    <p>Please wait while we process your request</p>
                    {user?.role === 'admin' && (
                      <button onClick={() => setPhase3Modal(true)} className={styles.movePhaseButton}>
                        Move to phase 3
                      </button>
                    )}
                  </div>
                )}
                {chat?.phase === 3 && (
                  <div className={styles.phaseSection}>
                    <p>Phase 3 Choose one of the available starting frame options below.</p>
                  </div>
                )}
                {chat?.phase === 4 && (
                  <div className={styles.phaseSection}>
                    <p>Phase 4: Video is being prepared for delivery.</p>
                  </div>
                )}
                {chat?.phase === 5 && (
                  <div className={styles.phaseSection}>
                    <p>Phase 5: Your video is prepared.</p>
                  </div>
                )}
              </div>
            )}
            <div className={styles.messageContainer}>
              {messages.map((message) => {
                const isOutgoing = message.user_id === user?.id;
                const hasImage = message.image_url !== null;
                // videos are also sent through image_url but they are acutally .mp4
                const hasVideo = hasImage && message.image_url.endsWith('.mp4');
                
                return (
                  <div
                    key={message.id}
                    className={classNames(styles.chatMessage, {
                      [styles.outgoingMessage]: isOutgoing,
                      [styles.incomingMessage]: !isOutgoing,
                    })}
                    style={{ position: 'relative' }}
                  >
                    {user?.role === 'admin' && (
                      <button 
                        onClick={() => setReplyToDelete(message)} 
                        className={styles.deleteReplyButton}
                      >
                        x
                      </button>
                    )}
                    <p className={styles.messageMeta}>
                      <strong>
                        {isOutgoing ? user?.email : message.user_email}
                      </strong> at {new Date(message.created_at).toLocaleString()}
                    </p>
                    {message.message && <p className={styles.messageText}>{message.message}</p>}
                    {(hasImage && !hasVideo) && (
                      chat?.phase === 3 ? (
                        <div className={styles.startingImageContainer}>
                          <SignedUrlImage 
                            filePath={message.image_url} 
                            alt="Starting Option" 
                            className={styles.startingImage}
                            onClick={() => setModalImage(message.image_url)}
                          />
                          <div className={styles.startingImageButtons}>
                            <button onClick={() => setModalImage(message.image_url)} className={styles.viewImageButton}>
                              View Image
                            </button>
                            { message.allow_starting_image && user?.role === 'user' &&  (
                              <button onClick={() => selectStartingImage(message.id)} className={styles.selectImageButton}>
                                Select Starting Image
                              </button>
                            )}
                          </div>
                          {selectedStartingImage === message.id && (
                            <span className={styles.selectedOverlay}>✓</span>
                          )}
                        </div>
                      ) : (
                        <SignedUrlImage 
                          filePath={message.image_url} 
                          alt="Attached" 
                          style={{ marginTop: '0.5rem', maxWidth: '300px', borderRadius: '4px', cursor: 'pointer' }}
                          onClick={() => setModalImage(message.image_url)}
                        />
                      )
                    )}
                    {hasVideo && (
                      <SignedUrlVideo 
                        filePath={message.image_url}
                        style={{ marginTop: '0.5rem', maxWidth: '300px', borderRadius: '4px', cursor: 'pointer' }}
                        onClick={() => setModalImage(message.image_url)}
                        controls={true}
                      />
                    )}
                  </div>
                );
              })}
            </div>
            {/* Message input field for sending text messages */}
            <div>
              <p style={{ fontStyle: 'italic', marginBottom: '0.5rem' }}>
                Writing message as {user?.email}
              </p>
              <textarea
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Type your message here..."
                style={{ width: '100%', padding: '0.5rem', minHeight: '100px' }}
              />
              <div style={{ display: 'flex', gap: '1rem', marginTop: '0.5rem' }}>
                <button onClick={sendTextMessage} style={{ padding: '0.5rem 1rem' }}>
                  Send Message
                </button>
                {user?.role === 'admin' && (
                  <button onClick={() => fileInputRef.current?.click()} style={{ padding: '0.5rem 1rem' }}>
                    Attach File
                  </button>
                )}
              </div>
              {user?.role === 'admin' && (
                <>
                  <input 
                    type="file"
                    accept="image/*, video/*"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    style={{ display: 'none' }}
                  />
                  {imageFile && imagePreview && (
                    <>
                      {imageFile.type.startsWith("video") ? (
                        <video 
                          src={imagePreview} 
                          controls
                          style={{ maxWidth: '200px', marginTop: '0.5rem', borderRadius: '4px' }}
                        />
                      ) : (
                        <img 
                          src={imagePreview} 
                          alt="Attachment preview" 
                          style={{ maxWidth: '200px', marginTop: '0.5rem', borderRadius: '4px' }}
                        />
                      )}
                    </>
                  )}
                </>
              )}
            </div>
          </>
        )}
        {replies.length === 0 && (
          <div style={{ marginBottom: '1rem' }}>
            <button
              onClick={deleteChat}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: 'red',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Delete Chat
            </button>
          </div>
        )}
        {modalImage && (
          <div className={styles.modalBackground} onClick={() => setModalImage(null)}>
            <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
              <img src={modalImage} alt="Full Size" />
              <button onClick={() => setModalImage(null)} style={{ marginTop: '1rem', padding: '0.5rem 1rem' }}>
                Close
              </button>
            </div>
          </div>
        )}

        {replyToDelete && (
          <div className={styles.modalBackground} onClick={() => setReplyToDelete(null)}>
            <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
              <p>Are you sure you want to delete this reply?</p>
              <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '1rem', marginTop: '1rem' }}>
                <button onClick={() => handleDeleteReply(replyToDelete)} style={{ padding: '0.5rem 1rem' }}>
                  Yes, Delete
                </button>
                <button onClick={() => setReplyToDelete(null)} style={{ padding: '0.5rem 1rem' }}>
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {phase3Modal && (
          <div className={styles.modalBackground} onClick={() => setPhase3Modal(false)}>
            <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
              <p>Are you sure you want to move to phase 3?</p>
              <div className={styles.modalButtonGroup}>
                <button onClick={moveToNextPhase} className={styles.confirmButton}>
                  Yes, Move
                </button>
                <button onClick={() => setPhase3Modal(false)} className={styles.cancelButton}>
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

      </main>
      {/* Render the "Move to previous phase" button (admin only) */}
      {user?.role === 'admin' && (
        <div style={{ textAlign: 'center', margin: '1rem 0' }}>
          <button onClick={moveToPreviousPhase} style={{ padding: '0.5rem 1rem', cursor: 'pointer' }}>
            Move to previous phase
          </button>
        </div>
      )}
      {user?.role === 'admin' && (
        <div style={{ textAlign: 'center', margin: '1rem 0' }}>
          <button onClick={moveToNextPhase} style={{ padding: '0.5rem 1rem', cursor: 'pointer' }}>
            Move to Next Phase
          </button>
        </div>
      )}
      <ToastContainer position="top-center" className="p-3">
        <Toast onClose={() => setNotification(null)} show={!!notification} delay={3000} autohide>
          <Toast.Body>{notification}</Toast.Body>
        </Toast>
      </ToastContainer>
    </>
  );
};

export default ChatPage; 