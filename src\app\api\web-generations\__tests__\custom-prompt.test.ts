// Tests for custom prompt functionality in Mortal Kombat video generator

describe('Custom Prompt Functionality', () => {
  describe('Prompt Validation', () => {
    it('should accept valid custom prompts', () => {
      const validPrompts = [
        "Epic action sequence with special effects",
        "Slow motion combat moves with dramatic lighting",
        "Character performing signature finishing move",
        "Dynamic camera angles during fight scene"
      ];

      validPrompts.forEach(prompt => {
        expect(typeof prompt).toBe('string');
        expect(prompt.length).toBeGreaterThan(0);
        expect(prompt.length).toBeLessThanOrEqual(500);
      });
    });

    it('should reject invalid prompts', () => {
      const invalidPrompts = [
        "", // Empty string
        "A".repeat(501), // Too long
        null,
        undefined
      ];

      invalidPrompts.forEach(prompt => {
        if (prompt === null || prompt === undefined) {
          expect(prompt).toBeFalsy();
        } else if (typeof prompt === 'string') {
          expect(prompt.length === 0 || prompt.length > 500).toBe(true);
        }
      });
    });

    it('should validate prompt length constraints', () => {
      const maxLength = 500;
      const validPrompt = "A".repeat(maxLength);
      const tooLongPrompt = "A".repeat(maxLength + 1);

      expect(validPrompt.length).toBe(maxLength);
      expect(tooLongPrompt.length).toBeGreaterThan(maxLength);
    });
  });

  describe('Default Character Prompts', () => {
    const characterPrompts = {
      'scorpion': 'A cinematic video showing Scorpion in an epic ninja battle scene with fire and chains, dynamic action poses',
      'sub-zero': 'A cinematic video showing Sub-Zero in an epic ice-powered battle scene with freezing attacks, dynamic action poses',
      'raiden': 'A cinematic video showing Raiden in an epic thunder god battle scene with lightning attacks, dynamic action poses',
      'liu-kang': 'A cinematic video showing Liu Kang in an epic martial arts battle scene with fire dragon attacks, dynamic action poses',
      'kitana': 'A cinematic video showing Kitana in an epic princess warrior battle scene with fan blade attacks, dynamic action poses',
      'johnny-cage': 'A cinematic video showing Johnny Cage in an epic Hollywood action scene with special effects and flashy moves, dynamic action poses',
      'mileena': 'A cinematic video showing Mileena in an epic savage battle scene with teeth and sai attacks, dynamic action poses',
      'kung-lao': 'A cinematic video showing Kung Lao in an epic monk warrior battle scene with razor hat attacks, dynamic action poses'
    };

    it('should have default prompts for all characters', () => {
      const allCharacters = ['scorpion', 'sub-zero', 'raiden', 'liu-kang', 'kitana', 'johnny-cage', 'mileena', 'kung-lao'];

      allCharacters.forEach(character => {
        const prompt = characterPrompts[character as keyof typeof characterPrompts];
        expect(prompt).toBeDefined();
        expect(typeof prompt).toBe('string');
        expect(prompt.length).toBeGreaterThan(0);
        expect(prompt.length).toBeLessThanOrEqual(500);
      });
    });

    it('should include character names in default prompts', () => {
      expect(characterPrompts.scorpion).toContain('Scorpion');
      expect(characterPrompts['sub-zero']).toContain('Sub-Zero');
      expect(characterPrompts.raiden).toContain('Raiden');
      expect(characterPrompts['liu-kang']).toContain('Liu Kang');
      expect(characterPrompts.kitana).toContain('Kitana');
      expect(characterPrompts['johnny-cage']).toContain('Johnny Cage');
      expect(characterPrompts.mileena).toContain('Mileena');
      expect(characterPrompts['kung-lao']).toContain('Kung Lao');
    });

    it('should include cinematic elements in default prompts', () => {
      const cinematicKeywords = ['cinematic', 'epic', 'dynamic', 'battle', 'scene'];
      
      Object.values(characterPrompts).forEach(prompt => {
        const hascinematicElement = cinematicKeywords.some(keyword => 
          prompt.toLowerCase().includes(keyword)
        );
        expect(hascinematicElement).toBe(true);
      });
    });

    it('should have unique prompts for new characters', () => {
      const newCharacters = ['johnny-cage', 'mileena', 'kung-lao'];
      const newPrompts = newCharacters.map(char => 
        characterPrompts[char as keyof typeof characterPrompts]
      );

      // Check that new character prompts are unique
      expect(newPrompts[0]).toContain('Hollywood action');
      expect(newPrompts[1]).toContain('savage battle');
      expect(newPrompts[2]).toContain('monk warrior');
    });
  });

  describe('Prompt Processing Logic', () => {
    it('should handle prompt sanitization', () => {
      const unsafePrompts = [
        "Prompt with <script>alert('xss')</script>",
        "Prompt with & ampersand",
        "Prompt with \"quotes\"",
        "Prompt with 'single quotes'"
      ];

      unsafePrompts.forEach(prompt => {
        // Basic sanitization checks
        expect(typeof prompt).toBe('string');
        expect(prompt.length).toBeGreaterThan(0);
      });
    });

    it('should trim whitespace from prompts', () => {
      const promptWithWhitespace = "  Epic battle scene  ";
      const trimmedPrompt = promptWithWhitespace.trim();
      
      expect(trimmedPrompt).toBe("Epic battle scene");
      expect(trimmedPrompt.length).toBeLessThan(promptWithWhitespace.length);
    });

    it('should handle special characters appropriately', () => {
      const promptWithSpecialChars = "Action scene with explosions! Dynamic moves & effects.";
      
      expect(typeof promptWithSpecialChars).toBe('string');
      expect(promptWithSpecialChars.includes('!')).toBe(true);
      expect(promptWithSpecialChars.includes('&')).toBe(true);
      expect(promptWithSpecialChars.includes('.')).toBe(true);
    });
  });

  describe('Custom Prompt UI Integration', () => {
    it('should support modal interface properties', () => {
      const modalProps = {
        isOpen: false,
        onClose: () => {},
        defaultPrompt: "Default prompt text",
        onSave: (prompt: string) => {},
        characterType: 'scorpion'
      };

      expect(typeof modalProps.isOpen).toBe('boolean');
      expect(typeof modalProps.onClose).toBe('function');
      expect(typeof modalProps.defaultPrompt).toBe('string');
      expect(typeof modalProps.onSave).toBe('function');
      expect(typeof modalProps.characterType).toBe('string');
    });

    it('should handle prompt reset functionality', () => {
      const customPrompt = "Custom user prompt";
      const defaultPrompt = "Default character prompt";
      
      // Simulate reset to default
      const resetPrompt = defaultPrompt;
      
      expect(resetPrompt).toBe(defaultPrompt);
      expect(resetPrompt).not.toBe(customPrompt);
    });

    it('should validate textarea constraints', () => {
      const textareaConstraints = {
        maxLength: 500,
        minLength: 1,
        placeholder: "Enter your custom video generation prompt...",
        required: false
      };

      expect(typeof textareaConstraints.maxLength).toBe('number');
      expect(typeof textareaConstraints.minLength).toBe('number');
      expect(typeof textareaConstraints.placeholder).toBe('string');
      expect(typeof textareaConstraints.required).toBe('boolean');
      expect(textareaConstraints.maxLength).toBe(500);
    });
  });

  describe('API Integration', () => {
    it('should include custom prompt in request payload', () => {
      const mockPayload = {
        character_type: 'johnny-cage',
        video_settings: {
          duration: 5,
          resolution: '720p',
          style: 'anime'
        },
        custom_prompt: 'Epic Hollywood action sequence with special effects'
      };

      expect(typeof mockPayload.custom_prompt).toBe('string');
      expect(mockPayload.custom_prompt.length).toBeGreaterThan(0);
      expect(mockPayload.custom_prompt.length).toBeLessThanOrEqual(500);
    });

    it('should handle missing custom prompt gracefully', () => {
      const payloadWithoutCustomPrompt: any = {
        character_type: 'scorpion',
        video_settings: {
          duration: 5,
          resolution: '720p',
          style: 'anime'
        }
        // custom_prompt intentionally omitted
      };

      expect(payloadWithoutCustomPrompt.custom_prompt).toBeUndefined();
      // Should fall back to default prompt for character
    });

    it('should validate custom prompt in API endpoints', () => {
      const validationRules = {
        maxLength: 500,
        required: false,
        type: 'string',
        sanitize: true
      };

      expect(validationRules.maxLength).toBe(500);
      expect(validationRules.required).toBe(false);
      expect(validationRules.type).toBe('string');
      expect(validationRules.sanitize).toBe(true);
    });
  });

  describe('Database Schema Support', () => {
    it('should support custom prompt in input_data', () => {
      const mockInputData = {
        character_type: 'mileena',
        video_settings: {
          duration: 8,
          resolution: '1080p',
          style: '3d_animation'
        },
        custom_prompt: 'Savage battle scene with teeth and sai attacks in dark arena'
      };

      expect(mockInputData).toHaveProperty('custom_prompt');
      expect(typeof mockInputData.custom_prompt).toBe('string');
      expect(mockInputData.custom_prompt.length).toBeLessThanOrEqual(500);
    });

    it('should handle custom prompt storage and retrieval', () => {
      const storedData = {
        id: 'req-123',
        input_data: {
          character_type: 'kung-lao',
          custom_prompt: 'Monk warrior with razor hat in temple setting'
        },
        output_data: {
          video_url: 'https://example.com/video.mp4',
          prompt_used: 'Monk warrior with razor hat in temple setting'
        }
      };

      expect(storedData.input_data.custom_prompt).toBeDefined();
      expect(storedData.output_data.prompt_used).toBe(storedData.input_data.custom_prompt);
    });
  });

  describe('Error Handling', () => {
    it('should handle prompt validation errors', () => {
      const validationErrors = [
        { field: 'custom_prompt', message: 'Prompt exceeds maximum length of 500 characters' },
        { field: 'custom_prompt', message: 'Prompt cannot be empty when provided' },
        { field: 'custom_prompt', message: 'Prompt contains invalid characters' }
      ];

      validationErrors.forEach(error => {
        expect(error.field).toBe('custom_prompt');
        expect(typeof error.message).toBe('string');
        expect(error.message.length).toBeGreaterThan(0);
      });
    });

    it('should provide helpful error messages', () => {
      const errorMessages = {
        tooLong: 'Custom prompt must be 500 characters or less',
        invalidChars: 'Custom prompt contains invalid characters',
        serverError: 'Failed to save custom prompt'
      };

      Object.values(errorMessages).forEach(message => {
        expect(typeof message).toBe('string');
        expect(message.length).toBeGreaterThan(0);
        expect(message.toLowerCase().includes('prompt')).toBe(true);
      });
    });
  });
}); 