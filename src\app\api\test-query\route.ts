import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabaseClient';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client specifically for server-side use with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

export async function GET(request: NextRequest) {
  try {
    // Get query parameters from the request URL
    const searchParams = request.nextUrl.searchParams;
    const telegramId = searchParams.get('telegram_id');
    
    if (!telegramId) {
      return NextResponse.json(
        { error: 'telegram_id parameter is required' }, 
        { status: 400 }
      );
    }

    // Query with regular client
    const { data: testData, error: testError } = await supabase
      .from('external_identities')
      .select('*')
      .eq('telegram_id', telegramId);
      
    // Query with admin client that uses service role
    const { data: adminData, error: adminError } = await supabaseAdmin
      .from('external_identities')
      .select('*')
      .eq('telegram_id', telegramId);
      
    // Run a broader query to see if the table has data
    const { data: allUsers, error: allUsersError } = await supabase
      .from('external_identities')
      .select('id, telegram_id')
      .limit(5);
      
    // Direct query
    return NextResponse.json({
      environment: {
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
        supabaseKeyLength: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length || 0,
        serviceKeyLength: process.env.SUPABASE_SERVICE_ROLE_KEY?.length || 0,
        nodeEnv: process.env.NODE_ENV
      },
      regularClient: {
        success: !testError,
        count: testData?.length || 0,
        found: testData && testData.length > 0,
        error: testError
      },
      adminClient: {
        success: !adminError,
        count: adminData?.length || 0,
        found: adminData && adminData.length > 0,
        data: adminData, 
        error: adminError
      },
      tableSample: {
        success: !allUsersError,
        count: allUsers?.length || 0,
        error: allUsersError
      }
    });
    
  } catch (error) {
    return NextResponse.json(
      { error: 'An unexpected error occurred', details: String(error) }, 
      { status: 500 }
    );
  }
} 