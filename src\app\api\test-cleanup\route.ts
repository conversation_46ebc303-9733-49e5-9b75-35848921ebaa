import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client using the service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// This ensures the endpoint only works in development/test environments
const isTestEnvironment = process.env.NODE_ENV !== 'production';
const TEST_API_KEY = process.env.TEST_API_KEY || 'test-api-key';

export async function POST(req: Request) {
  try {
    // Only allow in non-production environments
    if (!isTestEnvironment) {
      return NextResponse.json(
        { success: false, error: 'This endpoint is only available in test environments' },
        { status: 403 }
      );
    }
    
    // Verify the test API key
    const { searchParams } = new URL(req.url);
    const apiKey = searchParams.get('key');
    
    if (apiKey !== TEST_API_KEY) {
      return NextResponse.json(
        { success: false, error: 'Invalid API key' },
        { status: 401 }
      );
    }
    
    // Get the request body to see what to clean up
    const { tables = [], conversationPrefix = 'test-', olderThan } = await req.json();
    
    // Initialize results
    const results: Record<string, any> = {};
    
    // Cleanup Storage - delete files from test conversations
    try {
      const storageResults: Record<string, any> = {};
      
      // First, get all test conversation IDs
      const query = supabaseAdmin
        .from('bot_conversations')
        .select('id');
      
      // If a conversation prefix is provided, filter by that
      if (conversationPrefix) {
        // @ts-ignore: Supabase methods may not be properly typed
        query.ilike('provider_conversation_id', `${conversationPrefix}%`);
      }
      
      // If olderThan is provided (in hours), only delete records older than that
      if (olderThan) {
        const olderThanDate = new Date();
        olderThanDate.setHours(olderThanDate.getHours() - olderThan);
        // @ts-ignore: Supabase methods may not be properly typed
        query.lt('started_at', olderThanDate.toISOString());
      }
      
      // Get conversation IDs
      const { data: conversations, error: convError } = await query;
      
      if (convError) {
        storageResults.conversation_query = { success: false, error: convError.message };
      } else if (conversations && conversations.length > 0) {
        console.log(`Found ${conversations.length} test conversations to clean up`);
        const conversationIds = conversations.map(c => c.id);
        
        // Handle each conversation's cleanup separately to avoid foreign key constraint issues
        for (const convId of conversationIds) {
          try {
            // Step 1: Delete messages
            await supabaseAdmin
              .from('messages')
              .delete()
              .eq('conversation_id', convId);
            
            // Step 2: Delete uploads
            await supabaseAdmin
              .from('user_uploads')
              .delete()
              .eq('conversation_id', convId);
            
            // Step 3: Delete images
            await supabaseAdmin
              .from('images')
              .delete()
              .eq('conversation_id', convId);
            
            // Step 4: Delete the conversation itself
            await supabaseAdmin
              .from('bot_conversations')
              .delete()
              .eq('id', convId);
              
            console.log(`Successfully cleaned up conversation ${convId}`);
            results[`cleanup_conv_${convId}`] = { success: true };
          } catch (convCleanupError: any) {
            console.error(`Exception cleaning up conversation ${convId}: ${convCleanupError.message}`);
            results[`cleanup_conv_${convId}`] = { success: false, error: convCleanupError.message };
          }
        }
        
        // Also attempt to clean up any orphaned external identities
        if (conversationPrefix) {
          try {
            const { error: identityError } = await supabaseAdmin
              .from('external_identities')
              .delete()
              .or(`telegram_id.ilike.${conversationPrefix}%,whatsapp_id.ilike.${conversationPrefix}%`);
            
            if (identityError) {
              results.external_identities = { success: false, error: identityError.message };
            } else {
              results.external_identities = { success: true };
            }
          } catch (identityError: any) {
            results.external_identities = { success: false, error: identityError.message };
          }
        }
      } else {
        console.log('No test conversations found to clean up');
      }
      
      results.storage = storageResults;
    } catch (error: any) {
      console.error('Error in cleanup process:', error);
      results.error = error.message;
    }
    
    return NextResponse.json({
      success: true,
      message: 'Test data cleanup completed',
      results,
    });
    
  } catch (error: any) {
    console.error('Error cleaning up test data:', error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    );
  }
} 