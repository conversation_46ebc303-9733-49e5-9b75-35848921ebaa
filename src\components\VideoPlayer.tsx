import React from 'react';
import { Button } from 'react-bootstrap';

interface VideoPlayerProps {
  src: string;
  className?: string;
  showFullscreen?: boolean;
  style?: React.CSSProperties;
  hideControlsUntilHover?: boolean;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ 
  src, 
  className = "", 
  showFullscreen = true, 
  style,
  hideControlsUntilHover = false
}) => {
  const videoRef = React.useRef<HTMLVideoElement>(null);
  const [showControls, setShowControls] = React.useState(!hideControlsUntilHover);

  const openFullscreen = (videoElement: HTMLVideoElement) => {
    if (videoElement.requestFullscreen) {
      videoElement.requestFullscreen();
    } else if ((videoElement as any).webkitRequestFullscreen) {
      (videoElement as any).webkitRequestFullscreen();
    } else if ((videoElement as any).msRequestFullscreen) {
      (videoElement as any).msRequestFullscreen();
    }
  };

  return (
    <div 
      className="position-relative"
      onMouseEnter={() => hideControlsUntilHover && setShowControls(true)}
      onMouseLeave={() => hideControlsUntilHover && setShowControls(false)}
    >
      <video 
        ref={videoRef} 
        controls={showControls}
        className={`w-100 rounded ${className}`} 
        style={style}
      >
        <source src={src} type="video/mp4" />
        Your browser does not support the video tag.
      </video>
      {showFullscreen && showControls && (
        <Button
          variant="outline-light"
          size="sm"
          className="position-absolute"
          style={{ top: '10px', right: '10px', backgroundColor: 'rgba(0,0,0,0.5)' }}
          onClick={() => videoRef.current && openFullscreen(videoRef.current)}
          title="View Fullscreen"
        >
          ⛶
        </Button>
      )}
    </div>
  );
};

export default VideoPlayer; 