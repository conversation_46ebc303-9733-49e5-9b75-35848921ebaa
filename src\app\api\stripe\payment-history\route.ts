import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    // Get user ID from query parameters
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Fetch payment history with package details
    const { data: payments, error } = await supabase
      .from('payments')
      .select(`
        id,
        amount_cents,
        currency,
        status,
        tokens_granted,
        payment_method,
        created_at,
        updated_at,
        token_packages!inner (
          name,
          description,
          token_amount
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(50) // Limit to last 50 payments

    if (error) {
      console.error('Error fetching payment history:', error)
      return NextResponse.json(
        { error: 'Failed to fetch payment history' },
        { status: 500 }
      )
    }

    // Format payments for frontend
    const formattedPayments = payments.map((payment: any) => ({
      id: payment.id,
      packageName: payment.token_packages.name,
      packageDescription: payment.token_packages.description,
      expectedTokens: payment.token_packages.token_amount,
      tokensGranted: payment.tokens_granted,
      amountCents: payment.amount_cents,
      amountFormatted: formatPrice(payment.amount_cents, payment.currency),
      currency: payment.currency,
      status: payment.status,
      paymentMethod: payment.payment_method,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at
    }))

    return NextResponse.json({
      payments: formattedPayments
    })

  } catch (error) {
    console.error('Error in payment history API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function formatPrice(cents: number, currency: string): string {
  const amount = cents / 100
  return new Intl.NumberFormat('ro-RO', {
    style: 'currency',
    currency: currency
  }).format(amount)
} 