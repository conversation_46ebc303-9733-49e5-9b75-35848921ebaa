describe('Bot Upload File API', () => {
  it('should handle valid file upload data and return success response', () => {
    cy.botUploadFile().then((response) => {
      // In a real test environment, this would return a 200 response
      // Here we're just checking that the endpoint exists and handles requests
      cy.log(`Response status: ${response.status}`);
      cy.log(`Response body: ${JSON.stringify(response.body)}`);
      
      // Validate that the endpoint exists
      expect(response.status).to.not.eq(404);
    });
  });

  it('should return error for missing required fields', () => {
    // Create data with missing fields
    const { fileUrl, ...invalidData } = Cypress.env('mockBotData').uploadFile;
    
    cy.botUploadFile(invalidData).then((response) => {
      cy.log(`Response status: ${response.status}`);
      cy.log(`Response body: ${JSON.stringify(response.body)}`);
      
      // Should return a 400 Bad Request
      expect(response.status).to.eq(400);
      expect(response.body).to.have.property('success', false);
      expect(response.body).to.have.property('error').that.includes('Missing required fields');
    });
  });

  it('should handle file upload with metadata and recordAsMessage=false', () => {
    // Create data with recordAsMessage set to false
    const modifiedData = {
      ...Cypress.env('mockBotData').uploadFile,
      recordAsMessage: false
    };
    
    cy.botUploadFile(modifiedData).then((response) => {
      cy.log(`Response status: ${response.status}`);
      cy.log(`Response body: ${JSON.stringify(response.body)}`);
      
      // Validate that the endpoint exists
      expect(response.status).to.not.eq(404);
    });
  });

  it('should handle file upload without metadata', () => {
    // Create data without fileMetadata
    const { fileMetadata, ...modifiedData } = Cypress.env('mockBotData').uploadFile;
    
    cy.botUploadFile(modifiedData).then((response) => {
      cy.log(`Response status: ${response.status}`);
      cy.log(`Response body: ${JSON.stringify(response.body)}`);
      
      // Validate that the endpoint exists
      expect(response.status).to.not.eq(404);
    });
  });

  it('should handle file upload with textMessage caption', () => {
    // Create a specific test data for text message testing
    const testData = {
      conversationId: "21895339-0c97-410a-a9f1-d4f6a39ec40a", // Use a valid UUID format
      externalIdentityId: "a63f8f30-ba72-41bb-aa14-e92bf45ebe1b", // Use a valid UUID format
      fileUrl: "https://upload.wikimedia.org/wikipedia/commons/3/38/JPEG_example_JPG_RIP_001.jpg",
      fileMetadata: {
        originalFilename: "JPEG_example_JPG_RIP_001.jpg",
        mimeType: "image/jpeg",
        size: 12345
      },
      recordAsMessage: true,
      sender: "user",
      textMessage: "Custom image caption for testing"
    };
    
    cy.botUploadFile(testData).then((response) => {
      cy.log(`Response status: ${response.status}`);
      cy.log(`Response body: ${JSON.stringify(response.body)}`);
      
      // Base validations that should work in both testing and development environments
      expect(response.status).to.not.eq(404); // Endpoint exists
      
      // If we get a successful response, validate the content
      if (response.status === 200) {
        expect(response.body).to.have.property('success', true);
        expect(response.body).to.have.property('data');
        
        const responseData = response.body.data;
        
        // Verify the upload was recorded
        expect(responseData).to.have.property('uploadId');
        
        // Verify that the message was created since recordAsMessage is true
        expect(responseData).to.have.property('messageId');
        
        // Verify textMessage is returned in the response
        expect(responseData).to.have.property('textMessage', testData.textMessage);
        
        // Verify other expected properties
        expect(responseData).to.have.property('conversationId');
        expect(responseData).to.have.property('externalIdentityId');
        expect(responseData).to.have.property('fileUrl');
        expect(responseData).to.have.property('provider');
      }
    });
  });
  
  it('should handle file upload with different sender values', () => {
    // Test with sender = bot
    const modifiedData = {
      ...Cypress.env('mockBotData').uploadFile,
      sender: "bot",
      textMessage: "Image caption from bot"
    };
    
    cy.botUploadFile(modifiedData).then((response) => {
      cy.log(`Response status: ${response.status}`);
      cy.log(`Response body: ${JSON.stringify(response.body)}`);
      
      // Validate that the endpoint exists
      expect(response.status).to.not.eq(404);
    });
  });
}); 