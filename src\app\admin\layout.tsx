"use client";

import React from "react";
import Header from "@/components/Header";
import Link from "next/link";
import { usePathname } from "next/navigation";
import styles from "./admin.module.css";

export default function AdminLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  
  return (
    <>
      <Header />
      <main className={styles.container}>
        <h1>Admin Dashboard</h1>
        
        <nav className={styles.adminNav}>
          <Link
            href="/admin"
            className={`${styles.navLink} ${pathname === '/admin' ? styles.activeLink : ''}`}
          >
            Conversations
          </Link>
          <Link
            href="/admin/messages"
            className={`${styles.navLink} ${pathname === '/admin/messages' ? styles.activeLink : ''}`}
          >
            Messages
          </Link>
          <Link
            href="/admin/users"
            className={`${styles.navLink} ${pathname === '/admin/users' ? styles.activeLink : ''}`}
          >
            Users
          </Link>
        </nav>
        
        <div className={styles.adminContent}>
          {children}
        </div>
      </main>
    </>
  );
} 