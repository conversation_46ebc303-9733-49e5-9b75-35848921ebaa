// src/app/mk-free/[id]/page.tsx
import { MortalKombatVideoFlow } from '@/components/MortalKombatVideoFlow';
import { createClient } from '@supabase/supabase-js';
import { notFound } from 'next/navigation';

interface FreeGenerationPageProps {
  params: Promise<{
    id: string;
  }>;
}

async function getFreeGenerationVoucher(id: string) {
  // Create a Supabase client specifically for server-side use with service role key
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    process.env.SUPABASE_SERVICE_ROLE_KEY || ''
  );
  const { data, error } = await supabase
    .from('free_generations')
    .select('status, web_generation_request_id')
    .eq('id', id)
    .single();

  if (error || !data) {
    return null;
  }
  return data;
}

export default async function FreeMortalKombatPage({ params }: FreeGenerationPageProps) {
  const { id } = await params;
  const voucher = await getFreeGenerationVoucher(id);

  if (!voucher) {
    notFound();
  }

  return (
    <div>
      <MortalKombatVideoFlow
        isFreeGeneration={true}
        freeGenerationId={id}
        webGenerationRequestId={voucher.web_generation_request_id}
      />
    </div>
  );
}
