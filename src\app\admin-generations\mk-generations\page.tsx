"use client";

import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store';
import { supabase } from '../../../lib/supabaseClient';
import styles from '../../admin/admin.module.css';

interface MKGeneration {
  id: string;
  user_id: string;
  user_email: string;
  user_role: string;
  status: string;
  character: string;
  created_at: string;
  original_photo_url: string | null;
  generated_photo_url: string | null;
  is_public?: boolean;
}

export default function AdminMKGenerations() {
  const { user } = useSelector((state: RootState) => state.user);
  const [data, setData] = useState<MKGeneration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFailed, setShowFailed] = useState(false);

  useEffect(() => {
    fetchMKGenerations();
  }, []);

  const fetchMKGenerations = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        setError('Authentication required');
        return;
      }
      const response = await fetch('/api/admin/mk-generations', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${await response.text()}`);
      }
      const result = await response.json();
      setData(result.requests);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch Mortal Kombat generations');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const handleTogglePublic = async (gen: MKGeneration) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) return;
      // Optimistic update
      setData(prev => prev.map(g => g.id === gen.id ? { ...g, is_public: !gen.is_public } : g));
      await fetch('/api/admin/mk-generations', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({ id: gen.id, is_public: !gen.is_public })
      });
    } catch (err) {
      // Optionally show error or revert
      setData(prev => prev.map(g => g.id === gen.id ? { ...g, is_public: gen.is_public } : g));
    }
  };

  if (user?.role !== 'admin') {
    return (
      <div className={styles.errorMessage}>
        <h2>Access Denied</h2>
        <p>Admin access required to view this page.</p>
      </div>
    );
  }

  if (loading) {
    return <div className={styles.loading}>Loading Mortal Kombat generations...</div>;
  }

  if (error) {
    return (
      <div className={styles.errorMessage}>
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={fetchMKGenerations} className={styles.retryButton}>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={styles.adminPage}>
      <h2>Mortal Kombat Generations</h2>
      <div style={{ marginBottom: 16 }}>
        <label style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <input
            type="checkbox"
            checked={showFailed}
            onChange={e => setShowFailed(e.target.checked)}
            style={{ marginRight: 8 }}
          />
          Show Failed
        </label>
      </div>
      <div className={styles.tableContainer}>
        <table className={styles.adminTable}>
          <thead>
            <tr>
              <th>User</th>
              <th>Character</th>
              <th>Status</th>
              <th>Created</th>
              <th>Original Photo</th>
              <th>Generated Photo</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {data
              .filter(gen => showFailed || gen.status !== 'failed')
              .map((gen) => (
                <tr key={gen.id}>
                  <td>
                    <div className={styles.userInfo}>
                      <div>{gen.user_email}</div>
                      <small style={{ color: '#666' }}>{gen.user_role}</small>
                    </div>
                  </td>
                  <td>{gen.character}</td>
                  <td>{gen.status}</td>
                  <td><small>{formatDate(gen.created_at)}</small></td>
                  <td>
                    {gen.original_photo_url ? (
                      <img src={gen.original_photo_url} alt="Original" style={{ maxWidth: 80, borderRadius: 8 }} />
                    ) : (
                      <span style={{ color: '#999' }}>-</span>
                    )}
                  </td>
                  <td>
                    {gen.generated_photo_url ? (
                      <img src={gen.generated_photo_url} alt="Generated" style={{ maxWidth: 80, borderRadius: 8 }} />
                    ) : (
                      <span style={{ color: '#999' }}>-</span>
                    )}
                  </td>
                  <td>
                    <div className={styles.actionButtons}>
                      <button 
                        onClick={() => window.open(`/mortal-kombat-video?id=${gen.id}`, '_blank')}
                        className={styles.actionButton}
                        title="View Details"
                      >
                        👁️
                      </button>
                      <button 
                        onClick={() => navigator.clipboard.writeText(gen.id)}
                        className={styles.actionButton}
                        title="Copy ID"
                      >
                        📋
                      </button>
                      <button
                        onClick={() => handleTogglePublic(gen)}
                        className={styles.actionButton}
                        title={gen.is_public ? 'Set Private' : 'Set Public'}
                      >
                        {gen.is_public ? '🌟' : '⭐'}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
          </tbody>
        </table>
      </div>
      {data.length === 0 && (
        <div className={styles.emptyState}>
          <p>No Mortal Kombat generations found.</p>
        </div>
      )}
    </div>
  );
} 