import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Use service role client for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    // Get user ID from the Authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Fetch web generation requests for this user
    const { data: requests, error } = await supabaseAdmin
      .from('web_generation_requests')
      .select(`
        *,
        web_generation_steps(step_number, status, created_at),
        web_videos(id, video_url, thumbnail_url)
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching web generation requests:', error);
      return NextResponse.json({ error: 'Failed to fetch requests' }, { status: 500 });
    }

    console.log(`Fetched ${requests?.length || 0} requests for user ${user.id}`);

    // Process the data to get the latest step for each request
    const processedRequests = requests?.map(request => {
      // Get the latest completed step
      const steps = request.web_generation_steps || [];
      const completedSteps = steps.filter((step: any) => step.status === 'completed');
      const latestStep = completedSteps.length > 0 ? 
        Math.max(...completedSteps.map((step: any) => step.step_number)) : 0;
      
      return {
        id: request.id,
        flow_type: request.flow_type,
        status: request.status,
        character: request.input_data?.character_type || request.character,
        total_cost: parseFloat(request.actual_cost) || 0,
        created_at: request.created_at,
        updated_at: request.updated_at,
        current_step: request.current_step,
        latest_completed_step: latestStep,
        has_video: request.web_videos && request.web_videos.length > 0,
        video_url: request.web_videos?.[0]?.video_url,
        thumbnail_url: request.web_videos?.[0]?.thumbnail_url
      };
    }) || [];

    console.log(`Returning ${processedRequests.length} processed requests`);

    return NextResponse.json({ requests: processedRequests });

  } catch (error) {
    console.error('Error in web generations list API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 