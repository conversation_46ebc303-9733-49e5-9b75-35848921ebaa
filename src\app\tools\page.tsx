"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import Header from '@/components/Header';
import Button from 'react-bootstrap/Button';
import ToolCard from './ToolCard';
import styles from './page.module.css';
import { useTranslation } from '@/hooks/useTranslation';

export default function ToolsPage() {
  const router = useRouter();
  const { user } = useSelector((state: RootState) => state.user);
  const [lang, setLang] = useState<'en' | 'ro'>('ro');
  const { t } = useTranslation(lang);

  // Redirect non-admin users
  if (user && user.role !== 'admin') {
    return (
      <div>
        <Header />
        <main className={styles.container}>
          <h1>Access Denied</h1>
          <p>Admin access required to view this page.</p>
          <Button onClick={() => router.push('/dashboard')} variant="primary">
            Back to Dashboard
          </Button>
        </main>
      </div>
    );
  }

  const tools = [
    {
      id: 'mortal-kombat-video',
      title: t('mortal_kombat_tool_title'),
      description: t('mortal_kombat_tool_desc'),
      icon: '⚔️',
      route: '/mortal-kombat-video',
      category: t('mortal_kombat_tool_category'),
      status: t('mortal_kombat_tool_status'),
      features: t('mortal_kombat_tool_features'),
    },
    {
      id: 'image-to-video',
      title: t('image_to_video_tool_title'),
      description: t('image_to_video_tool_desc'),
      icon: '🎬',
      route: '/image-to-video',
      category: t('image_to_video_tool_category'),
      status: t('image_to_video_tool_status'),
      features: t('image_to_video_tool_features'),
    },
    {
      id: 'image-editor',
      title: t('image_editor_tool_title'),
      description: t('image_editor_tool_desc'),
      icon: '🎨',
      route: '/image-editor',
      category: t('image_editor_tool_category'),
      status: t('image_editor_tool_status'),
      features: t('image_editor_tool_features'),
    },
    {
      id: 'logo-generation',
      title: t('logo_generation_tool_title'),
      description: t('logo_generation_tool_desc'),
      icon: '🎯',
      route: '/logo-generation',
      category: t('logo_generation_tool_category'),
      status: t('logo_generation_tool_status'),
      features: t('logo_generation_tool_features'),
    }
  ];

  const handleToolClick = (route: string) => {
    router.push(route);
  };

  return (
    <div>
      <Header />
      <main className={styles.container}>
        <div className={styles.header}>
          <h1>{t('admin_tools_title')}</h1>
          <p>{t('admin_tools_subtitle')}</p>
          <div style={{ marginTop: 16 }}>
            <select value={lang} onChange={e => setLang(e.target.value as 'en' | 'ro')}>
              <option value="ro">Română</option>
              <option value="en">English</option>
            </select>
          </div>
        </div>

        <div className={styles.toolsGrid}>
          {tools.map((tool) => (
            <ToolCard
              key={tool.id}
              icon={tool.icon}
              title={tool.title}
              category={tool.category}
              status={tool.status}
              description={tool.description}
              features={tool.features}
              onLaunch={() => handleToolClick(tool.route)}
              lang={lang}
              t={t}
            />
          ))}
        </div>

        <div className={styles.comingSoon}>
          <h2>{t('coming_soon')}</h2>
          <div className={styles.futureTool}>
            <div className={styles.toolIcon}>🎬</div>
            <div>
              <h3>{t('advanced_video_editor')}</h3>
              <p>{t('advanced_video_editor_desc')}</p>
            </div>
          </div>
          <div className={styles.futureTool}>
            <div className={styles.toolIcon}>🔊</div>
            <div>
              <h3>{t('voice_synthesis_tool')}</h3>
              <p>{t('voice_synthesis_tool_desc')}</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
} 