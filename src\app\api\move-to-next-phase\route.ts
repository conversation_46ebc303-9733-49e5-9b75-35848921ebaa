import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client using the service role key (kept secret)
const supabaseServiceRole = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: Request) {
  try {
    const { chatId } = await request.json();
    if (!chatId) {
      return NextResponse.json({ error: "chatId is required" }, { status: 400 });
    }

    // Fetch the current chat record (we only need the phase)
    const { data: chatData, error: chatError } = await supabaseServiceRole
      .from("chats")
      .select("phase")
      .eq("id", chatId)
      .single();

    if (chatError || !chatData) {
      return NextResponse.json(
        { error: chatError?.message || "Chat not found" },
        { status: 500 }
      );
    }

    // Calculate the next phase (increment the current phase; clamp at 5)
    const currentPhase = chatData.phase || 1;
    const newPhase = currentPhase < 5 ? currentPhase + 1 : currentPhase;

    // Update the chat record with the new phase.
    const { data: updatedChat, error: updateError } = await supabaseServiceRole
      .from("chats")
      .update({ phase: newPhase })
      .eq("id", chatId)
      .select()
      .single();

    if (updateError) {
      return NextResponse.json(
        { error: updateError.message },
        { status: 500 }
      );
    }

    // Insert a system reply notifying the user.
    const systemMsg =
      "Chat has been moved to next phase successfully.";
    const { error: sysMsgError } = await supabaseServiceRole
      .from("chat_replies")
      .insert({
        chat_id: chatId,
        sender_role: "system",
        message: systemMsg,
      })
      .select()
      .single();

    if (sysMsgError) {
      return NextResponse.json(
        { error: sysMsgError.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true, data: updatedChat });
  } catch (err: any) {
    return NextResponse.json({ error: err.message }, { status: 500 });
  }
} 