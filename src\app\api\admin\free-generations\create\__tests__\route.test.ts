import { POST } from '../route';
import { NextRequest } from 'next/server';

// Mock the Supabase client
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(),
}));

import { createClient } from '@supabase/supabase-js';

const mockCreateClient = createClient as jest.Mock;

describe('POST /api/admin/free-generations/create', () => {
  let fromMock: jest.Mock;
  let insertMock: jest.Mock;
  let selectMock: jest.Mock;
  let singleMock: jest.Mock;

  beforeEach(() => {
    // Reset mocks before each test
    fromMock = jest.fn();
    insertMock = jest.fn();
    selectMock = jest.fn();
    singleMock = jest.fn();

    // Chain the mocks
    fromMock.mockReturnValue({ insert: insertMock });
    insertMock.mockReturnValue({ select: selectMock });
    selectMock.mockReturnValue({ single: singleMock });

    mockCreateClient.mockReturnValue({
      from: fromMock,
    });

    // Mock environment variables
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
    process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key';
    process.env.NEXT_PUBLIC_BASE_URL = 'https://aivis.ro';
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should create a free generation link successfully', async () => {
    const mockId = 'a-unique-uuid-12345';
    singleMock.mockResolvedValueOnce({
      data: { id: mockId },
      error: null,
    });

    const response = await POST();
    const body = await response.json();

    expect(response.status).toBe(200);
    expect(body.message).toBe('Free generation link created successfully.');
    expect(body.freeGenerationId).toBe(mockId);
    expect(body.freeLink).toBe(`https://aivis.ro/mk-free/${mockId}`);

    // Verify Supabase calls
    expect(mockCreateClient).toHaveBeenCalledWith(
      'https://test.supabase.co',
      'test-service-role-key'
    );
    expect(fromMock).toHaveBeenCalledWith('free_generations');
    expect(insertMock).toHaveBeenCalledWith([{ flow_type: 'mortal_kombat', status: 'new' }]);
    expect(selectMock).toHaveBeenCalledWith('id');
    expect(singleMock).toHaveBeenCalled();
  });

  it('should return a 500 error if Supabase fails to insert', async () => {
    const mockError = { message: 'Database error', code: '500' };
    singleMock.mockResolvedValueOnce({
      data: null,
      error: mockError,
    });

    const response = await POST();
    const body = await response.json();

    expect(response.status).toBe(500);
    expect(body.error).toBe('Failed to create free generation link.');

    // Verify Supabase calls
    expect(fromMock).toHaveBeenCalledWith('free_generations');
    expect(insertMock).toHaveBeenCalledWith([{ flow_type: 'mortal_kombat', status: 'new' }]);
  });

  it('should handle unexpected errors gracefully', async () => {
    // Make the mock throw an unexpected error
    fromMock.mockImplementation(() => {
      throw new Error('Unexpected server crash');
    });

    const response = await POST();
    const body = await response.json();

    expect(response.status).toBe(500);
    expect(body.error).toBe('An unexpected error occurred.');
  });

  it('should use fallback base URL when NEXT_PUBLIC_BASE_URL is not set', async () => {
    const originalEnv = process.env;
    process.env = { ...originalEnv, NODE_ENV: 'production' };
    delete process.env.NEXT_PUBLIC_BASE_URL;

    const mockId = 'a-unique-uuid-12345';
    singleMock.mockResolvedValueOnce({
      data: { id: mockId },
      error: null,
    });

    const response = await POST();
    const body = await response.json();

    expect(response.status).toBe(200);
    expect(body.freeLink).toBe(`https://aivis.ro/mk-free/${mockId}`);
    
    process.env = originalEnv;
  });

  it('should use localhost URL in development', async () => {
    const originalEnv = process.env;
    process.env = { ...originalEnv, NODE_ENV: 'development' };
    delete process.env.NEXT_PUBLIC_BASE_URL;

    const mockId = 'a-unique-uuid-12345';
    singleMock.mockResolvedValueOnce({
      data: { id: mockId },
      error: null,
    });

    const response = await POST();
    const body = await response.json();

    expect(response.status).toBe(200);
    expect(body.freeLink).toBe(`http://localhost:3000/mk-free/${mockId}`);
    
    process.env = originalEnv;
  });
});
