import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import Stripe from 'stripe'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
})

export async function POST(request: NextRequest) {
  console.log('🔵 CREATE_CHECKOUT: Request received')
  console.log('🔵 CREATE_CHECKOUT: Stripe secret key starts with:', process.env.STRIPE_SECRET_KEY?.substring(0, 12))
  console.log('🔵 CREATE_CHECKOUT: Environment APP_URL:', process.env.NEXT_PUBLIC_APP_URL)
  
  try {
    const { packageId, userId } = await request.json()
    console.log('🔵 CREATE_CHECKOUT: Package ID:', packageId, 'User ID:', userId)

    if (!packageId || !userId) {
      return NextResponse.json(
        { error: 'Package ID and User ID are required' },
        { status: 400 }
      )
    }

    // Get the token package details
    const { data: tokenPackage, error: packageError } = await supabase
      .from('token_packages')
      .select('*')
      .eq('id', packageId)
      .eq('is_active', true)
      .single()

    if (packageError || !tokenPackage) {
      console.error('❌ CREATE_CHECKOUT: Package not found:', packageError)
      return NextResponse.json(
        { error: 'Token package not found or inactive' },
        { status: 404 }
      )
    }

    console.log('✅ CREATE_CHECKOUT: Package found:', tokenPackage.name, tokenPackage.price_cents)

    // Get user details for customer creation
    const { data: { user }, error: userError } = await supabase.auth.admin.getUserById(userId)
    
    if (userError || !user) {
      console.error('❌ CREATE_CHECKOUT: User not found:', userError)
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    console.log('✅ CREATE_CHECKOUT: User found:', user.email)

    // Create or get Stripe customer
    let customerId: string
    
    // Check if customer already exists
    const customers = await stripe.customers.list({
      email: user.email,
      limit: 1
    })

    if (customers.data.length > 0) {
      customerId = customers.data[0].id
      console.log('✅ CREATE_CHECKOUT: Existing customer found:', customerId)
    } else {
      // Create new customer
      const customer = await stripe.customers.create({
        email: user.email,
        metadata: {
          userId: userId
        }
      })
      customerId = customer.id
      console.log('✅ CREATE_CHECKOUT: New customer created:', customerId)
    }

    // Create payment record in pending state
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .insert({
        user_id: userId,
        token_package_id: packageId,
        amount_cents: tokenPackage.price_cents,
        currency: tokenPackage.currency,
        status: 'pending',
        stripe_customer_id: customerId
      })
      .select()
      .single()

    if (paymentError) {
      console.error('❌ CREATE_CHECKOUT: Failed to create payment record:', paymentError)
      return NextResponse.json(
        { error: 'Failed to create payment record' },
        { status: 500 }
      )
    }

    console.log('✅ CREATE_CHECKOUT: Payment record created:', payment.id)

    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: tokenPackage.currency.toLowerCase(),
            product_data: {
              name: tokenPackage.name,
              description: `${tokenPackage.description} - ${tokenPackage.token_amount} tokens`,
            },
            unit_amount: tokenPackage.price_cents,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/payment-success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/tokens`,
      metadata: {
        paymentId: payment.id,
        userId: userId,
        packageId: packageId
      }
    })

    console.log('✅ CREATE_CHECKOUT: Stripe session created:', session.id)
    console.log('🔵 CREATE_CHECKOUT: Success URL will be:', `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/payment-success?session_id=${session.id}`)

    // Update payment record with session ID
    await supabase
      .from('payments')
      .update({
        stripe_session_id: session.id,
        status: 'processing'
      })
      .eq('id', payment.id)

    console.log('✅ CREATE_CHECKOUT: Payment updated with session ID')

    return NextResponse.json({
      sessionId: session.id,
      url: session.url,
      paymentId: payment.id
    })

  } catch (error) {
    console.error('❌ CREATE_CHECKOUT: Error creating checkout session:', error)
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    )
  }
} 