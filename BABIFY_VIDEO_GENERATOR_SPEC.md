# Babify Video Generator - Feature Specification

## Overview
A video transformation tool that takes user-uploaded videos, extracts the first frame, transforms the person in that frame into a "baby" version using AI, then creates a new video combining the babified image with the original audio using HEDRA's video generation API.

## User Flow

### Complete Process Flow
```
Upload Video → Extract Frame → Babify Image → Extract Audio → Generate Video → Download Result
```

### Detailed Steps
1. **Video Upload**: User uploads a video file (max 100MB)
2. **Frame Extraction**: System extracts first frame as image
3. **AI Babification**: Image processed through FAL AI's gpt-image-1 to create baby version (customizable prompt)
4. **Audio Extraction**: Audio track extracted from original video as MP3
5. **HEDRA Generation**: Babified image + audio sent to HEDRA for video generation (16:9 default, user selectable)
6. **Result Delivery**: User receives final babified video

## Technical Implementation

### Database Schema

#### New Table: `babify_requests`
```sql
CREATE TABLE babify_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  wallet_id UUID REFERENCES user_wallets(id),
  status VARCHAR(50) DEFAULT 'pending', -- pending, processing_frame, processing_image, processing_audio, generating_video, completed, failed
  current_step INTEGER DEFAULT 1, -- 1: upload, 2: processing, 3: completed
  
  -- Input data
  original_video_url TEXT NOT NULL,
  original_video_duration DECIMAL,
  original_video_size INTEGER, -- bytes
  custom_prompt TEXT, -- User's custom babification prompt
  aspect_ratio VARCHAR(10) DEFAULT '16:9', -- 1:1, 16:9, or 9:16
  
  -- Processing data
  extracted_frame_url TEXT, -- First frame image
  babified_image_url TEXT, -- AI processed baby image
  extracted_audio_url TEXT, -- Extracted MP3 audio
  
  -- HEDRA integration
  hedra_job_id TEXT, -- HEDRA generation job ID
  hedra_video_url TEXT, -- Final generated video
  
  -- Metadata
  estimated_cost DECIMAL DEFAULT 0,
  actual_cost DECIMAL DEFAULT 0,
  token_cost INTEGER DEFAULT 0,
  processing_time INTEGER, -- seconds
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);
```

#### New Table: `babify_steps`
```sql
CREATE TABLE babify_steps (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  babify_request_id UUID REFERENCES babify_requests(id) ON DELETE CASCADE,
  step_number INTEGER NOT NULL,
  step_type VARCHAR(100) NOT NULL, -- frame_extraction, image_babification, audio_extraction, video_generation
  status VARCHAR(50) DEFAULT 'pending', -- pending, processing, completed, failed
  
  -- External service data
  fal_request_id TEXT, -- For babification step
  hedra_request_id TEXT, -- For video generation step
  
  input_data JSONB DEFAULT '{}',
  output_data JSONB DEFAULT '{}',
  error_message TEXT,
  
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### API Endpoints

#### Core Babify Flow
```typescript
// Start babify process
POST /api/babify-video/start
Body: {
  video_file: File, // Multipart form data (max 100MB)
  custom_prompt?: string, // Optional custom babification prompt
  aspect_ratio?: "1:1" | "16:9" | "9:16" // Default: "16:9"
}
Response: {
  request_id: string,
  status: 'pending' | 'processing',
  estimated_cost: 50, // tokens
  estimated_time: number // seconds (2-5 minutes)
}

// Get babify request status
GET /api/babify-video/:requestId
Response: {
  request_id: string,
  status: string,
  current_step: number,
  progress: {
    frame_extraction: 'pending' | 'processing' | 'completed' | 'failed',
    image_babification: 'pending' | 'processing' | 'completed' | 'failed',
    audio_extraction: 'pending' | 'processing' | 'completed' | 'failed',
    video_generation: 'pending' | 'processing' | 'completed' | 'failed'
  },
  result?: {
    original_video_url: string,
    babified_video_url: string,
    preview_image_url: string
  }
}

// Download result
GET /api/babify-video/:requestId/download
Response: Video file stream
```

#### Processing Steps APIs
```typescript
// Extract frame from video
POST /api/babify-video/:requestId/extract-frame
Body: {
  frame_time?: number // seconds, default: 0 (first frame)
}

// Process babification
POST /api/babify-video/:requestId/babify-image

// Extract audio
POST /api/babify-video/:requestId/extract-audio

// Generate final video with HEDRA
POST /api/babify-video/:requestId/generate-video
```

### File Structure
```
src/app/babify-video/
├── page.tsx                 # Main babify video page
├── components/
│   ├── VideoUpload.tsx      # Video file upload component
│   ├── ProcessingSteps.tsx  # Step-by-step progress display
│   ├── VideoPreview.tsx     # Original vs babified preview
│   ├── ResultDisplay.tsx    # Final result with download
│   └── ErrorHandler.tsx     # Error states and retry options
├── hooks/
│   ├── useBabifyFlow.ts     # Main flow state management
│   ├── useVideoProcessing.ts # Video processing utilities
│   └── usePolling.ts        # Status polling for long processes
├── types/
│   └── babify.ts           # TypeScript definitions
└── utils/
    ├── videoUtils.ts       # Video manipulation helpers
    ├── hedraApi.ts         # HEDRA API integration
    └── falApi.ts           # FAL AI API integration
```

### Third-Party API Integrations

#### FAL AI Integration (Image Babification)
```typescript
// Using gpt-image-1 for babification with customizable prompt
const DEFAULT_BABIFY_PROMPT = "Transform this person into an adorable baby version while keeping their main facial features and characteristics. Make them look like a cute, chubby baby with big eyes and soft features.";

const babifyImage = async (imageUrl: string, customPrompt?: string) => {
  const response = await fetch('https://fal.run/fal-ai/gpt-image-1', {
    method: 'POST',
    headers: {
      'Authorization': `Key ${process.env.FAL_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      prompt: customPrompt || DEFAULT_BABIFY_PROMPT,
      image_url: imageUrl,
      image_size: "square_hd",
      num_inference_steps: 28,
      guidance_scale: 3.5,
      num_images: 1,
      safety_tolerance: 2
    })
  });
  
  return response.json();
};
```

#### HEDRA API Integration (Video Generation)
Based on the [HEDRA API documentation](https://api.hedra.com/web-app/redoc?_gl=1*1x8egr0*_gcl_au*NDAxMzcxNjY5LjE3NDg5NzgwOTI.*_ga*MTYwMjI5MDUwMS4xNzQ4OTc4MDg4*_ga_TY6P58BJ9J*czE3NDk0NzgxMjckbzckZzEkdDE3NDk0NzgzNTEkajE3JGwwJGgxMzU1MDcwNjk4#tag/Public/operation/generate_asset_public_generations_post):

```typescript
// Generate video using HEDRA with aspect ratio selection
const generateHedraVideo = async (
  imageUrl: string, 
  audioUrl: string, 
  aspectRatio: "1:1" | "16:9" | "9:16" = "16:9"
) => {
  const response = await fetch('https://api.hedra.com/public/generations', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.HEDRA_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      character_image_url: imageUrl,  // Babified image
      voice_file_url: audioUrl,       // Extracted MP3 audio
      aspect_ratio: aspectRatio,      // 16:9 default, user selectable
      output_image_ratio: 0.8,        // Image scaling
      quality: "high"                 // or "medium", "low"
    })
  });
  
  return response.json();
};

// Check HEDRA job status
const checkHedraStatus = async (jobId: string) => {
  const response = await fetch(`https://api.hedra.com/public/generations/${jobId}`, {
    headers: {
      'Authorization': `Bearer ${process.env.HEDRA_API_KEY}`
    }
  });
  
  return response.json();
};
```

### Video Processing Pipeline

#### 1. Frame Extraction (Server-side with FFmpeg)
```bash
# Extract first frame as high-quality image
ffmpeg -i input_video.mp4 -vf "select=eq(n\,0)" -vsync vfr -q:v 2 first_frame.jpg

# Future: Extract frame at specific time
ffmpeg -i input_video.mp4 -ss 00:00:10 -vframes 1 -q:v 2 frame_at_10s.jpg
```

#### 2. Audio Extraction
```bash
# Extract audio as MP3
ffmpeg -i input_video.mp4 -vn -ar 44100 -ac 2 -b:a 192k extracted_audio.mp3

# Alternative: Extract as WAV for higher quality
ffmpeg -i input_video.mp4 -vn -ar 44100 -ac 2 extracted_audio.wav
```

#### 3. Processing Queue System
```typescript
interface BabifyJob {
  requestId: string;
  step: 'frame_extraction' | 'image_babification' | 'audio_extraction' | 'video_generation';
  inputData: {
    videoUrl?: string;
    imageUrl?: string;
    audioUrl?: string;
  };
  outputData?: any;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  retryCount: number;
  createdAt: Date;
}

// Queue processor for handling async operations
class BabifyQueueProcessor {
  async processJob(job: BabifyJob) {
    switch (job.step) {
      case 'frame_extraction':
        return await this.extractFrame(job.inputData.videoUrl);
      case 'image_babification':
        return await this.babifyImage(job.inputData.imageUrl);
      case 'audio_extraction':
        return await this.extractAudio(job.inputData.videoUrl);
      case 'video_generation':
        return await this.generateVideo(job.inputData.imageUrl, job.inputData.audioUrl);
    }
  }
}
```

## User Interface Design

### Main Page Layout
```
┌─────────────────────────────────────────────────────────────┐
│ 🍼 Babify Video Generator                                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│         📁 Drop Video Here or Click to Upload              │
│            (MP4, MOV, AVI supported - Max 100MB)           │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ Settings:                                                   │
│ Babify Prompt: [Default baby transformation...] [Custom]   │
│ Video Format:  ( ) 1:1 Square  (•) 16:9 Landscape  ( ) 9:16 Portrait │
│ Cost: 50 tokens ($0.50)                                    │
├─────────────────────────────────────────────────────────────┤
│ Processing Steps:                                           │
│ ✅ 1. Extract Frame                                         │
│ 🔄 2. Babify Character                                      │
│ ⏳ 3. Extract Audio                                         │
│ ⏳ 4. Generate Video                                        │
├─────────────────────────────────────────────────────────────┤
│ Original Video Preview    |    Babified Result              │
│ [    video player    ]    |    [   preview image   ]       │
└─────────────────────────────────────────────────────────────┘
```

### Component Structure
```typescript
// Main page component
const BabifyVideoPage = () => {
  const [uploadedVideo, setUploadedVideo] = useState<File | null>(null);
  const [requestId, setRequestId] = useState<string | null>(null);
  const { status, progress, result } = useBabifyFlow(requestId);

  return (
    <div className="container py-5">
      <Header />
      
      {!uploadedVideo ? (
        <VideoUpload onUpload={handleVideoUpload} />
      ) : (
        <>
          <ProcessingSteps progress={progress} />
          <div className="row">
            <div className="col-md-6">
              <VideoPreview video={uploadedVideo} />
            </div>
            <div className="col-md-6">
              {result ? (
                <ResultDisplay result={result} />
              ) : (
                <ProcessingIndicator status={status} />
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};
```

## Pricing Structure

### Token Costs
```typescript
const BABIFY_COSTS = {
  frame_extraction: 2,    // $0.02
  image_babification: 15, // $0.15 (FAL AI gpt-image-1)
  audio_extraction: 3,    // $0.03
  video_generation: 30,   // $0.30 (HEDRA API)
  total: 50               // $0.50 total
};
```

### Cost Calculation
- **Frame Extraction**: 2 tokens ($0.02)
- **Image Babification**: 15 tokens ($0.15) - FAL AI processing
- **Audio Extraction**: 3 tokens ($0.03)
- **Video Generation**: 30 tokens ($0.30) - HEDRA API
- **Total Cost**: 50 tokens ($0.50 per video)

## Error Handling

### Common Error Scenarios
```typescript
const ERROR_TYPES = {
  INVALID_VIDEO_FORMAT: 'Video format not supported',
  VIDEO_TOO_LARGE: 'Video file exceeds size limit (100MB)',
  VIDEO_TOO_SHORT: 'Video must be at least 1 second long',
  FRAME_EXTRACTION_FAILED: 'Failed to extract frame from video',
  BABIFICATION_FAILED: 'AI babification process failed',
  AUDIO_EXTRACTION_FAILED: 'Failed to extract audio from video',
  HEDRA_API_ERROR: 'Video generation service unavailable',
  INSUFFICIENT_TOKENS: 'Insufficient token balance',
  PROCESSING_TIMEOUT: 'Processing took too long, please try again'
};

// Retry logic for failed operations
const retryFailedStep = async (requestId: string, step: string) => {
  const maxRetries = 3;
  let retryCount = 0;
  
  while (retryCount < maxRetries) {
    try {
      await processStep(requestId, step);
      break;
    } catch (error) {
      retryCount++;
      if (retryCount === maxRetries) {
        throw new Error(`Failed after ${maxRetries} attempts: ${error.message}`);
      }
      await wait(2000 * retryCount); // Exponential backoff
    }
  }
};
```

## Performance Considerations

### File Size Limits
- **Video Upload**: Max 100MB
- **Video Duration**: Max 60 seconds
- **Supported Formats**: MP4, MOV, AVI, WebM
- **Audio Output**: MP3 format, 44.1kHz, 192kbps

### Processing Optimization
- **Async Processing**: All steps run in background queues
- **Progress Updates**: Real-time progress via WebSocket or polling
- **Caching**: Cache extracted frames and audio for retries
- **CDN Storage**: Store all media files on CDN for fast access

### Expected Processing Times
- **Frame Extraction**: 5-15 seconds
- **Image Babification**: 30-60 seconds (FAL AI)
- **Audio Extraction**: 10-30 seconds
- **Video Generation**: 60-180 seconds (HEDRA)
- **Total Time**: 2-5 minutes per video

## Security & Privacy

### Data Protection
- **Temporary Storage**: Delete original videos after 24 hours
- **Secure Upload**: Validate file types and scan for malware
- **Access Control**: Users can only access their own babify requests
- **API Security**: Rate limiting and authentication for all endpoints

### Content Moderation
- **Safe Content**: Reject videos with inappropriate content
- **Face Detection**: Ensure human faces are present for babification
- **Age Restrictions**: Terms of service compliance for user-generated content

## Future Enhancements

### Phase 2 Features
- [ ] **Frame Selection**: Allow users to choose which frame to babify
- [ ] **Multiple Faces**: Detect and babify multiple people in the frame
- [ ] **Babification Intensity**: Slider to control how "baby-like" the transformation is
- [ ] **Voice Modification**: Optional baby voice filter for audio
- [ ] **Batch Processing**: Upload multiple videos at once

### Phase 3 Features
- [ ] **3D Babification**: More advanced 3D character transformation
- [ ] **Animation**: Add subtle animation to the babified character
- [ ] **Background Replacement**: Change background to baby-themed scenes
- [ ] **Social Sharing**: Direct sharing to social media platforms
- [ ] **Templates**: Pre-made baby-themed video templates

## Integration with Existing System

### Database Connections
```sql
-- Link to existing user system
ALTER TABLE babify_requests 
ADD CONSTRAINT fk_babify_user 
FOREIGN KEY (user_id) REFERENCES auth.users(id);

-- Link to wallet system for token deduction
ALTER TABLE babify_requests 
ADD CONSTRAINT fk_babify_wallet 
FOREIGN KEY (wallet_id) REFERENCES user_wallets(id);
```

### Navigation Updates
```typescript
// Add to main navigation
const navigationItems = [
  { href: '/mortal-kombat-video', label: '⚔️ Mortal Kombat Generator' },
  { href: '/babify-video', label: '🍼 Babify Video Generator' }, // NEW
  // ... other items
];
```

### Admin Dashboard Integration
```typescript
// Add babify requests to admin dashboard
const adminSections = [
  { href: '/admin/multi-step-requests', label: 'Multi Step Requests' },
  { href: '/admin/video-requests', label: 'Video Requests' },
  { href: '/admin/babify-requests', label: 'Babify Requests' }, // NEW
  // ... other sections
];
```

---

## Feature Requirements ✅

Based on requirements clarification:

1. **Frame Selection**: ✅ Use first frame extraction (no frame selection UI for v1)
2. **Video Quality**: ✅ 16:9 default with user selectable options (1:1, 16:9, 9:16)
3. **Babification Prompt**: ✅ Default prompt with customizable option in UI
4. **Audio Quality**: ✅ MP3 format, 44.1kHz, 192kbps
5. **Token Pricing**: ✅ 50 tokens ($0.50) total cost
6. **File Limits**: ✅ 100MB max upload limit, 60 seconds max duration

## Implementation Priority

**Phase 1** (Core Feature):
- Video upload with 100MB limit validation
- First frame extraction using FFmpeg
- FAL AI babification with default prompt
- Audio extraction to MP3
- HEDRA video generation with 16:9 default
- Basic UI with progress tracking

**Phase 2** (Enhanced Options):
- Custom prompt input with character limit
- Aspect ratio selection (1:1, 16:9, 9:16)
- Enhanced error handling and retry logic
- Advanced progress indicators

This specification provides a complete foundation for the Babify Video Generator. Ready to start implementation! 🚀 