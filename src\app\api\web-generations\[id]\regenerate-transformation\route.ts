import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'
import { fal } from '@fal-ai/client'

// Create a Supabase client specifically for server-side use with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
)

// Configure the FAL client with the API key
fal.config({
  credentials: process.env.FAL_API_KEY || ''
})

// Validate FAL API key is present
if (!process.env.FAL_API_KEY) {
  console.error('FAL_API_KEY environment variable is not set')
}

// Character prompts for transformation - focused on costume while preserving original person and background
const CHARACTER_PROMPTS = {
  scorpion: "Dress this person as <PERSON><PERSON>pion from Mortal Kombat. Keep their face, body, and background unchanged but add: yellow ninja outfit with black trim, <PERSON><PERSON><PERSON>'s signature skull mask covering lower face, kunai weapon with chain rope. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle fire/ember particle effects around the person without changing the background. Photorealistic, professional costume photography.",
  'sub-zero': "Dress this person as Sub-Zero from Mortal Kombat. Keep their face, body, and background unchanged but add: blue ninja outfit with ice-blue accents, Sub-Zero's signature ice mask covering lower face, frost effects around hands only. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle ice particle effects around the person without changing the background. Photorealistic, professional costume photography.",
  raiden: "Dress this person as Raiden from Mortal Kombat. Keep their face, body, and background unchanged but add: white and blue traditional outfit with lightning patterns, Raiden's conical straw hat, subtle blue glow in eyes. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle electrical spark effects around the person without changing the background. Photorealistic, professional costume photography.",
  'liu-kang': "Dress this person as Liu Kang from Mortal Kombat. Keep their face, body, and background unchanged but add: red martial arts outfit with black trim and dragon motifs, red bandana/headband. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle fire particle effects around fists without changing the background. Photorealistic, professional costume photography.",
  kitana: "Dress this person as Kitana from Mortal Kombat. Keep their face, body, and background unchanged but add: royal blue and black outfit with elegant design, decorative mask covering lower face, steel fans as weapons. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle magical sparkle effects around the person without changing the background. Photorealistic, professional costume photography.",
  'johnny-cage': "Dress this person as Johnny Cage from Mortal Kombat. Keep their face, body, and background unchanged but add: designer sunglasses, black military pants, open vest showing chest, confident Hollywood action star look. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle green energy glow around hands without changing the background. Photorealistic, professional costume photography.",
  mileena: "Dress this person as Mileena from Mortal Kombat. Keep their face, body, and background unchanged but add: purple ninja outfit with revealing design, Mileena's signature sai weapons, pink/purple mask covering lower face. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle dark energy effects around the person without changing the background. Photorealistic, professional costume photography.",
  'kung-lao': "Dress this person as Kung Lao from Mortal Kombat. Keep their face, body, and background unchanged but add: traditional Shaolin monk robes in blue and white, Kung Lao's signature razor-rimmed hat, martial arts stance. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle wind effects around the hat without changing the background. Photorealistic, professional costume photography."
}

// Transformation function for GPT-Image-1
async function transformWithGPTImage1(imageUrl: string, prompt: string) {
  const result = await fal.subscribe("fal-ai/gpt-image-1/edit-image/byok", {
    input: {
      image_urls: [imageUrl],
      prompt: prompt,
      image_size: "auto",
      num_images: 1,
      quality: "auto",
      openai_api_key: process.env.OPENAI_API_KEY
    },
    logs: true,
    onQueueUpdate: (update) => {
      if (update.status === "IN_PROGRESS") {
        console.log("GPT-Image-1 Processing:", update.logs?.map((log) => log.message).join('\n'));
      }
    },
  });

  return {
    transformedImageUrl: result.data.images[0].url,
    requestId: result.requestId,
    originalResponse: result.data,
    model: 'gpt-image-1/edit-image/byok',
    userTokenCost: 10, // User always pays 10 tokens
    internalCost: 0.04 // Internal API cost $0.04
  };
}

// Transformation function for SeedEdit
async function transformWithSeedEdit(imageUrl: string, prompt: string) {
  const result = await fal.subscribe("fal-ai/bytedance/seededit/v3/edit-image", {
    input: {
      prompt: prompt,
      image_url: imageUrl,
      guidance_scale: 0.5
    },
    logs: true,
    onQueueUpdate: (update) => {
      if (update.status === "IN_PROGRESS") {
        console.log("SeedEdit Processing:", update.logs?.map((log) => log.message).join('\n'));
      }
    },
  });

  return {
    transformedImageUrl: result.data.image.url,
    requestId: result.requestId,
    originalResponse: result.data,
    model: 'fal-ai/bytedance/seededit/v3/edit-image',
    userTokenCost: 10, // User always pays 10 tokens
    internalCost: 0.02 // Internal API cost $0.02 (cheaper for us)
  };
}

// Transformation function for FLUX.1 Kontext [pro]
async function transformWithFluxKontext(imageUrl: string, prompt: string) {
  const result = await fal.subscribe("fal-ai/flux-pro/kontext", {
    input: {
      prompt: prompt,
      image_url: imageUrl,
      guidance_scale: 3.5,
      num_images: 1,
      safety_tolerance: "2",
      output_format: "jpeg"
    },
    logs: true,
    onQueueUpdate: (update) => {
      if (update.status === "IN_PROGRESS") {
        console.log("FLUX Kontext Processing:", update.logs?.map((log) => log.message).join('\n'));
      }
    },
  });

  return {
    transformedImageUrl: result.data.images[0].url,
    requestId: result.requestId,
    originalResponse: result.data,
    model: 'fal-ai/flux-pro/kontext',
    userTokenCost: 10, // User always pays 10 tokens
    internalCost: 0.03 // Internal API cost (estimated)
  };
}

// Get selected image generator from database
async function getSelectedImageGenerator(): Promise<string> {
  const { data, error } = await supabaseAdmin
    .from('config_options')
    .select('value')
    .eq('key', 'mk_image_generator')
    .single();

  if (error || !data || !data.value || !data.value.selected) {
    console.log('No image generator config found, defaulting to gpt-image-1');
    return 'gpt-image-1';
  }

  return data.value.selected;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get JWT token from Authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const token = authHeader.split(' ')[1]

    // Verify JWT token with Supabase
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      )
    }

    const { id: requestId } = await params

    console.log('Regenerate transformation - Request ID:', requestId, 'User ID:', user.id)

    // Get the existing generation request using the correct table
    const { data: existingRequest, error: fetchError } = await supabaseAdmin
      .from('web_generation_requests')
      .select('*')
      .eq('id', requestId)
      .eq('user_id', user.id)
      .single()

    console.log('Database query result:', { existingRequest, fetchError })

    if (fetchError || !existingRequest) {
      console.log('Generation request not found:', { fetchError, requestId, userId: user.id })
      
      return NextResponse.json(
        { 
          error: 'Generation request not found',
          details: {
            requestId,
            userId: user.id,
            fetchError: fetchError?.message
          }
        },
        { status: 404 }
      )
    }

    // Debug: Log the current status
    console.log('Current request status for regeneration:', {
      status: existingRequest.status,
      currentStep: existingRequest.current_step,
      totalSteps: existingRequest.total_steps,
      awaitingApprovalForStep: existingRequest.awaiting_approval_for_step
    })

    // Verify the request is in the right state for regeneration
    // Allow regeneration for awaiting_approval or if we're on step 2 (approval step)
    const canRegenerate = existingRequest.status === 'awaiting_approval' || 
                         (existingRequest.current_step === 2 && existingRequest.status === 'in_progress');
    
    if (!canRegenerate) {
      return NextResponse.json(
        { 
          error: 'Can only regenerate transformations that are awaiting approval or in progress on step 2',
          currentStatus: existingRequest.status,
          currentStep: existingRequest.current_step
        },
        { status: 400 }
      )
    }

    // Get the original image URL and character type from input data
    const inputData = existingRequest.input_data
    if (!inputData?.uploaded_image_url || !inputData?.character_type) {
      return NextResponse.json(
        { error: 'Missing original image or character type for regeneration' },
        { status: 400 }
      )
    }

    // Update status to in_progress
    const { error: updateError } = await supabaseAdmin
      .from('web_generation_requests')
      .update({
        status: 'in_progress',
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId)

    if (updateError) {
      console.error('Failed to update status:', updateError)
      return NextResponse.json(
        { error: 'Failed to update request status' },
        { status: 500 }
      )
    }

    // Get the character prompt
    const characterType = inputData.character_type
    const prompt = CHARACTER_PROMPTS[characterType as keyof typeof CHARACTER_PROMPTS]
    
    if (!prompt) {
      return NextResponse.json(
        { error: `Invalid character type: ${characterType}` },
        { status: 400 }
      )
    }

    // Get selected image generator
    const selectedImageGenerator = await getSelectedImageGenerator();
    console.log('Using image generator for regeneration:', selectedImageGenerator);

    // Validate required API keys before making the call
    if (!process.env.FAL_API_KEY) {
      console.error('FAL_API_KEY environment variable is not set')
      
      // Reset status back to awaiting_approval since we haven't actually tried anything yet
      await supabaseAdmin
        .from('web_generation_requests')
        .update({
          status: 'awaiting_approval',
          error_message: 'FAL API key not configured',
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)
      
      return NextResponse.json(
        { error: 'FAL API key not configured. Please contact support.' },
        { status: 500 }
      )
    }

    // Check if OpenAI API key is required for GPT-Image-1
    if (selectedImageGenerator === 'gpt-image-1' && !process.env.OPENAI_API_KEY) {
      console.error('OPENAI_API_KEY environment variable is not set')
      
      // Reset status back to awaiting_approval since we haven't actually tried anything yet
      await supabaseAdmin
        .from('web_generation_requests')
        .update({
          status: 'awaiting_approval',
          error_message: 'OpenAI API key not configured',
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)
      
      return NextResponse.json(
        { error: 'OpenAI API key not configured. Please contact support.' },
        { status: 500 }
      )
    }

    try {
      console.log('Calling transformation API for regeneration with:', {
        imageUrl: inputData.uploaded_image_url,
        characterType,
        promptLength: prompt.length,
        generator: selectedImageGenerator
      })

      // Call the appropriate transformation function based on selected generator
      let result;
      if (selectedImageGenerator === 'gpt-image-1') {
        result = await transformWithGPTImage1(inputData.uploaded_image_url, prompt);
      } else if (selectedImageGenerator === 'fal-seededit-v3') {
        result = await transformWithSeedEdit(inputData.uploaded_image_url, prompt);
      } else if (selectedImageGenerator === 'fal-flux-pro-kontext') {
        result = await transformWithFluxKontext(inputData.uploaded_image_url, prompt);
      } else {
        throw new Error(`Unsupported image generator: ${selectedImageGenerator}`);
      }

      console.log('Transformation API result:', result)

      const transformedImageUrl = result.transformedImageUrl;

      // Update the transformation step record with new result
      const { data: transformStep, error: stepFetchError } = await supabaseAdmin
        .from('web_generation_steps')
        .select('id')
        .eq('web_request_id', requestId)
        .eq('step_type', 'image_transform')
        .single();

      if (stepFetchError || !transformStep) {
        throw new Error('Could not find transformation step to update');
      }

      // Update transformation step with new result
      await supabaseAdmin
        .from('web_generation_steps')
        .update({
          status: 'awaiting_approval',
          fal_request_id: result.requestId,
          output_data: {
            transformed_image_url: transformedImageUrl,
            original_response: result.originalResponse
          },
          completed_at: new Date().toISOString(),
          token_cost: result.userTokenCost
        })
        .eq('id', transformStep.id);

      // Update the main request with new transformation result (matching start route format)
      const updatedOutputData = {
        transformed_image_url: transformedImageUrl
      }

      const { data: updatedRequest, error: finalUpdateError } = await supabaseAdmin
        .from('web_generation_requests')
        .update({
          status: 'awaiting_approval',
          awaiting_approval_for_step: 2,
          output_data: updatedOutputData,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)
        .select('*')
        .single()

      if (finalUpdateError) {
        console.error('Failed to save transformation result:', finalUpdateError)
        return NextResponse.json(
          { error: 'Failed to save transformation result' },
          { status: 500 }
        )
      }

      return NextResponse.json(updatedRequest)

    } catch (falError) {
      console.error('FAL API error during regeneration:', falError)
      
      // Reset status back to awaiting_approval so user can try again
      await supabaseAdmin
        .from('web_generation_requests')
        .update({
          status: 'awaiting_approval',
          error_message: `Regeneration failed: ${falError instanceof Error ? falError.message : 'Unknown error'}`,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)

      return NextResponse.json(
        { error: 'Failed to regenerate transformation', details: falError instanceof Error ? falError.message : 'Unknown error' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Regenerate transformation error:', error)
    
    // Try to reset status back to awaiting_approval if we have the requestId
    const { id: requestId } = await params
    if (requestId) {
      try {
        await supabaseAdmin
          .from('web_generation_requests')
          .update({
            status: 'awaiting_approval',
            error_message: `Internal error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            updated_at: new Date().toISOString()
          })
          .eq('id', requestId)
      } catch (updateError) {
        console.error('Failed to reset status after error:', updateError)
      }
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 