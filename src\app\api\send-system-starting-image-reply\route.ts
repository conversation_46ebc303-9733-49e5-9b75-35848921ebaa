import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client using the service role key (keep this key secret!)
const supabaseServiceRole = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: Request) {
  try {
    // Expecting chatId and replyId in the JSON body.
    const { chatId, replyId, message } = await request.json();

    if (!chatId || !replyId) {
      return NextResponse.json(
        { error: "chatId and replyId are required" },
        { status: 400 }
      );
    }

    // Optionally, fetch the original reply so we can extract its image_url.
    const { data: selectedMsg, error: msgError } = await supabaseServiceRole
      .from("chat_replies")
      .select("image_url")
      .eq("id", replyId)
      .single();

    if (msgError) {
      return NextResponse.json({ error: msgError.message }, { status: 500 });
    }

    const image_url = selectedMsg?.image_url || null;

    // Construct a system reply. If a custom message was provided, use that;
    // otherwise, default to our standard system message.
    const systemMessage =
      message || `Starting image was selected (id: ${replyId}).`;

    // Insert the system reply into the chat_replies table.
    const { data, error } = await supabaseServiceRole
      .from("chat_replies")
      .insert({
        chat_id: chatId,
        sender_role: "system",
        message: systemMessage,
        image_url: image_url,
      })
      .select();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
  } catch (err: any) {
    return NextResponse.json({ error: err.message }, { status: 500 });
  }
} 