# Bot Integration APIs

This document outlines the APIs available for integrating external messaging platforms (e.g., Telegram, WhatsApp) with the Aivis platform. These APIs handle user authentication, wallet management, conversation tracking, file uploads, and image generation.

## Table of Contents

1. [Bot Message Receiving](#bot-receive-message)
2. [Bot Message Sending](#bot-send-message)
3. [Image Generation and Editing](#bot-generate-image)
4. [File Upload Handling](#bot-upload-file)

---

## Bot Receive Message

`POST /api/bot-receive-message`

Handles incoming messages from users through messaging platforms like Telegram or WhatsApp. Creates external identities for new users, sets up conversation tracking, and records messages.

### Request Body

```json
{
  "messageId": "telegram_msg_123456",  // Platform-specific message ID
  "firstName": "John",                 // User's first name
  "lastName": "<PERSON><PERSON>",                   // User's last name (optional)
  "languageCode": "en",                // User's language code (optional)
  "chatId": "telegram_chat_123456",    // Platform-specific chat ID
  "text": "Hello bot!",                // Message text content
  "provider": "telegram",              // Either "telegram" or "whatsapp"
  "providerUserId": "telegram_user_123456", // Platform-specific user ID
  "providerBotId": "telegram_bot_123456"    // Platform-specific bot ID
}
```

### Response

```json
{
  "success": true,
  "data": {
    "externalIdentityId": "uuid-of-external-identity",
    "walletId": "uuid-of-wallet",
    "botId": "uuid-of-bot",
    "conversationId": "uuid-of-conversation",
    "messageId": "uuid-of-message",
    "provider": "telegram",
    "providerUserId": "telegram_user_123456",
    "balance": 100,
    "userName": "John Doe",
    "languageCode": "en"
  }
}
```

### First-time User Workflow

When a user interacts with the bot for the first time:
1. An external identity is created for the user
2. A wallet is created with a one-time token airdrop (e.g., 100 tokens)
3. The airdrop is recorded in the transaction history
4. The user can immediately use tokens for operations like image generation

---

## Bot Send Message

`POST /api/bot-send-message`

Records a message sent from the bot to a user in the database, providing necessary info for delivery through the messaging platform.

### Request Body

```json
{
  "conversationId": "uuid-of-conversation", // ID of the conversation
  "text": "Here's the generated image",     // Message text content
  "imageUrl": "https://example.com/image.jpg", // URL of an image to send (optional)
  "imageId": "uuid-of-image"                // ID of a previously generated image (optional)
}
```

> Note: Provide either `imageUrl` OR `imageId`, not both.

### Response

```json
{
  "success": true,
  "data": {
    "messageId": "uuid-of-message",
    "conversationId": "uuid-of-conversation",
    "provider": "telegram",
    "providerConversationId": "telegram_chat_123456",
    "externalIdentityId": "uuid-of-external-identity",
    "text": "Here's the generated image",
    "imageUrl": "https://example.com/image.jpg",
    "imageId": null
  }
}
```

---

## Bot Generate Image

`POST /api/bot-generate-image`

Handles image generation or editing operations, consuming tokens from the user's wallet.

### Request Body

```json
{
  "conversationId": "uuid-of-conversation", // ID of the conversation
  "walletId": "uuid-of-wallet",           // ID of the user's wallet
  "tokenCost": 20,                        // Number of tokens to consume
  "operation": "generate",                // Either "generate" or "edit"
  "promptText": "A futuristic cityscape", // For "generate" operation
  "originalUploadId": "uuid-of-upload",   // For "edit" operation
  "parameters": {                         // Optional parameters
    "width": 1024,
    "height": 1024,
    "style": "realistic"
  },
  "resultUrl": "https://example.com/result.jpg" // URL for resulting image
}
```

### Response

```json
{
  "success": true,
  "data": {
    "imageId": "uuid-of-generated-image",
    "conversationId": "uuid-of-conversation",
    "walletId": "uuid-of-wallet",
    "tokenCost": 20,
    "operation": "generate",
    "resultUrl": "https://example.com/result.jpg",
    "remainingBalance": 80
  }
}
```

### Error Responses

If the user has insufficient tokens:

```json
{
  "success": false,
  "error": "Insufficient tokens. Required: 20, Available: 10"
}
```

---

## Bot Upload File

`POST /api/bot-upload-file`

Records a file uploaded by a user through the bot (typically an image to be edited).

### Request Body

```json
{
  "conversationId": "uuid-of-conversation",   // ID of the conversation
  "externalIdentityId": "uuid-of-external-identity", // ID of the external identity
  "fileUrl": "https://example.com/upload.jpg", // URL of the uploaded file
  "fileMetadata": {                           // Optional metadata
    "originalFilename": "photo.jpg",
    "mimeType": "image/jpeg",
    "size": 102400
  },
  "recordAsMessage": true                     // Whether to record as a message
}
```

### Response

```json
{
  "success": true,
  "data": {
    "uploadId": "uuid-of-upload",
    "messageId": "uuid-of-message",            // If recordAsMessage=true
    "conversationId": "uuid-of-conversation",
    "externalIdentityId": "uuid-of-external-identity",
    "fileUrl": "https://example.com/upload.jpg",
    "provider": "telegram",
    "providerConversationId": "telegram_chat_123456"
  }
}
```

---

## Integration Flow

A typical integration with external messaging platforms would follow this flow:

1. **User sends a message to the bot**
   - Call `bot-receive-message` to process the message
   - The API creates/retrieves user identity and records the message

2. **User uploads an image**
   - Call `bot-upload-file` to record the upload
   - The API stores the file reference and optionally records it as a message

3. **User requests image generation**
   - Parse user's message for generation intent
   - Call `bot-generate-image` with operation="generate"
   - The API consumes tokens and records the generated image

4. **User requests image editing**
   - Parse user's message for editing intent
   - Call `bot-generate-image` with operation="edit" and reference to uploaded image
   - The API consumes tokens and records the edited image

5. **Bot sends a response**
   - Call `bot-send-message` to record the bot's response
   - Use the returned data to send the actual message through the messaging platform

## Error Handling

All APIs return a consistent error format:

```json
{
  "success": false,
  "error": "Detailed error message"
}
```

Status codes:
- `400`: Bad request (invalid parameters)
- `404`: Resource not found
- `500`: Server error

## Security Considerations

- All APIs require authentication via the Supabase service role key
- Keep the service role key secure and never expose it in client-side code
- Use proper encryption for all communication with these APIs 