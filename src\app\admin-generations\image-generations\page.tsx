"use client";

import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store';
import { supabase } from '../../../lib/supabaseClient';
import styles from '../../admin/admin.module.css';

interface WebImageGeneration {
  id: string;
  user_id: string;
  user_email: string;
  user_role: string;
  prompt_text: string;
  status: string;
  result_url: string;
  created_at: string;
  token_cost: number;
}

interface WebImageGenerationsData {
  images: WebImageGeneration[];
  total_count: number;
  completed_count: number;
  pending_count: number;
  error_count: number;
}

export default function AdminWebImageGenerations() {
  const { user } = useSelector((state: RootState) => state.user);
  const [data, setData] = useState<WebImageGenerationsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchWebImageGenerations();
  }, []);

  const fetchWebImageGenerations = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        setError('Authentication required');
        return;
      }

      const response = await fetch('/api/admin/image-generations', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${await response.text()}`);
      }

      const result = await response.json();
      setData(result);
    } catch (err: any) {
      console.error('Error fetching web image generations:', err);
      setError(err.message || 'Failed to fetch web image generations');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (user?.role !== 'admin') {
    return (
      <div className={styles.errorMessage}>
        <h2>Access Denied</h2>
        <p>Admin access required to view this page.</p>
      </div>
    );
  }

  if (loading) {
    return <div className={styles.loading}>Loading web image generations...</div>;
  }

  if (error) {
    return (
      <div className={styles.errorMessage}>
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={fetchWebImageGenerations} className={styles.retryButton}>
          Retry
        </button>
      </div>
    );
  }

  if (!data) {
    return <div className={styles.errorMessage}>No data available</div>;
  }

  return (
    <div className={styles.adminPage}>
      <h2>Web Image Generations</h2>
      {/* Statistics Summary */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <h3>Total Images</h3>
          <div className={styles.statNumber}>{data.total_count}</div>
        </div>
        <div className={styles.statCard}>
          <h3>Completed</h3>
          <div className={styles.statNumber} style={{ color: '#28a745' }}>
            {data.completed_count}
          </div>
        </div>
        <div className={styles.statCard}>
          <h3>Pending</h3>
          <div className={styles.statNumber} style={{ color: '#ffc107' }}>
            {data.pending_count}
          </div>
        </div>
        <div className={styles.statCard}>
          <h3>Errors</h3>
          <div className={styles.statNumber} style={{ color: '#dc3545' }}>
            {data.error_count}
          </div>
        </div>
      </div>
      {/* Images Table */}
      <div className={styles.tableContainer}>
        <table className={styles.adminTable}>
          <thead>
            <tr>
              <th>User</th>
              <th>Status</th>
              <th>Prompt</th>
              <th>Created</th>
              <th>Image</th>
              <th>Tokens</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {data.images.map((image) => (
              <tr key={image.id}>
                <td>
                  <div className={styles.userInfo}>
                    <div>{image.user_email}</div>
                    <small style={{ color: '#666' }}>{image.user_role}</small>
                  </div>
                </td>
                <td>
                  <span className={styles.statusBadge}>{image.status}</span>
                </td>
                <td style={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                  {image.prompt_text}
                </td>
                <td>
                  <small>{formatDate(image.created_at)}</small>
                </td>
                <td>
                  {image.result_url ? (
                    <a href={image.result_url} target="_blank" rel="noopener noreferrer">
                      <img src={image.result_url} alt="Generated" style={{ maxWidth: 80, maxHeight: 80, borderRadius: 8 }} />
                    </a>
                  ) : (
                    <span style={{ color: '#999' }}>No image</span>
                  )}
                </td>
                <td>{image.token_cost}</td>
                <td>
                  <div className={styles.actionButtons}>
                    <button 
                      onClick={() => navigator.clipboard.writeText(image.id)}
                      className={styles.actionButton}
                      title="Copy ID"
                    >
                      📋
                    </button>
                    {image.result_url && (
                      <a 
                        href={image.result_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className={styles.actionButton}
                        title="Download"
                        download
                      >
                        💾
                      </a>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {data.images.length === 0 && (
        <div className={styles.emptyState}>
          <p>No web image generations found.</p>
        </div>
      )}
    </div>
  );
}