import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client specifically for server-side use with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    const token = authHeader.split(' ')[1];

    // Verify the token with Supabase
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('user_id', user.id)
      .single();

    if (profileError || !profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get pagination parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '20');
    const offset = (page - 1) * pageSize;

    // Fetch logo generations with pagination
    const { data: logoGenerations, error: fetchError } = await supabaseAdmin
      .from('logo_generations')
      .select(`
        id,
        user_id,
        prompt,
        style,
        image_url,
        fal_request_id,
        model_used,
        token_cost,
        status,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + pageSize - 1);

    if (fetchError) {
      console.error('Error fetching logo generations:', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch logo generations' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    const { count, error: countError } = await supabaseAdmin
      .from('logo_generations')
      .select('id', { count: 'exact', head: true });

    if (countError) {
      console.error('Error counting logo generations:', countError);
      return NextResponse.json(
        { error: 'Failed to count logo generations' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      logoGenerations: logoGenerations || [],
      total: count || 0,
      page: page,
      pageSize: pageSize,
      totalPages: Math.ceil((count || 0) / pageSize)
    });

  } catch (error) {
    console.error('Admin logo generations error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 