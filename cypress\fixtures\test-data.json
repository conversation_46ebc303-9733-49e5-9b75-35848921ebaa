{"botReceiveMessage": {"messageId": "test-message-id", "firstName": "Test", "lastName": "User", "languageCode": "en", "chatId": "test-chat-id", "text": "Hello bot!", "provider": "telegram", "providerUserId": "test-provider-user-id", "providerBotId": "test-provider-bot-id"}, "botSendMessage": {"conversationId": "test-conversation-id", "text": "Hello from the bot!"}, "botGenerateImage": {"conversationId": "test-conversation-id", "walletId": "test-wallet-id", "tokenCost": 10, "operation": "generate", "promptText": "A beautiful sunset over the ocean"}, "botUploadFile": {"conversationId": "test-conversation-id", "externalIdentityId": "test-external-identity-id", "fileUrl": "https://example.com/test-image.jpg", "fileMetadata": {"originalFilename": "test-image.jpg", "mimeType": "image/jpeg", "size": 12345}, "recordAsMessage": true, "sender": "user", "textMessage": "This is a test image caption"}}