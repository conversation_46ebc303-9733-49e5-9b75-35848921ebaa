import React from 'react'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import { MortalKombatVideoFlow } from '../MortalKombatVideoFlow'

// Mock Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn().mockReturnValue(null),
  }),
}))

// Mock Supabase client
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn().mockResolvedValue({
        data: { user: { id: 'test-user', email: '<EMAIL>' } },
        error: null,
      }),
      getSession: jest.fn().mockResolvedValue({
        data: { session: { access_token: 'mock-token' } },
        error: null,
      }),
    },
    storage: {
      from: jest.fn().mockReturnThis(),
      getPublicUrl: jest.fn(),
    },
  })),
}))

// Mock Header component
jest.mock('@/components/Header', () => {
  return function MockHeader() {
    return <div data-testid="header">Header</div>
  }
})

// Mock fetch for API calls
const mockFetch = jest.fn()
global.fetch = mockFetch

describe('MortalKombatVideoFlow - Image Resize Modal', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockFetch.mockClear()
  })

  it('should show resize modal when API returns requiresResizeConfirmation', async () => {
    // Mock API response for resize confirmation required
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        requiresResizeConfirmation: true,
        originalImage: {
          url: 'https://test.com/original.jpg',
          width: 5000,
          height: 3000,
          size: 2048576
        },
        resizedPreview: {
          url: 'https://test.com/preview.jpg',
          width: 3840,
          height: 2304,
          size: 1048576
        },
        settings: {
          character_type: 'scorpion',
          video_settings: {
            duration: 5,
            resolution: '720p'
          }
        },
        message: 'Image is larger than 4000 pixels and needs to be resized. Please review the preview.'
      })
    })

    const { container } = render(<MortalKombatVideoFlow isFreeGeneration={false} />)

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByTestId('mk-main-title')).toBeInTheDocument()
    })

    // Find and click the file input to trigger upload
    const fileInput = container.querySelector('input[type="file"]')
    expect(fileInput).toBeInTheDocument()

    // Create a mock file that would be large
    const largeFile = new File(['large-image-data'], 'large-test.jpg', { type: 'image/jpeg' })
    
    // Simulate file upload
    fireEvent.change(fileInput!, { target: { files: [largeFile] } })

    // Find and click the start generation button
    await waitFor(() => {
      const startButton = screen.getByTestId('mk-start-generation')
      expect(startButton).toBeInTheDocument()
      fireEvent.click(startButton)
    })

    // Wait for the modal to appear
    await waitFor(() => {
      expect(screen.getByText(/Optimizare dimensiune imagine necesară/i)).toBeInTheDocument()
    }, { timeout: 5000 })

    // Check modal content
    expect(screen.getByText(/Imaginea ta este prea mare pentru procesare/i)).toBeInTheDocument()
    expect(screen.getByText(/Original:/i)).toBeInTheDocument()
    expect(screen.getByText(/Optimizat:/i)).toBeInTheDocument()
    expect(screen.getByTestId('mk-resize-original-size')).toHaveTextContent('5000×3000px')
    expect(screen.getByTestId('mk-resize-optimized-size')).toHaveTextContent('3840×2304px')

    // Check benefits section
    expect(screen.getByText(/Beneficiile optimizării:/i)).toBeInTheDocument()
    expect(screen.getByText(/Procesare și generare mai rapidă/i)).toBeInTheDocument()
    expect(screen.getByText(/Compatibilitate mai bună cu modelele AI/i)).toBeInTheDocument()

    // Check buttons
    expect(screen.getByText(/Anulează/i)).toBeInTheDocument()
    expect(screen.getByText(/Continuă cu imaginea optimizată/i)).toBeInTheDocument()
  })

  it('should handle resize confirmation acceptance', async () => {
    // Mock initial resize confirmation response
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        requiresResizeConfirmation: true,
        originalImage: {
          url: 'https://test.com/original.jpg',
          width: 5000,
          height: 3000,
          size: 2048576
        },
        resizedPreview: {
          url: 'https://test.com/preview.jpg',
          width: 3840,
          height: 2304,
          size: 1048576
        },
        settings: {
          character_type: 'scorpion'
        }
      })
    })

    // Mock fetch for the resized image URL
    mockFetch.mockImplementationOnce((url) => {
      if (url === 'https://test.com/preview.jpg') {
        return Promise.resolve({
          ok: true,
          blob: async () => new Blob(['mock-image-data'], { type: 'image/jpeg' })
        });
      }
      // fallback to default mock
      return Promise.resolve({ ok: true, json: async () => ({}) });
    });

    // Mock confirm resize response
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        status: 'awaiting_approval',
        current_step: 2,
        id: 'test-request-id'
      })
    })

    const { container } = render(<MortalKombatVideoFlow isFreeGeneration={false} />)

    // Wait for component to load and simulate the resize modal flow
    await waitFor(() => {
      expect(screen.getByTestId('mk-main-title')).toBeInTheDocument()
    })

    // Simulate file upload
    const fileInput = container.querySelector('input[type="file"]')
    const largeFile = new File(['large-image-data'], 'large-test.jpg', { type: 'image/jpeg' })
    fireEvent.change(fileInput!, { target: { files: [largeFile] } })

    // Start generation
    await waitFor(() => {
      const startButton = screen.getByTestId('mk-start-generation')
      fireEvent.click(startButton)
    })

    await act(async () => {
      await waitFor(() => {
        const continueButton = screen.getByText(/Continuă cu imaginea optimizată/i)
        expect(continueButton).toBeInTheDocument()
        fireEvent.click(continueButton)
      })
      // Should close modal and proceed with generation
      await waitFor(() => {
        expect(screen.queryByTestId('mk-resize-modal')).not.toBeInTheDocument()
      }, { timeout: 5000 })
    })
  })

  it('should handle resize confirmation cancellation', async () => {
    // Mock resize confirmation response
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        requiresResizeConfirmation: true,
        originalImage: {
          url: 'https://test.com/original.jpg',
          width: 5000,
          height: 3000,
          size: 2048576
        },
        resizedPreview: {
          url: 'https://test.com/preview.jpg',
          width: 3840,
          height: 2304,
          size: 1048576
        },
        settings: { character_type: 'scorpion' }
      })
    })

    const { container } = render(<MortalKombatVideoFlow isFreeGeneration={false} />)

    await waitFor(() => {
      expect(screen.getByTestId('mk-main-title')).toBeInTheDocument()
    })

    // Simulate file upload
    const fileInput = container.querySelector('input[type="file"]')
    const largeFile = new File(['large-image-data'], 'large-test.jpg', { type: 'image/jpeg' })
    fireEvent.change(fileInput!, { target: { files: [largeFile] } })

    // Start generation
    await waitFor(() => {
      const startButton = screen.getByTestId('mk-start-generation')
      fireEvent.click(startButton)
    })

    // Wait for modal and click cancel
    await waitFor(() => {
      const cancelButton = screen.getByText(/Anulează/i)
      expect(cancelButton).toBeInTheDocument()
      fireEvent.click(cancelButton)
    })

    // Modal should close and return to initial state
    await waitFor(() => {
      expect(screen.queryByTestId('mk-resize-modal')).not.toBeInTheDocument()
    }, { timeout: 5000 })

    // Should be back to step 1
    expect(screen.getByTestId('mk-upload-step')).toBeInTheDocument()
  })

  it('should display correct image previews in modal', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        requiresResizeConfirmation: true,
        originalImage: {
          url: 'https://test.com/original.jpg',
          width: 5000,
          height: 3000,
          size: 2048576
        },
        resizedPreview: {
          url: 'https://test.com/preview.jpg',
          width: 3840,
          height: 2304,
          size: 1048576
        },
        settings: { character_type: 'scorpion' }
      })
    })

    const { container } = render(<MortalKombatVideoFlow isFreeGeneration={false} />)

    await waitFor(() => {
      expect(screen.getByTestId('mk-main-title')).toBeInTheDocument()
    })

    // Simulate upload and generation
    const fileInput = container.querySelector('input[type="file"]')
    const largeFile = new File(['large-image-data'], 'large-test.jpg', { type: 'image/jpeg' })
    fireEvent.change(fileInput!, { target: { files: [largeFile] } })

    await waitFor(() => {
      const startButton = screen.getByTestId('mk-start-generation')
      fireEvent.click(startButton)
    })

    // Check that both images are displayed in the modal
    await waitFor(() => {
      const originalImage = container.querySelector('img[src="https://test.com/original.jpg"]')
      const previewImage = container.querySelector('img[src="https://test.com/preview.jpg"]')
      
      expect(originalImage).toBeInTheDocument()
      expect(previewImage).toBeInTheDocument()
    })
  })

  it('should show loading state when processing resize confirmation', async () => {
    // Mock initial response
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        requiresResizeConfirmation: true,
        originalImage: { url: 'https://test.com/original.jpg', width: 5000, height: 3000, size: 2048576 },
        resizedPreview: { url: 'https://test.com/preview.jpg', width: 3840, height: 2304, size: 1048576 },
        settings: { character_type: 'scorpion' }
      })
    })

    // Mock delayed confirm response
    let resolveConfirm: (value: any) => void
    const confirmPromise = new Promise(resolve => { resolveConfirm = resolve })
    mockFetch.mockReturnValueOnce(confirmPromise)

    const { container } = render(<MortalKombatVideoFlow isFreeGeneration={false} />)

    await waitFor(() => {
      expect(screen.getByTestId('mk-main-title')).toBeInTheDocument()
    })

    // Simulate upload
    const fileInput = container.querySelector('input[type="file"]')
    const largeFile = new File(['large-image-data'], 'large-test.jpg', { type: 'image/jpeg' })
    fireEvent.change(fileInput!, { target: { files: [largeFile] } })

    await waitFor(() => {
      const startButton = screen.getByTestId('mk-start-generation')
      fireEvent.click(startButton)
    })

    // Click continue button
    await waitFor(() => {
      const continueButton = screen.getByText(/Continuă cu imaginea optimizată/i)
      fireEvent.click(continueButton)
    })

    // Should show loading state
    await waitFor(() => {
      expect(screen.getByText(/Se procesează.../i)).toBeInTheDocument()
    })

    // Resolve the promise to complete the flow
    resolveConfirm!({
      ok: true,
      json: async () => ({ status: 'awaiting_approval', current_step: 2 })
    })
  })
}) 