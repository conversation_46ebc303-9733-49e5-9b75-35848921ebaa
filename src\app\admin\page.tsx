"use client";

import React, { useState, useEffect } from "react";
import styles from "./admin.module.css";
import { supabase } from "@/lib/supabaseClient";
import Link from "next/link";

interface ConversationStats {
  totalConversations: number;
  telegramConversations: number;
  whatsappConversations: number;
  totalMessages: number;
  userMessages: number;
  botMessages: number;
  avgMessagesPerConversation: number;
  activeUsers: number;
  mostRecentConversation: string;
  imagesGeneratedToday: number;
  recentActiveUsers: {
    id: string;
    telegram_id?: string;
    whatsapp_id?: string;
    firstName?: string;
    lastName?: string;
    lastActive: string;
    platform: string;
  }[];
  dailyConversations: {
    date: string;
    count: number;
  }[];
}

interface BotActivity {
  provider: string;
  provider_bot_id: string;
  name: string;
  conversation_count: number;
  message_count: number;
}

interface Bot {
  id: string;
  provider: string;
  provider_bot_id: string;
  name: string;
  created_at: string;
}

export default function ConversationsPage() {
  const [conversationStats, setConversationStats] = useState<ConversationStats | null>(null);
  const [botActivity, setBotActivity] = useState<BotActivity[]>([]);
  const [bots, setBots] = useState<Bot[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch data on component mount
  useEffect(() => {
    fetchConversationStats();
    fetchBotActivity();
    fetchBots();
  }, []);

  // Function to fetch all bots
  const fetchBots = async () => {
    try {
      const { data, error } = await supabase
        .from('bots')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      setBots(data || []);
    } catch (err: any) {
      console.error('Error fetching bots:', err);
    }
  };

  // Function to fetch conversation statistics
  const fetchConversationStats = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get total conversations
      const { count: totalConversations, error: conversationsError } = await supabase
        .from('bot_conversations')
        .select('id', { count: 'exact', head: true });

      if (conversationsError) throw conversationsError;

      // Get telegram conversations
      const { count: telegramConversations, error: telegramError } = await supabase
        .from('bot_conversations')
        .select('id', { count: 'exact', head: true })
        .eq('provider', 'telegram');

      if (telegramError) throw telegramError;

      // Get whatsapp conversations
      const { count: whatsappConversations, error: whatsappError } = await supabase
        .from('bot_conversations')
        .select('id', { count: 'exact', head: true })
        .eq('provider', 'whatsapp');

      if (whatsappError) throw whatsappError;

      // Get total messages
      const { count: totalMessages, error: messagesError } = await supabase
        .from('messages')
        .select('id', { count: 'exact', head: true });

      if (messagesError) throw messagesError;

      // Get user messages
      const { count: userMessages, error: userMessagesError } = await supabase
        .from('messages')
        .select('id', { count: 'exact', head: true })
        .eq('sender', 'user');

      if (userMessagesError) throw userMessagesError;

      // Get bot messages
      const { count: botMessages, error: botMessagesError } = await supabase
        .from('messages')
        .select('id', { count: 'exact', head: true })
        .eq('sender', 'bot');

      if (botMessagesError) throw botMessagesError;

      // Get number of unique users
      const { data: uniqueUsers, error: usersError } = await supabase
        .from('external_identities')
        .select('id', { count: 'exact' });

      if (usersError) throw usersError;

      // Get most recent conversation
      const { data: recentConversation, error: recentError } = await supabase
        .from('bot_conversations')
        .select('started_at')
        .order('started_at', { ascending: false })
        .limit(1);

      if (recentError) throw recentError;

      // Get recent active users
      const { data: recentActiveUsers, error: recentUsersError } = await supabase
        .from('bot_conversations')
        .select(`
          external_identity_id,
          started_at,
          provider,
          external_identity:external_identities(
            id,
            telegram_id,
            whatsapp_id,
            telegram_first_name,
            telegram_last_name,
            whatsapp_name
          )
        `)
        .order('started_at', { ascending: false })
        .limit(5);

      if (recentUsersError) throw recentUsersError;

      // Process recent active users data
      const processedRecentUsers = recentActiveUsers
        ? recentActiveUsers
            .filter(conversation => conversation.external_identity) // Filter out nulls
            .map(conversation => {
              const identity = conversation.external_identity as any; // Type assertion for safety
              const platform = conversation.provider || 'unknown';
              const userId = platform === 'telegram' ? identity.telegram_id : identity.whatsapp_id;
              
              // Use name fields if available, otherwise use platform + ID
              const firstName = identity.telegram_first_name || identity.whatsapp_name || null;
              const lastName = identity.telegram_last_name || '';
              
              return {
                id: identity.id,
                telegram_id: identity.telegram_id,
                whatsapp_id: identity.whatsapp_id,
                firstName,
                lastName,
                lastActive: new Date(conversation.started_at).toLocaleString(),
                platform
              };
            })
            // Remove duplicates (same user with multiple conversations)
            .filter((user, index, self) => 
              index === self.findIndex(u => u.id === user.id)
            )
        : [];

      // Get daily messages for the last 7 days
      const today = new Date();
      const sevenDaysAgo = new Date(today);
      sevenDaysAgo.setDate(today.getDate() - 7);

      const { data: dailyData, error: dailyError } = await supabase
        .from('messages')
        .select('created_at')
        .gte('created_at', sevenDaysAgo.toISOString())
        .order('created_at', { ascending: true });

      if (dailyError) throw dailyError;

      // Process daily data
      const dailyCounts: Record<string, number> = {};
      const dailyMessages: { date: string; count: number }[] = [];

      // Initialize last 7 days with 0 counts
      for (let i = 0; i < 7; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);
        const dateString = date.toISOString().split('T')[0];
        dailyCounts[dateString] = 0;
      }

      // Count messages per day
      dailyData?.forEach(msg => {
        const date = new Date(msg.created_at).toISOString().split('T')[0];
        dailyCounts[date] = (dailyCounts[date] || 0) + 1;
      });

      // Convert to array for display
      Object.keys(dailyCounts).sort().forEach(date => {
        dailyMessages.push({ date, count: dailyCounts[date] });
      });

      // Calculate average messages per conversation
      const avgMessagesPerConversation = totalConversations && totalConversations > 0 && totalMessages != null
        ? Math.round((totalMessages / totalConversations) * 10) / 10
        : 0;

      // Get images generated today
      today.setHours(0, 0, 0, 0);
      
      const { count: imagesGeneratedToday, error: imagesError } = await supabase
        .from('images')
        .select('id', { count: 'exact', head: true })
        .gte('created_at', today.toISOString());
        
      if (imagesError) throw imagesError;

      setConversationStats({
        totalConversations: totalConversations || 0,
        telegramConversations: telegramConversations || 0,
        whatsappConversations: whatsappConversations || 0,
        totalMessages: totalMessages || 0,
        userMessages: userMessages || 0,
        botMessages: botMessages || 0,
        avgMessagesPerConversation,
        activeUsers: uniqueUsers?.length || 0,
        mostRecentConversation: recentConversation?.[0]?.started_at 
          ? new Date(recentConversation[0].started_at).toLocaleString() 
          : 'None',
        imagesGeneratedToday: imagesGeneratedToday || 0,
        recentActiveUsers: processedRecentUsers,
        dailyConversations: dailyMessages
      });

    } catch (err: any) {
      setError(`Error fetching conversation statistics: ${err.message}`);
      console.error('Error fetching conversation statistics:', err);
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch bot activity
  const fetchBotActivity = async () => {
    try {
      // Get bot activity
      const { data, error } = await supabase.rpc('get_bot_activity');
      
      if (error) throw error;
      
      setBotActivity(data || []);
    } catch (err: any) {
      console.error('Error fetching bot activity:', err);
      // We'll just log this error instead of showing it to the user
      // since we already have the main stats error handled
    }
  };

  return (
    <div className={styles.conversationsSection}>
 
      <h2>Bot Conversations Statistics</h2>
      
      {loading && <p className={styles.loadingText}>Loading conversation statistics...</p>}
      {error && <p className={styles.errorText}>{error}</p>}
      
      {!loading && !error && conversationStats && (
        <>

          
          <div className={styles.statsGrid}>
            <div className={styles.statCard}>
              <h3>Total Conversations</h3>
              <p className={styles.statValue}>{conversationStats.totalConversations}</p>
              <div className={styles.statDetails}>
                <span>Telegram: {conversationStats.telegramConversations}</span>
                <span>WhatsApp: {conversationStats.whatsappConversations}</span>
              </div>
            </div>
            
            <div className={styles.statCard}>
              <h3>Total Messages</h3>
              <p className={styles.statValue}>{conversationStats.totalMessages}</p>
              <div className={styles.statDetails}>
                <span>From users: {conversationStats.userMessages}</span>
                <span>From bots: {conversationStats.botMessages}</span>
              </div>
            </div>
            
            <div className={styles.statCard}>
              <h3>Active Users</h3>
              <p className={styles.statValue}>{conversationStats.activeUsers}</p>
            </div>
            
            <div className={styles.statCard}>
              <h3>Avg Messages/Conversation</h3>
              <p className={styles.statValue}>{conversationStats.avgMessagesPerConversation}</p>
            </div>
            
            <div className={styles.statCard}>
              <h3>Images Generated Today</h3>
              <p className={styles.statValue}>{conversationStats.imagesGeneratedToday}</p>
            </div>
          </div>
          
          <div className={styles.recentActivity}>
            <h3>Recent Activity</h3>
            <p>Most recent conversation: {conversationStats.mostRecentConversation}</p>
            
            {conversationStats.recentActiveUsers.length > 0 && (
              <div className={styles.recentUsers}>
                <h4>Recently Active Users</h4>
                <ul className={styles.usersList}>
                  {conversationStats.recentActiveUsers.map((user, index) => (
                    <li key={user.id} className={styles.userItem}>
                      <div className={styles.userIcon}>
                        {user.telegram_id ? 'T' : 'W'}
                      </div>
                      <div className={styles.userDetails}>
                        <span className={styles.userName}>
                          {user.firstName 
                            ? `${user.firstName} ${user.lastName}` 
                            : user.telegram_id 
                              ? 'Telegram User' 
                              : 'WhatsApp User'
                          }
                        </span>
                        <span className={styles.userId}>
                          ID: {user.telegram_id || user.whatsapp_id}
                        </span>
                        <span className={styles.userActivity}>
                          Last active: {user.lastActive}
                        </span>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
          
          <div className={styles.dailyStats}>
            <h3>Daily Messages (Last 7 Days)</h3>
            <div className={styles.barChart}>
              {conversationStats.dailyConversations.map((day, index) => (
                <div key={index} className={styles.barContainer}>
                  <div 
                    className={styles.bar} 
                    style={{ 
                      height: `${Math.min(Math.max(day.count * 8, 5), 160)}px`
                    }}
                  ></div>
                  <span className={styles.barLabel}>{day.date.split('-')[2]}</span>
                  <span className={styles.barCount}>{day.count}</span>
                </div>
              ))}
            </div>
          </div>

                    {/* Active Bots Section */}
                    <div className={styles.activeBots}>
            <h3>Active Bots</h3>
            <div className={styles.botsList}>
              {bots.length === 0 ? (
                <p>No bots configured.</p>
              ) : (
                bots.map((bot) => (
                  <div key={bot.id} className={styles.botCard}>
                    <div className={styles.botIcon}>
                      {bot.provider === 'telegram' ? (
                        <div className={styles.telegramIcon}>T</div>
                      ) : (
                        <div className={styles.whatsappIcon}>W</div>
                      )}
                    </div>
                    <div className={styles.botInfo}>
                      <h4>{bot.name}</h4>
                      <p className={styles.botProvider}>{bot.provider}</p>
                      <p className={styles.botId}>ID: {bot.provider_bot_id}</p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
          
          {botActivity.length > 0 && (
            <div className={styles.botActivity}>
              <h3>Bot Activity</h3>
              <table className={styles.table}>
                <thead>
                  <tr>
                    <th>Bot Name</th>
                    <th>Provider</th>
                    <th>Bot ID</th>
                    <th>Conversations</th>
                    <th>Messages</th>
                  </tr>
                </thead>
                <tbody>
                  {botActivity.map((bot, index) => (
                    <tr key={index}>
                      <td className={styles.botName}>{bot.name}</td>
                      <td className={styles.botProviderCell}>
                        <span className={`${styles.providerBadge} ${styles[bot.provider]}`}>
                          {bot.provider}
                        </span>
                      </td>
                      <td className={styles.botProviderId}>{bot.provider_bot_id}</td>
                      <td>{bot.conversation_count}</td>
                      <td>{bot.message_count}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </>
      )}
    </div>
  );
} 