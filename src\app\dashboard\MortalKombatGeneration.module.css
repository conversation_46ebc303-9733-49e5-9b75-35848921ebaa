.mkGenCard {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  min-height: 80px;
}

.mkGenImage {
  width: 96px;
  height: 96px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #eee;
  margin-right: 16px;
}

.mkGenDetails {
  flex: 1;
}

.mkGenCharacter {}

.mkGenStatus {}

.mkGenCreated {}

.mkGenButton {
  min-width: 120px;
}

.mkGenDetailsWrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0;
}

.mkGenDetailsMain {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.mkGenDetailsBottomRow {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 16px;
  gap: 8px;
}

.mkGenLeft {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.mkGenInfoBlock {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.mkGenInfo {
  font-size: 16px;
  font-weight: 400;
  color: #222;
}

@media (max-width: 600px) {
  .mkGenCard {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 16px;
  }
  .mkGenDetailsWrapper {
    flex-direction: column-reverse;
    gap: 0;
  }
  .mkGenDetailsBottomRow {
    flex-direction: row;
    justify-content: space-between;
    margin-top: 8px;
  }
  .mkGenLeft {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  .mkGenImage {
    margin: 0 0 8px 0;
    width: 120px;
    height: 120px;
  }
  .mkGenButton {
    margin-top: 12px;
    width: 100%;
    min-width: unset;
  }
} 