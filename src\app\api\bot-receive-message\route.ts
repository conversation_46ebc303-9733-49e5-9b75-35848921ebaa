import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client using the service role key (kept secret)
const supabaseServiceRole = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface BotReceiveMessageRequest {
  messageId: string;    // Platform-specific message ID
  firstName: string;    // User's first name
  lastName?: string;    // User's last name (optional)
  languageCode?: string; // User's language code (e.g., 'en', 'ro')
  chatId: string;       // Platform-specific chat ID
  text: string;         // Message text content
  provider: 'telegram' | 'whatsapp'; // Message source platform
  providerUserId: string; // Platform-specific user ID
  providerBotId: string;  // Platform-specific bot ID
}

/**
 * API endpoint to receive and process messages from messaging platforms
 * This handles the initial contact and subsequent messages
 */
export async function POST(request: Request) {
  try {
    const {
      messageId,
      firstName,
      lastName,
      languageCode,
      chatId,
      text,
      provider,
      providerUserId,
      providerBotId
    } = await request.json() as BotReceiveMessageRequest;
    
    // Validate required fields
    if (!messageId || !firstName || !chatId || !text || !provider || !providerUserId || !providerBotId) {
      return NextResponse.json({ 
        success: false, 
        error: "Missing required fields: messageId, firstName, chatId, text, provider, providerUserId, providerBotId" 
      }, { status: 400 });
    }
    
    // Validate provider type
    if (provider !== 'telegram' && provider !== 'whatsapp') {
      return NextResponse.json({ 
        success: false, 
        error: "Provider must be either 'telegram' or 'whatsapp'" 
      }, { status: 400 });
    }
    
    // Step 1: Get or create the external identity and wallet with potential airdrop
    let externalIdentityId: string;
    let walletId: string;
    
    if (provider === 'telegram') {
      // Get the external identity for this Telegram user
      const { data: extIdentity, error: extError } = await supabaseServiceRole
        .from('external_identities')
        .select('id, user_id')
        .eq('telegram_id', providerUserId)
        .maybeSingle();
      if (extError || !extIdentity) {
        return NextResponse.json({ success: false, error: 'Telegram user not found' }, { status: 404 });
      }
      externalIdentityId = extIdentity.id;
      let walletQuery;
      if (extIdentity.user_id) {
        // Use the web user's wallet
        walletQuery = supabaseServiceRole
          .from('wallets')
          .select('id')
          .eq('user_id', extIdentity.user_id)
          .maybeSingle();
      } else {
        // Use the external identity's wallet
        walletQuery = supabaseServiceRole
          .from('wallets')
          .select('id')
          .eq('external_identity_id', extIdentity.id)
          .maybeSingle();
      }
      const { data: wallet, error: walletError } = await walletQuery;
      if (walletError || !wallet) {
        return NextResponse.json({ success: false, error: 'Wallet not found' }, { status: 404 });
      }
      walletId = wallet.id;
      // Update user's name data
      await supabaseServiceRole
        .from('external_identities')
        .update({
          telegram_first_name: firstName,
          telegram_last_name: lastName || null
        })
        .eq('id', externalIdentityId);
    } else {
      // Call the database function for WhatsApp users
      const { data, error } = await supabaseServiceRole.rpc(
        'get_or_create_external_wallet',
        { p_whatsapp_id: providerUserId }
      );
      
      if (error) {
        return NextResponse.json({ success: false, error: error.message }, { status: 500 });
      }
      
      walletId = data;
      
      // Get the external identity ID
      const { data: identityData, error: identityError } = await supabaseServiceRole
        .from('external_identities')
        .select('id')
        .eq('whatsapp_id', providerUserId)
        .single();
        
      if (identityError) {
        return NextResponse.json({ success: false, error: identityError.message }, { status: 500 });
      }
      
      externalIdentityId = identityData.id;
      
      // Update user's name data
      await supabaseServiceRole
        .from('external_identities')
        .update({
          whatsapp_name: firstName
        })
        .eq('id', externalIdentityId);
    }
    
    // Step 2: Get or create the bot record
    let botId: string;
    const { data: botData, error: botError } = await supabaseServiceRole
      .from('bots')
      .select('id')
      .eq('provider', provider)
      .eq('provider_bot_id', providerBotId)
      .single();
      
    if (botError && botError.code !== 'PGRST116') { // Not found error
      return NextResponse.json({ success: false, error: botError.message }, { status: 500 });
    }
    
    if (botData) {
      botId = botData.id;
    } else {
      // Create a new bot record
      const botName = provider === 'telegram' ? 'Telegram Bot' : 'WhatsApp Bot';
      const { data: newBotData, error: newBotError } = await supabaseServiceRole
        .from('bots')
        .insert({
          provider,
          provider_bot_id: providerBotId,
          name: botName
        })
        .select('id')
        .single();
        
      if (newBotError) {
        return NextResponse.json({ success: false, error: newBotError.message }, { status: 500 });
      }
      
      botId = newBotData.id;
    }
    
    // Step 3: Get or create the conversation
    const { data: conversationData, error: conversationError } = await supabaseServiceRole.rpc(
      'get_or_create_bot_conversation',
      {
        p_external_identity_id: externalIdentityId,
        p_bot_id: botId,
        p_provider: provider,
        p_provider_conversation_id: chatId
      }
    );
    
    if (conversationError) {
      return NextResponse.json({ success: false, error: conversationError.message }, { status: 500 });
    }
    
    const conversationId = conversationData;
    
    // Step 4: Record the user message
    const { data: messageData, error: messageError } = await supabaseServiceRole.rpc(
      'record_user_message',
      {
        p_conversation_id: conversationId,
        p_content: text
      }
    );
    
    if (messageError) {
      return NextResponse.json({ success: false, error: messageError.message }, { status: 500 });
    }
    
    // Step 5: Store user metadata if it's valuable for future processing
    // (optional, can be expanded upon if needed)
    
    // Step 6: Get wallet balance
    const { data: walletData, error: walletError } = await supabaseServiceRole
      .from('wallets')
      .select('balance')
      .eq('id', walletId)
      .single();
      
    if (walletError) {
      return NextResponse.json({ success: false, error: walletError.message }, { status: 500 });
    }
    
    // Return success response with all relevant IDs and context
    return NextResponse.json({
      success: true,
      data: {
        externalIdentityId,
        walletId,
        botId,
        conversationId,
        messageId: messageData,
        provider,
        providerUserId,
        balance: walletData.balance,
        userName: `${firstName}${lastName ? ' ' + lastName : ''}`,
        languageCode
      }
    });
    
  } catch (err: any) {
    console.error('Error in bot-receive-message API:', err);
    return NextResponse.json({ success: false, error: err.message }, { status: 500 });
  }
} 