.chatHeaderBox {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.chatHeaderRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chatId {
  font-size: 1rem;
}

.chatPhaseNumber {
  font-size: 3rem;
  font-weight: bold;
  color: #0070f3;
}

.progressContainer {
  max-width: 500px;
  width: 100%;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  margin-left: auto;
  margin-right: auto;
}

.progressCircleContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progressCircle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-weight: bold;
  transition: transform 0.2s;
  cursor: pointer;
}

.active {
  background: #0070f3;
}

.inactive {
  background: #ddd;
}

.progressLine {
  flex: 1;
  height: 4px;
  margin: 0 8px;
}

.tooltip {
  visibility: hidden;
  opacity: 0;
  background-color: gray;
  color: white;
  text-align: center;
  border-radius: 4px;
  padding: 4px 8px;
  position: absolute;
  top: -40px;
  white-space: nowrap;
  transition: opacity 0.2s;
  z-index: 10;
}

.progressCircleContainer:hover .tooltip {
  visibility: visible;
  opacity: 1;
}

.phaseDescription {
  text-align: center;
  margin-top: 0.5rem;
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 1rem;
}

.phaseSection {
  display: flex;
  justify-content: center;
  align-items: center;;
  flex-direction: column;
  
}

.progressCircle:hover {
  transform: scale(1.1);
}

.modalBackground {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.modalContent {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  max-width: 90%;
  max-height: 90%;
  overflow: auto;
  text-align: center;
}

.modalContent img {
  max-width: 100%;
  height: auto;
}

/* --- New styles for chat message bubbles --- */
.messageContainer {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.incomingMessage {
  align-self: flex-start;
  max-width: 70%;
  background-color: #f1f0f0;
  padding: 0.75rem;
  border-radius: 16px;
  border-top-left-radius: 0;
}

.outgoingMessage {
  align-self: flex-end;
  max-width: 70%;
  background-color: #0070f3;
  color: #fff;
  padding: 0.75rem;
  border-radius: 16px;
  border-top-right-radius: 0;
}

.messageMeta {
  font-size: 0.75rem;
  color: #555;
  margin-bottom: 0.25rem;
}

.messageText {
  font-size: 1rem;
  margin: 0;
}

.chatMessage {
  margin-bottom: 0.5rem;
}

.deleteReplyButton {
  position: absolute;
  top: 4px;
  right: 4px;
  background: transparent;
  border: none;
  cursor: pointer;
  color: red;
  font-weight: bold;
}

.movePhaseButton {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #0070f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.modalButtonGroup {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.confirmButton {
  padding: 0.5rem 1rem;
  background-color: #0070f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.cancelButton {
  padding: 0.5rem 1rem;
  background-color: #ddd;
  color: #333;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* Styles for phase 3: Starting image selection */
.startingImageContainer {
  position: relative;
  margin-top: 0.5rem;
}

.startingImage {
  max-width: 300px;
  border-radius: 4px;
  cursor: pointer;
}

.startingImageButtons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 0.5rem;
}

.viewImageButton,
.selectImageButton {
  padding: 0.5rem 1rem;
  border: none;
  background-color: #0070f3;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

.selectedOverlay {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background-color: green;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.snackbar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: #fff;
  padding: 1rem 1.5rem;
  border-radius: 4px;
  opacity: 0.9;
  z-index: 1000;
} 