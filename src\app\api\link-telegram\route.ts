import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  // Check x-api-key header
  const apiKey = request.headers.get('x-api-key');
  const validApiKey = process.env.BOT_API_KEY;
  if (!apiKey || apiKey !== validApiKey) {
    return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { code, telegram_id } = await request.json();
    if (!code || !telegram_id) {
      return NextResponse.json({ success: false, error: 'Missing code or telegram_id' }, { status: 400 });
    }

    // 1. Find the link code (unused, not expired)
    const { data: linkCode, error: codeError } = await supabase
      .from('link_codes')
      .select('*')
      .eq('code', code)
      .eq('used', false)
      .gte('expires_at', new Date().toISOString())
      .maybeSingle();
    if (codeError || !linkCode) {
      return NextResponse.json({ success: false, error: 'Invalid or expired code' }, { status: 400 });
    }

    // 2. Find the external_identity for this telegram_id
    const { data: extIdentity, error: extError } = await supabase
      .from('external_identities')
      .select('*')
      .eq('telegram_id', telegram_id)
      .maybeSingle();
    if (extError || !extIdentity) {
      return NextResponse.json({ success: false, error: 'Telegram user not found' }, { status: 404 });
    }

    // 3. Link the external_identity to the user_id from the code
    const { error: updateError } = await supabase
      .from('external_identities')
      .update({ user_id: linkCode.user_id })
      .eq('id', extIdentity.id);
    if (updateError) {
      return NextResponse.json({ success: false, error: 'Failed to link Telegram account' }, { status: 500 });
    }

    // 4. Mark the code as used and store the external_identity_id
    const { error: codeUpdateError } = await supabase
      .from('link_codes')
      .update({ used: true, external_identity_id: extIdentity.id })
      .eq('id', linkCode.id);
    if (codeUpdateError) {
      return NextResponse.json({ success: false, error: 'Failed to update code status' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (err: any) {
    return NextResponse.json({ success: false, error: err.message }, { status: 500 });
  }
} 