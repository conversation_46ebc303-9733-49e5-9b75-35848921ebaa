describe('Database Cleanup', () => {
  it('should clean up test data from the database', () => {
    // Log what we're about to do
    cy.log('Running manual cleanup of test data');
    
    // Clean all test data with 'test-' prefix
    cy.request({
      method: 'POST',
      url: `/api/test-cleanup?key=${Cypress.env('TEST_API_KEY') || 'test-api-key'}`,
      failOnStatusCode: false,
      body: {
        conversationPrefix: 'test-',
        olderThan: null // Clean up all test data regardless of age
      }
    }).then(response => {
      cy.log(`Cleanup status: ${response.status}`);
      cy.log(`Cleanup response: ${JSON.stringify(response.body)}`);
      
      // Verify the response
      expect(response.status).to.be.oneOf([200, 404]);
      
      if (response.status === 200) {
        expect(response.body).to.have.property('success');
        
        if (response.body.results) {
          Object.entries(response.body.results).forEach(([table, result]) => {
            cy.log(`Table ${table}: ${JSON.stringify(result)}`);
          });
        }
      }
    });
  });
  
  it('should clean up older test data (24h+)', () => {
    // Clean only older test data (useful for keeping recent test results)
    cy.request({
      method: 'POST',
      url: `/api/test-cleanup?key=${Cypress.env('TEST_API_KEY') || 'test-api-key'}`,
      failOnStatusCode: false,
      body: {
        olderThan: 24 // Clean data older than 24 hours
      }
    }).then(response => {
      cy.log(`Cleanup status: ${response.status}`);
      cy.log(`Cleanup response: ${JSON.stringify(response.body)}`);
      
      // Verify the response
      expect(response.status).to.be.oneOf([200, 404]);
    });
  });
}); 