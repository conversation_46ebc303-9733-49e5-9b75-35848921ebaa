.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 1.5rem;
  color: #0070f3;
}

.userSection {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.tokenContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tokenCount {
  font-weight: 500;
}

.profileContainer {
  position: relative;
}

.profileButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profileMenu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  min-width: 220px;
  width: max-content;
  z-index: 10;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  max-height: none;
  overflow: visible;
}

.menuItem {
  display: block;
  width: 100%;
  text-align: left;
  padding: 0.5rem 1rem;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  white-space: nowrap;
}

.menuItem:hover {
  background-color: #f5f5f5;
}

.loadingText {
  font-size: 0.9rem;
  color: #555;
}

.freeGenerationText {
  font-size: 0.95rem;
  font-weight: 600;
  color: #28a745;
  padding: 0.5rem 1rem;
  background: #f8f9fa;
  border-radius: 20px;
  border: 1px solid #e9ecef;
}

.nav {
  display: flex;
  gap: 1.5rem;
}

.navLink {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}

.navLink:hover {
  color: #0070f3;
}

.menuSection {
  padding: 0.25rem 0;
}

.menuSectionTitle {
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menuDivider {
  height: 1px;
  background: #e9ecef;
  margin: 0.5rem 0;
} 