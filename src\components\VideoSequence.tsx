"use client";

import React, { useState, useRef, useEffect } from 'react';
import styles from '../app/page.module.css';
import { logger } from '../utils/logger';

const videos = ['/demovid1.mp4', '/demovid2.mp4'];

const VideoSequence: React.FC = () => {
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const videoElement = videoRef.current;
    
    if (!videoElement) return;
    
    const handleVideoEnd = () => {
      // Move to the next video in the sequence
      setCurrentVideoIndex((prevIndex) => (prevIndex + 1) % videos.length);
    };
    
    videoElement.addEventListener('ended', handleVideoEnd);
    
    // Clean up event listener
    return () => {
      videoElement.removeEventListener('ended', handleVideoEnd);
    };
  }, []);

  // When the currentVideoIndex changes, load and play the new video
  useEffect(() => {
    const videoElement = videoRef.current;
    if (videoElement) {
      videoElement.load();
      videoElement.play().catch(error => {
        logger.error("Error playing video:", error);
      });
    }
  }, [currentVideoIndex]);

  return (
    <video 
      ref={videoRef}
      className={styles.heroVideo} 
      autoPlay 
      muted 
      playsInline
    >
      <source src={videos[currentVideoIndex]} type="video/mp4" />
      Your browser does not support the video tag.
    </video>
  );
};

export default VideoSequence; 