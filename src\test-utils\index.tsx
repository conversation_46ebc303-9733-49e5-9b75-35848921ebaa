import React from 'react'
import { render, RenderOptions } from '@testing-library/react'

// Mock providers for testing
const TestProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <>{children}</>
}

// Custom render function with providers
export const renderWithProviders = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => {
  return render(ui, { wrapper: TestProviders, ...options })
}

// Re-export everything from testing library
export * from '@testing-library/react'
export { renderWithProviders as render } 