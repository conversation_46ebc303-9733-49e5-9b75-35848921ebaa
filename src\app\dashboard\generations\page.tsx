"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from 'next/navigation';
import { supabase } from "@/lib/supabaseClient";
import styles from "../page.module.css";
import Header from '@/components/Header';
import { Download } from 'react-bootstrap-icons';

interface GeneratedImage {
  id: string;
  result_image_url: string;
  created_at: string;
  prompt_text: string | null;
  source_type: string;
  parameters: any;
  token_cost?: number;
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString();
};

export default function UserGenerationsPage() {
  const [images, setImages] = useState<GeneratedImage[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedImage, setSelectedImage] = useState<GeneratedImage | null>(null);
  const [session, setSession] = useState<any>(null);

  useEffect(() => {
    fetchSessionAndImages();
  }, []);

  const fetchSessionAndImages = async () => {
    setLoading(true);
    setError(null);
    try {
      // Get current user session
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        setError("Not authenticated");
        setLoading(false);
        return;
      }
      setSession(session);
      // 1. Get all external_identity IDs for this user
      const { data: externalIdentities, error: extError } = await supabase
        .from('external_identities')
        .select('id')
        .eq('user_id', session.user.id);
      if (extError) throw extError;
      const externalIds = (externalIdentities || []).map((ei: any) => ei.id);
      // 2. Get all wallet IDs for this user (web + linked external identities)
      let walletQuery = supabase.from('wallets').select('id');
      if (externalIds.length > 0) {
        walletQuery = walletQuery.or(`user_id.eq.${session.user.id},external_identity_id.in.(${externalIds.join(',')})`);
      } else {
        walletQuery = walletQuery.eq('user_id', session.user.id);
      }
      const { data: wallets, error: walletsError } = await walletQuery;
      if (walletsError) throw walletsError;
      const walletIds = wallets.map((w: any) => w.id);
      if (walletIds.length === 0) {
        setImages([]);
        setLoading(false);
        return;
      }
      // Fetch images for these wallet IDs
      const { data: images, error: imagesError } = await supabase
        .from('web_images')
        .select('*')
        .in('wallet_id', walletIds)
        .order('created_at', { ascending: false });
      if (imagesError) throw imagesError;
      setImages(images);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Header />
      <div className={styles.mainContainer}>
        <h1>Imaginile generate de tine</h1>
        {loading ? (
          <div>Se încarcă imaginile...</div>
        ) : error ? (
          <div style={{ color: 'red' }}>{error}</div>
        ) : images.length === 0 ? (
          <div>Nu ai generat încă nicio imagine.</div>
        ) : (
          <div className={styles.imagesGrid}>
            {images.map((img) => (
              <div
                key={img.id}
                className={styles.imageCard}
                onClick={() => { setSelectedImage(img); setShowDetails(true); }}
              >
                <button
                  className={styles.downloadButton}
                  title="Descarcă imaginea"
                  onClick={e => {
                    e.stopPropagation();
                    const link = document.createElement('a');
                    link.href = img.result_image_url;
                    link.download = `generated-image-${img.id}.jpg`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }}
                >
                  <Download size={28} />
                </button>
                <img
                  src={img.result_image_url}
                  alt={img.prompt_text || 'Imagine generată'}
                  className={styles.generatedImage}
                />
                <div className={styles.imageDetails}>
                  <div style={{ fontSize: 12, color: '#888' }}>{formatDate(img.created_at)}</div>
                </div>
              </div>
            ))}
          </div>
        )}
        {showDetails && selectedImage && (
          <div className={styles.imageModalOverlay} onClick={() => setShowDetails(false)}>
            <div className={styles.imageModal} onClick={e => e.stopPropagation()}>
              <button className={styles.closeModalButton} onClick={() => setShowDetails(false)}>
                ×
              </button>
              <button
                className={styles.downloadButton}
                title="Descarcă imaginea"
                onClick={e => {
                  e.stopPropagation();
                  const link = document.createElement('a');
                  link.href = selectedImage.result_image_url;
                  link.download = `generated-image-${selectedImage.id}.jpg`;
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }}
              >
                <Download size={28} />
              </button>
              {selectedImage && (
                <>
                  <img
                    src={selectedImage.result_image_url}
                    alt={selectedImage.prompt_text || 'Imagine generată'}
                    className={styles.modalImage}
                  />
                  <div className={styles.modalDetails}>
                    <div style={{ fontWeight: 500, fontSize: 18 }}>{selectedImage.prompt_text}</div>
                    <div style={{ fontSize: 14, color: '#888', marginBottom: 8 }}>{formatDate(selectedImage.created_at)}</div>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
} 