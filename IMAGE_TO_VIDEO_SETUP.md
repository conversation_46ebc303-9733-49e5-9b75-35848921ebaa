# Image to Video Generator Setup Guide

This guide explains how to set up and configure the Image to Video Generator feature that transforms static images into dynamic videos using AI.

## Overview

The Image to Video Generator uses the **Seedance v1 Pro** model from [fal.ai](https://fal.ai/models/fal-ai/bytedance/seedance/v1/pro/image-to-video) to provide AI-powered image-to-video generation capabilities. Users can upload images and use text prompts to create dynamic videos with motion and effects.

## Features

- **Image Upload**: Drag & drop or file picker for image selection
- **AI-Powered Video Generation**: Text-based prompts for video motion and effects
- **Advanced Settings**: Configurable duration (5-10 seconds) and resolution
- **Automatic Storage**: Generated videos are saved to Supabase storage
- **Cost Management**: Token-based pricing with balance checking
- **Error Handling**: Automatic refunds on failure

## Prerequisites

1. **FAL.ai Account**: You need a FAL.ai account and API key
2. **Supabase Storage**: Configured buckets for image and video storage
3. **Authentication**: Users must be signed in to generate videos

## Setup Instructions

### Step 1: Get Your FAL.ai API Key

1. Visit [fal.ai](https://fal.ai) and create an account
2. Navigate to your [API Keys section](https://fal.ai/dashboard/keys)
3. Create a new API key with appropriate permissions
4. Copy the API key (starts with `fal_`)

### Step 2: Configure Environment Variables

Add your FAL API key to your environment variables:

#### For Local Development (.env.local)
```bash
FAL_API_KEY=your_fal_api_key_here
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

#### For Production Deployment
Set the environment variables on your hosting platform:

**Vercel:**
1. Go to your project dashboard
2. Navigate to Settings > Environment Variables
3. Add the required environment variables

### Step 3: Database Setup

Run the database migration to create the required tables:

```sql
-- This creates the following tables:
-- - image_to_video_requests: Main requests table
-- - generated_videos: Video output tracking
-- - Indexes and RLS policies for security
```

The migration file is located at: `supabase/migrations/20250103000000_create_image_to_video_tables.sql`

### Step 4: Supabase Storage Setup

Ensure you have the following storage buckets configured:

1. **generated-images**: For storing uploaded images
2. **generated-videos**: For storing generated videos

#### Create Storage Buckets
```sql
-- Create buckets
INSERT INTO storage.buckets (id, name, public) VALUES ('generated-images', 'generated-images', true);
INSERT INTO storage.buckets (id, name, public) VALUES ('generated-videos', 'generated-videos', true);

-- Set up policies for generated-images bucket
CREATE POLICY "Users can upload images" ON storage.objects FOR INSERT WITH CHECK (
  bucket_id = 'generated-images' AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can view images" ON storage.objects FOR SELECT USING (
  bucket_id = 'generated-images' AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Set up policies for generated-videos bucket
CREATE POLICY "Users can upload videos" ON storage.objects FOR INSERT WITH CHECK (
  bucket_id = 'generated-videos' AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can view videos" ON storage.objects FOR SELECT USING (
  bucket_id = 'generated-videos' AND auth.uid()::text = (storage.foldername(name))[1]
);
```

### Step 5: Access the Image to Video Generator

1. Navigate to `/image-to-video` in your application
2. Or access it through the tools page at `/tools` (admin only)

## Usage

### For Regular Users

1. **Upload Image**: Click the upload area or drag & drop an image
2. **Enter Prompt**: Describe the motion and effects you want to see
3. **Configure Settings**: Choose duration and resolution
4. **Generate Video**: Click "Generate Video" to start the process
5. **Download Result**: Once complete, download your generated video

### For Administrators

1. **Monitor Requests**: Access `/admin/image-to-video-generations` to view all requests
2. **View Statistics**: See completion rates and performance metrics
3. **Manage Costs**: Track token usage and system performance

## API Endpoints

### POST `/api/image-to-video`

Generates a video from an uploaded image and prompt.

**Request Format:**
```javascript
const formData = new FormData();
formData.append('image', imageFile);
formData.append('prompt', 'Your video description');
formData.append('duration', '5');
formData.append('resolution', '1080p');

fetch('/api/image-to-video', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${session.access_token}`
  },
  body: formData
});
```

**Response Format:**
```json
{
  "request_id": "uuid",
  "status": "completed",
  "video": {
    "url": "https://...",
    "duration": 5,
    "resolution": "1080p",
    "prompt": "Your prompt"
  },
  "image_url": "https://...",
  "cost": 0.62,
  "tokens_charged": 62,
  "remaining_balance": 938
}
```

## Cost Structure

- **1080p Video (5 seconds)**: 62 tokens ($0.62)
- **Token Balance**: Users must have sufficient tokens before generation
- **Automatic Refunds**: Failed generations are automatically refunded

## Technical Details

### Dependencies Added
- `@fal-ai/client`: Official FAL.ai JavaScript client
- `sharp`: Image processing for optimization

### Files Created
- `src/app/api/image-to-video/route.ts`: Main API endpoint
- `src/app/image-to-video/page.tsx`: User interface
- `src/app/image-to-video/page.module.css`: Styling
- `src/app/admin/image-to-video-generations/page.tsx`: Admin interface
- `supabase/migrations/20250103000000_create_image_to_video_tables.sql`: Database schema

### Image Processing Flow

1. **Upload**: User uploads image → Processed with Sharp for optimization
2. **Storage**: Image stored in Supabase with proper file naming
3. **Generation**: FAL.ai Seedance model processes image + prompt
4. **Video Storage**: Generated video downloaded and stored in Supabase
5. **Result**: User receives video URL and download option

### Database Schema

#### image_to_video_requests
- `id`: Primary key
- `user_id`: Foreign key to auth.users
- `wallet_id`: Foreign key to wallets
- `status`: Processing status
- `image_url`: Source image URL
- `video_url`: Generated video URL
- `prompt`: User's text prompt
- `duration`: Video duration in seconds
- `resolution`: Video resolution
- `token_cost`: Cost in tokens
- `created_at`, `updated_at`, `completed_at`: Timestamps

#### generated_videos
- `id`: Primary key
- `request_id`: Foreign key to image_to_video_requests
- `video_url`: Video file URL
- `prompt_text`: Generation prompt
- `source_image_url`: Original image URL
- `fal_request_id`: FAL API request ID
- `token_cost`: Generation cost

## Security

- **Authentication**: Users must be signed in
- **Row Level Security**: Users can only access their own content
- **File Validation**: Images are processed and validated
- **Cost Protection**: Balance checking prevents overspending
- **Admin Access**: Administrative functions require admin role

## Troubleshooting

### Common Issues

1. **"FAL API key not configured"**
   - Ensure `FAL_API_KEY` environment variable is set
   - Verify the API key is valid and active

2. **"Failed to upload image"**
   - Check Supabase storage bucket permissions
   - Verify `SUPABASE_SERVICE_ROLE_KEY` is configured

3. **"Insufficient token balance"**
   - User needs to purchase more tokens
   - Check wallet balance in the database

4. **"Video generation failed"**
   - Check FAL.ai service status
   - Verify image format and size compatibility
   - Check server logs for specific errors

### Performance Optimization

1. **Image Optimization**: Images are automatically resized to optimal dimensions
2. **Progress Tracking**: Real-time status updates during generation
3. **Error Recovery**: Automatic retries and user-friendly error messages
4. **Resource Management**: Efficient memory usage for large files

## Support

For issues related to:
- **FAL.ai API**: Check [FAL.ai documentation](https://fal.ai/docs) or contact their support
- **Supabase Storage**: Verify bucket configuration and permissions
- **Image Processing**: Check image format and size requirements
- **Token Management**: Verify wallet and transaction records

## Changelog

### v1.0.0 (Initial Release)
- Basic image-to-video generation functionality
- Integration with Seedance v1 Pro model
- Supabase storage for images and videos
- Token-based cost management
- Admin monitoring interface
- Error handling and refunds
- Responsive design for mobile/desktop