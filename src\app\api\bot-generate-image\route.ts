import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client using the service role key (kept secret)
const supabaseServiceRole = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface BotGenerateImageRequest {
  conversationId: string;   // ID of the conversation
  walletId: string;         // ID of the user's wallet
  tokenCost: number;        // Number of tokens to consume for the operation
  operation: 'generate' | 'edit'; // Whether to generate a new image or edit an existing one
  promptText?: string;      // Text prompt for image generation
  originalUploadId?: string; // ID of the original upload to edit
  parameters?: Record<string, any>; // Additional parameters for the generation/editing
  resultUrl?: string;        // URL where the result image will be/has been stored
  imageBase64?: string;      // Base64 encoded image (optional)
  imageMimeType?: string;    // e.g., 'image/jpeg', 'image/png' (required if imageBase64 is provided)
}

function decodeBase64(base64: string): Uint8Array {
  const binaryString = Buffer.from(base64, 'base64').toString('binary');
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
}

/**
 * API endpoint for generating or editing images via the bot
 * Consumes tokens from the user's wallet and records the operation
 */
export async function POST(request: Request) {
  try {
    const {
      conversationId,
      walletId,
      tokenCost,
      operation,
      promptText,
      originalUploadId,
      parameters,
      resultUrl,
      imageBase64,
      imageMimeType
    } = await request.json() as BotGenerateImageRequest;
    
    // Validate required fields
    if (!conversationId || !walletId || !tokenCost || !operation) {
      return NextResponse.json({ 
        success: false, 
        error: "Missing required fields: conversationId, walletId, tokenCost, operation" 
      }, { status: 400 });
    }
    
    // Validate operation-specific parameters
    if (operation === 'generate' && !promptText) {
      return NextResponse.json({ 
        success: false, 
        error: "For 'generate' operation, promptText is required" 
      }, { status: 400 });
    }
    
    if (operation === 'edit' && !originalUploadId) {
      return NextResponse.json({ 
        success: false, 
        error: "For 'edit' operation, originalUploadId is required" 
      }, { status: 400 });
    }
    
    // Validate imageBase64 and imageMimeType
    if (imageBase64 && !imageMimeType) {
      return NextResponse.json({ 
        success: false, 
        error: "imageMimeType is required when providing imageBase64" 
      }, { status: 400 });
    }
    
    // Step 1: If we have a base64 image, upload it to Supabase Storage
    let finalImageUrl = resultUrl;
    if (imageBase64) {
      // Create a unique filename in a folder named after the conversationId
      const extension = imageMimeType?.split('/')[1] || 'jpg';
      const filename = `${conversationId}/${Date.now()}.${extension}`;
      const imageBuffer = Buffer.from(imageBase64, 'base64');
      // Upload to Supabase Storage (bucket: generated-images)
      const { data: uploadData, error: uploadError } = await supabaseServiceRole
        .storage
        .from('generated-images')
        .upload(filename, imageBuffer, {
          contentType: imageMimeType,
          upsert: false
        });
      if (uploadError) {
        return NextResponse.json({ 
          success: false, 
          error: "Failed to upload image: " + uploadError.message 
        }, { status: 500 });
      }
      // Get the public URL
      const { data: { publicUrl } } = supabaseServiceRole
        .storage
        .from('generated-images')
        .getPublicUrl(filename);
      finalImageUrl = publicUrl;
    }
    
    // Step 2: Validate the wallet exists
    const { data: walletData, error: walletError } = await supabaseServiceRole
      .from('wallets')
      .select('balance')
      .eq('id', walletId)
      .single();
      
    if (walletError) {
      return NextResponse.json({ 
        success: false, 
        error: "Wallet not found: " + walletError.message 
      }, { status: 404 });
    }
    
    // Step 3: Check if the wallet has enough tokens
    if (walletData.balance < tokenCost) {
      return NextResponse.json({ 
        success: false, 
        error: `Insufficient tokens. Required: ${tokenCost}, Available: ${walletData.balance}` 
      }, { status: 400 });
    }
    
    // Step 4: Generate or edit the image and consume tokens
    let imageId: string;
    if (operation === 'generate') {
      const { data, error } = await supabaseServiceRole.rpc(
        'generate_image_from_text',
        {
          p_wallet_id: walletId,
          p_conversation_id: conversationId,
          p_prompt_text: promptText,
          p_token_cost: tokenCost,
          p_result_url: finalImageUrl,
          p_parameters: parameters || null
        }
      );
      
      if (error) {
        return NextResponse.json({ success: false, error: error.message }, { status: 500 });
      }
      
      imageId = data;
    } else {
      const { data, error } = await supabaseServiceRole.rpc(
        'generate_edited_image',
        {
          p_wallet_id: walletId,
          p_conversation_id: conversationId,
          p_original_upload_id: originalUploadId,
          p_token_cost: tokenCost,
          p_result_url: finalImageUrl,
          p_parameters: parameters || null
        }
      );
      
      if (error) {
        return NextResponse.json({ success: false, error: error.message }, { status: 500 });
      }
      
      imageId = data;
    }
    
    // Step 5: Get the updated wallet balance
    const { data: updatedWalletData, error: updatedWalletError } = await supabaseServiceRole
      .from('wallets')
      .select('balance')
      .eq('id', walletId)
      .single();
      
    if (updatedWalletError) {
      return NextResponse.json({ success: false, error: updatedWalletError.message }, { status: 500 });
    }
    
    // Return success response with image data
    return NextResponse.json({
      success: true,
      data: {
        imageId,
        conversationId,
        walletId,
        tokenCost,
        operation,
        resultUrl: finalImageUrl,
        remainingBalance: updatedWalletData.balance
      }
    });
    
  } catch (err: any) {
    console.error('Error in bot-generate-image API:', err);
    return NextResponse.json({ success: false, error: err.message }, { status: 500 });
  }
} 