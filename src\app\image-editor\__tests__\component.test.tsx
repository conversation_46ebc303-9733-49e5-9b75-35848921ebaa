import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import ImageEditor from '../page'

// Mock CSS modules
jest.mock('../../admin.module.css', () => ({
  pageContainer: 'pageContainer',
  pageHeader: 'pageHeader',
  nsfwNotice: 'nsfwNotice',
  imageEditorContainer: 'imageEditorContainer',
  uploadSection: 'uploadSection',
  dropZone: 'dropZone',
  hasImage: 'hasImage',
  uploadPrompt: 'uploadPrompt',
  supportedFormats: 'supportedFormats',
  selectedImageContainer: 'selectedImageContainer',
  selectedImage: 'selectedImage',
  imageOverlay: 'imageOverlay',
  editControls: 'editControls',
  promptSection: 'promptSection',
  promptInput: 'promptInput',
  controlButtons: 'controlButtons',
  settingsButton: 'settingsButton',
  active: 'active',
  editButton: 'editButton',
  resetButton: 'resetButton',
  settingsPanel: 'settingsPanel',
  settingsGrid: 'settingsGrid',
  settingItem: 'settingItem',
  errorMessage: 'errorMessage',
  resultsSection: 'resultsSection',
  imageComparison: 'imageComparison',
  imagePanel: 'imagePanel',
  resultImage: 'resultImage',
  downloadButton: 'downloadButton',
  historySection: 'historySection',
  historyList: 'historyList',
  historyItem: 'historyItem',
  historyImages: 'historyImages',
  historyThumbnail: 'historyThumbnail',
  historyDetails: 'historyDetails',
  downloadHistoryButton: 'downloadHistoryButton',
}))

// Helper function to create mock files
const createMockFile = (name = 'test.jpg', type = 'image/jpeg') => {
  return new File(['test content'], name, { type })
}

describe('ImageEditor Component', () => {
  beforeEach(() => {
    // Reset fetch mock before each test
    global.fetch = jest.fn()
    jest.clearAllMocks()
  })

  it('renders the main interface correctly', () => {
    render(<ImageEditor />)
    
    // Check main elements
    expect(screen.getByText('Image Editor')).toBeTruthy()
    expect(screen.getByText('Upload an image and use AI to edit it with text prompts')).toBeTruthy()
    expect(screen.getByText('Upload an Image')).toBeTruthy()
    expect(screen.getByText('Drag and drop an image here, or click to select')).toBeTruthy()
    expect(screen.getByText('Supported: JPEG, PNG, WebP')).toBeTruthy()
  })

  it('has a file input element', () => {
    render(<ImageEditor />)
    const fileInput = document.querySelector('input[type="file"]')
    expect(fileInput).toBeTruthy()
    expect(fileInput?.getAttribute('accept')).toBe('image/*')
  })

  it('shows edit controls after file upload', async () => {
    const user = userEvent.setup()
    render(<ImageEditor />)
    
    const file = createMockFile()
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    
    await user.upload(fileInput, file)
    
    await waitFor(() => {
      expect(screen.getByDisplayValue('')).toBeTruthy() // prompt input
      expect(screen.getByText('Settings')).toBeTruthy()
      expect(screen.getByText('Edit Image')).toBeTruthy()
      expect(screen.getByText('Reset')).toBeTruthy()
    })
  })

  it('enables edit button when both image and prompt are provided', async () => {
    const user = userEvent.setup()
    render(<ImageEditor />)
    
    // Upload file
    const file = createMockFile()
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    await user.upload(fileInput, file)
    
    // Wait for edit controls to appear
    await waitFor(() => {
      expect(screen.getByText('Edit Image')).toBeTruthy()
    })
    
    // Initially edit button should be disabled
    const editButton = screen.getByText('Edit Image').closest('button')
    expect(editButton?.disabled).toBe(true)
    
    // Enter prompt
    const promptInput = screen.getByRole('textbox')
    await user.type(promptInput, 'Add a rainbow to the sky')
    
    // Edit button should now be enabled
    await waitFor(() => {
      expect(editButton?.disabled).toBe(false)
    })
  })

  it('toggles settings panel when settings button is clicked', async () => {
    const user = userEvent.setup()
    render(<ImageEditor />)
    
    // Upload a file first
    const file = createMockFile()
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    await user.upload(fileInput, file)
    
    await waitFor(() => {
      expect(screen.getByText('Settings')).toBeTruthy()
    })
    
    const settingsButton = screen.getByText('Settings')
    await user.click(settingsButton)
    
    await waitFor(() => {
      expect(screen.getByText('Edit Settings')).toBeTruthy()
      expect(screen.getByText('Safety Tolerance')).toBeTruthy()
      expect(screen.getByText('Output Format')).toBeTruthy()
    })
  })

  it('makes API call when edit button is clicked', async () => {
    const user = userEvent.setup()
    
    // Mock successful API response
    const mockResponse = {
      success: true,
      data: {
        images: [{ url: 'https://example.com/edited.jpg', width: 1024, height: 1024 }],
        seed: 12345,
        prompt: 'Add a rainbow to the sky',
      },
      requestId: 'test-123',
    }
    
    global.fetch = jest.fn().mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    })
    
    render(<ImageEditor />)
    
    // Upload file and enter prompt
    const file = createMockFile()
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    await user.upload(fileInput, file)
    
    await waitFor(() => {
      expect(screen.getByRole('textbox')).toBeTruthy()
    })
    
    const promptInput = screen.getByRole('textbox')
    await user.type(promptInput, 'Add a rainbow to the sky')
    
    // Click edit button
    const editButton = screen.getByText('Edit Image')
    await user.click(editButton)
    
    // Verify API was called
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/image-edit', expect.objectContaining({
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      }))
    })
  })

  it('handles API errors gracefully', async () => {
    const user = userEvent.setup()
    
    // Mock failed API response
    global.fetch = jest.fn().mockResolvedValueOnce({
      ok: false,
      json: async () => ({ error: 'API Error' }),
    })
    
    render(<ImageEditor />)
    
    // Upload file and enter prompt
    const file = createMockFile()
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    await user.upload(fileInput, file)
    
    await waitFor(() => {
      expect(screen.getByRole('textbox')).toBeTruthy()
    })
    
    const promptInput = screen.getByRole('textbox')
    await user.type(promptInput, 'Test prompt')
    
    // Click edit button
    const editButton = screen.getByText('Edit Image')
    await user.click(editButton)
    
    // Error should be displayed (this might appear in console due to error handling)
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalled()
    })
  })

  it('resets the editor when reset button is clicked', async () => {
    const user = userEvent.setup()
    render(<ImageEditor />)
    
    // Upload file and enter prompt
    const file = createMockFile()
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    await user.upload(fileInput, file)
    
    await waitFor(() => {
      expect(screen.getByRole('textbox')).toBeTruthy()
    })
    
    const promptInput = screen.getByRole('textbox')
    await user.type(promptInput, 'Test prompt')
    
    // Click reset button
    const resetButton = screen.getByText('Reset')
    await user.click(resetButton)
    
    // Should be back to initial state
    await waitFor(() => {
      expect(screen.getByText('Upload an Image')).toBeTruthy()
      expect(screen.queryByRole('textbox')).toBeFalsy()
    })
  })

  it('handles drag and drop file upload', () => {
    render(<ImageEditor />)
    
    const dropZone = screen.getByText('Upload an Image').closest('div')
    const file = createMockFile()
    
    const dataTransfer = {
      files: [file],
      items: [{ kind: 'file', type: file.type, getAsFile: () => file }],
      types: ['Files'],
    }
    
    // Simulate drag and drop
    fireEvent.drop(dropZone!, { dataTransfer })
    
    // File should be processed (this test verifies the drop handler exists)
    expect(dropZone).toBeTruthy()
  })

  it('displays results section and history after successful edit', async () => {
    const user = userEvent.setup()
    
    // Mock successful API response
    const mockResponse = {
      success: true,
      data: {
        images: [{ url: 'https://example.com/edited.jpg', width: 1024, height: 1024 }],
        seed: 12345,
        prompt: 'Add a rainbow to the sky',
      },
      requestId: 'test-123',
    }
    
    global.fetch = jest.fn().mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    })
    
    render(<ImageEditor />)
    
    // Upload file and enter prompt
    const file = createMockFile()
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    await user.upload(fileInput, file)
    
    await waitFor(() => {
      expect(screen.getByRole('textbox')).toBeTruthy()
    })
    
    const promptInput = screen.getByRole('textbox')
    await user.type(promptInput, 'Add a rainbow to the sky')
    
    // Click edit button
    const editButton = screen.getByText('Edit Image')
    await user.click(editButton)
    
    // Check that results and history sections appear
    await waitFor(() => {
      expect(screen.getByText('Results')).toBeTruthy()
      expect(screen.getByText('Edit History')).toBeTruthy()
    })
  })
}) 