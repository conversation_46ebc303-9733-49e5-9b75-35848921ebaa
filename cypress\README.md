# Cypress Tests for Bot API Endpoints

This directory contains end-to-end tests for the bot API endpoints using Cypress.

## API Endpoints Tested

1. `bot-receive-message` - Handles incoming messages from Telegram/WhatsApp
2. `bot-send-message` - Records bot responses in the database
3. `bot-generate-image` - Handles image generation/editing and token consumption
4. `bot-upload-file` - Handles file uploads from messaging platforms

## How to Run Tests

### Option 1: Interactive Mode

To run tests in interactive mode with the Cypress UI:

```bash
npm run test:open
```

This will:
1. Start the Next.js development server
2. Open the Cypress test runner
3. Allow you to select and run specific test files

### Option 2: Headless Mode (CI)

To run all tests in headless mode (good for CI):

```bash
npm run test:e2e
```

This will:
1. Start the Next.js development server
2. Run all Cypress tests
3. Output results to the console

## Test Structure

Each API endpoint has its own test file with multiple test cases:

- Happy path tests with valid data
- Error handling tests for invalid requests
- Edge case tests for specific scenarios

## Mock Data

Test data is defined in the `cypress.config.js` file under the `env.mockBotData` object.

## Continuous Integration

These tests are automatically run in GitHub Actions workflow on:
- Push to main or develop branch
- Pull requests to main or develop branch

See `.github/workflows/cypress.yml` for the CI configuration. 