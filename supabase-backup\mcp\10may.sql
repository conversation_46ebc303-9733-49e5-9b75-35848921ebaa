

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE OR REPLACE FUNCTION "public"."create_user_profile"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  -- Insert a new profile for the user with default values
  INSERT INTO public.profiles (user_id, tokens, role)
  VALUES (NEW.id, 100, 'user');
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."create_user_profile"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."deduct_tokens_for_new_chat"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE 
  current_tokens INTEGER;
BEGIN
  -- Look up the current user's token balance.
  SELECT tokens INTO current_tokens FROM profiles WHERE user_id = NEW.user_id;
  
  IF current_tokens IS NULL THEN
    RAISE EXCEPTION 'User profile not found for user_id: %', NEW.user_id;
  ELSIF current_tokens < 10 THEN
    RAISE EXCEPTION 'Insufficient tokens. Current balance: %', current_tokens;
  END IF;
  
  -- Deduct 10 tokens from the user's profile.
  UPDATE profiles 
  SET tokens = tokens - 10 
  WHERE user_id = NEW.user_id;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."deduct_tokens_for_new_chat"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."insert_user_profile"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  INSERT INTO profiles (user_id, tokens, role)
  VALUES (NEW.id, 10, 'user');  -- Default value of 10 tokens
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."insert_user_profile"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_chat_phase_if_conditions_met"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Only consider chats in phase 1.
  IF (SELECT phase FROM chats WHERE id = NEW.chat_id) = 1 THEN
    -- Check if there's at least one reply with non-empty text
    -- and at least one reply with a non-null image_url.
    IF EXISTS (
         SELECT 1
         FROM chat_replies 
         WHERE chat_id = NEW.chat_id 
           AND message IS NOT NULL 
           AND TRIM(message) <> ''
       )
       AND EXISTS (
         SELECT 1
         FROM chat_replies 
         WHERE chat_id = NEW.chat_id 
           AND image_url IS NOT NULL
       ) THEN
         -- Upgrade chat phase to 2.
         UPDATE chats 
         SET phase = 2 
         WHERE id = NEW.chat_id;
        -- Insert a system reply notifying the user.
        INSERT INTO chat_replies (chat_id, sender_role, message)
        VALUES (NEW.chat_id, 'system', 'Thank you for telling us what you require for your video. We will process your request and get back to you');
    END IF;
  END IF;
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_chat_phase_if_conditions_met"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."chat_replies" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "chat_id" "uuid" NOT NULL,
    "sender_role" "text" NOT NULL,
    "message" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "image_url" "text",
    "user_id" "uuid",
    "allow_starting_image" boolean DEFAULT false
);


ALTER TABLE "public"."chat_replies" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."chat_replies_with_user" AS
 SELECT "cr"."id",
    "cr"."chat_id",
    "cr"."sender_role",
    "cr"."message",
    "cr"."created_at",
    "cr"."image_url",
    "cr"."user_id",
    "u"."email" AS "user_email"
   FROM ("public"."chat_replies" "cr"
     LEFT JOIN "auth"."users" "u" ON (("cr"."user_id" = "u"."id")));


ALTER TABLE "public"."chat_replies_with_user" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."chats" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "start_time" timestamp with time zone DEFAULT "now"(),
    "phase" integer DEFAULT 1 NOT NULL,
    "starting_image_reply_id" "uuid",
    "starting_image_id" "uuid",
    CONSTRAINT "chk_phase" CHECK (("phase" = ANY (ARRAY[1, 2, 3, 4, 5])))
);


ALTER TABLE "public"."chats" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."profiles" (
    "user_id" "uuid" NOT NULL,
    "tokens" integer DEFAULT 100 NOT NULL,
    "role" "text" DEFAULT 'user'::"text" NOT NULL,
    CONSTRAINT "chk_role" CHECK (("role" = ANY (ARRAY['user'::"text", 'moderator'::"text", 'admin'::"text"])))
);


ALTER TABLE "public"."profiles" OWNER TO "postgres";


ALTER TABLE ONLY "public"."chat_replies"
    ADD CONSTRAINT "chat_replies_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."chats"
    ADD CONSTRAINT "chats_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_pkey" PRIMARY KEY ("user_id");



ALTER TABLE ONLY "public"."chat_replies"
    ADD CONSTRAINT "chat_replies_chat_id_fkey" FOREIGN KEY ("chat_id") REFERENCES "public"."chats"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."chat_replies"
    ADD CONSTRAINT "chat_replies_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."chats"
    ADD CONSTRAINT "chats_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."chats"
    ADD CONSTRAINT "fk_starting_image" FOREIGN KEY ("starting_image_id") REFERENCES "public"."chat_replies"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



CREATE POLICY "Allow Admins to Delete Chats" ON "public"."chats" FOR DELETE TO "authenticated" USING ((( SELECT "profiles"."role"
   FROM "public"."profiles"
  WHERE ("profiles"."user_id" = "auth"."uid"())) = 'admin'::"text"));



CREATE POLICY "Allow Admins to Insert Chats" ON "public"."chats" FOR INSERT TO "authenticated" WITH CHECK ((( SELECT "profiles"."role"
   FROM "public"."profiles"
  WHERE ("profiles"."user_id" = "auth"."uid"())) = 'admin'::"text"));



CREATE POLICY "Allow Admins to Select Chats" ON "public"."chats" FOR SELECT TO "authenticated" USING ((( SELECT "profiles"."role"
   FROM "public"."profiles"
  WHERE ("profiles"."user_id" = "auth"."uid"())) = 'admin'::"text"));



CREATE POLICY "Allow Admins to Update Chats" ON "public"."chats" FOR UPDATE TO "authenticated" WITH CHECK ((( SELECT "profiles"."role"
   FROM "public"."profiles"
  WHERE ("profiles"."user_id" = "auth"."uid"())) = 'admin'::"text"));



CREATE POLICY "Allow authenticated user to select their chats" ON "public"."chats" FOR SELECT USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Allow only authenticated user to insert their chat_replies" ON "public"."chat_replies" FOR INSERT WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Allow only authenticated user to insert their chats" ON "public"."chats" FOR INSERT WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Enable insert for authenticated users only" ON "public"."chat_replies" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable read access for all users" ON "public"."chat_replies" FOR SELECT USING (true);



ALTER TABLE "public"."chat_replies" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."chats" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "delete_reply_policy" ON "public"."chat_replies" FOR DELETE USING ((("auth"."uid"() = "user_id") OR (( SELECT "profiles"."role"
   FROM "public"."profiles"
  WHERE ("profiles"."user_id" = "auth"."uid"())) = 'admin'::"text")));





ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";






ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."chat_replies";



GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";











































































































































































GRANT ALL ON FUNCTION "public"."create_user_profile"() TO "anon";
GRANT ALL ON FUNCTION "public"."create_user_profile"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_user_profile"() TO "service_role";



GRANT ALL ON FUNCTION "public"."deduct_tokens_for_new_chat"() TO "anon";
GRANT ALL ON FUNCTION "public"."deduct_tokens_for_new_chat"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."deduct_tokens_for_new_chat"() TO "service_role";



GRANT ALL ON FUNCTION "public"."insert_user_profile"() TO "anon";
GRANT ALL ON FUNCTION "public"."insert_user_profile"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."insert_user_profile"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_chat_phase_if_conditions_met"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_chat_phase_if_conditions_met"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_chat_phase_if_conditions_met"() TO "service_role";


















GRANT ALL ON TABLE "public"."chat_replies" TO "anon";
GRANT ALL ON TABLE "public"."chat_replies" TO "authenticated";
GRANT ALL ON TABLE "public"."chat_replies" TO "service_role";



GRANT ALL ON TABLE "public"."chat_replies_with_user" TO "anon";
GRANT ALL ON TABLE "public"."chat_replies_with_user" TO "authenticated";
GRANT ALL ON TABLE "public"."chat_replies_with_user" TO "service_role";



GRANT ALL ON TABLE "public"."chats" TO "anon";
GRANT ALL ON TABLE "public"."chats" TO "authenticated";
GRANT ALL ON TABLE "public"."chats" TO "service_role";



GRANT ALL ON TABLE "public"."profiles" TO "anon";
GRANT ALL ON TABLE "public"."profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."profiles" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
