import { NextResponse } from 'next/server';

// Configure this route for static export
export const dynamic = 'force-static';
export const revalidate = false; // Use false to never revalidate

// POST /api/login
export async function GET() {
  try {
    // return hello world message
    return NextResponse.json({ message: 'Hello World' });

  } catch (error: any) {
    return NextResponse.json({ success: false, message: error.message }, { status: 500 });
  }
}

export async function POST() {
    try {
      // return hello world message
      return NextResponse.json({ message: 'Hello World' });

    } catch (error: any) {
      return NextResponse.json({ success: false, message: error.message }, { status: 500 });
    }
  } 