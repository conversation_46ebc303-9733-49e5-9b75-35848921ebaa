import React from 'react';
import styles from './ToolCard.module.css';
import Button from 'react-bootstrap/Button';

interface ToolCardProps {
  icon: React.ReactNode;
  title: string;
  category: string;
  status: string;
  description: string;
  features: string[];
  onLaunch: () => void;
  lang: 'en' | 'ro';
  t: (key: string) => string;
  disabled?: boolean;
}

const ToolCard: React.FC<ToolCardProps> = ({
  icon,
  title,
  category,
  status,
  description,
  features,
  onLaunch,
  lang,
  t,
  disabled
}) => {
  return (
    <div className={styles.toolCard}>
      <div className={styles.toolHeader}>
        <div className={styles.toolIcon}>{icon}</div>
        <div className={styles.toolInfo}>
          <h3>{title}</h3>
          <div className={styles.toolMeta}>
            <span className={styles.category}>{category}</span>
            <span className={`${styles.status} ${status.toLowerCase() === 'active' || status.toLowerCase() === 'activ' ? styles.active : ''}`}>{status}</span>
          </div>
        </div>
      </div>
      <p className={styles.toolDescription}>{description}</p>
      <div className={styles.toolFeatures}>
        <h4>{lang === 'ro' ? 'Funcționalități:' : 'Features:'}</h4>
        <ul>
          {Array.isArray(features) ? features.map((feature, index) => (
            <li key={index}>{feature}</li>
          )) : null}
        </ul>
      </div>
      <div className={styles.toolActions}>
        <Button 
          onClick={onLaunch}
          variant="primary"
          className={styles.launchButton}
          disabled={disabled}
        >
          {t('launch_tool')}
        </Button>
      </div>
    </div>
  );
};

export default ToolCard; 