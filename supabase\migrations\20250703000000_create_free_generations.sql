-- Create the new table to store free generation vouchers
CREATE TABLE free_generations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    status TEXT NOT NULL DEFAULT 'new', -- 'new', 'claimed', 'completed'
    flow_type TEXT NOT NULL DEFAULT 'mortal_kombat',
    web_generation_request_id UUID REFERENCES web_generation_requests(id) NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Add a unique constraint to ensure a voucher can only be linked to one generation request
ALTER TABLE free_generations
ADD CONSTRAINT unique_web_generation_request_id
UNIQUE (web_generation_request_id);

-- Add an index for faster lookups by status
CREATE INDEX idx_free_generations_status ON free_generations(status);

-- Alter the existing web_generation_requests table to allow anonymous users
-- These columns will be NULL for free generations.
ALTER TABLE web_generation_requests
ALTER COLUMN user_id DROP NOT NULL,
ALTER COLUMN wallet_id DROP NOT NULL;

-- Enable Row Level Security (RLS) for the new table
ALTER TABLE free_generations ENABLE ROW LEVEL SECURITY;

-- Create a policy that allows public access to view vouchers.
-- This is safe because the ID is a non-guessable UUID.
CREATE POLICY "Public access to view vouchers"
ON free_generations
FOR SELECT
USING (true);

-- Create a policy that allows admin users to create, update, and delete vouchers.
-- This assumes you have a way to identify admins, e.g., via a custom claim in their JWT.
-- You might need to adjust the `auth.jwt()->>'role' = 'admin'` part based on your setup.
CREATE POLICY "Admin full access"
ON free_generations
FOR ALL
USING (auth.jwt()->>'role' = 'admin')
WITH CHECK (auth.jwt()->>'role' = 'admin');
