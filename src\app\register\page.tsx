"use client";
import { useState } from 'react';
import { supabase } from '../../lib/supabaseClient';
import { useRouter } from 'next/navigation';
import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import styles from './register.module.css';

export default function Register() {
  const router = useRouter();
  const [message, setMessage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);
    setMessage(null);
    const formData = new FormData(event.currentTarget);
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;

    const { error } = await supabase.auth.signUp({
      email,
      password,
    });

    setIsSubmitting(false);
    if (error) {
      setMessage(`Error: ${error.message}`);
    } else {
      setMessage("Înregistrarea a fost realizată cu succes. Te rugăm să verifici emailul pentru confirmare.");
    }
  };

  const handleSocialLogin = async (provider: 'google') => {
    setIsSubmitting(true);
    setMessage(null);
    
    // Determine the correct redirect URL based on environment
    const currentOrigin = window.location.origin;
    const redirectUrl = currentOrigin.includes('localhost') 
      ? 'http://localhost:3000/auth/callback'
      : 'https://aivis.ro/auth/callback';
    
    const { error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: redirectUrl
      }
    });

    setIsSubmitting(false);
    if (error) {
      setMessage(`Error: ${error.message}`);
    }
  };

  const handleInputChange = () => {
    if (message) {
      setMessage(null);
    }
  };

  return (
    <div className={styles.container}>
      <main className={styles.main}>
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginBottom: '1.5rem' }}>
          <Link href="/" style={{ display: 'inline-block' }}>
            <Image src="/aivis-wide.png" alt="Aivis Logo" width={180} height={48} style={{ marginBottom: 12, objectFit: 'contain', cursor: 'pointer' }} />
          </Link>
        </div>
        <h1 className={styles.title}>Înregistrare</h1>
        <form className={styles.form} onSubmit={handleSubmit}>
          <div className={styles.inputGroup}>
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              placeholder="Emailul tău"
              required
              onChange={handleInputChange}
            />
          </div>
          <div className={styles.inputGroup}>
            <label htmlFor="password">Parolă</label>
            <input
              type="password"
              id="password"
              name="password"
              placeholder="Parola ta"
              required
              onChange={handleInputChange}
            />
          </div>
   
          <button 
            type="submit" 
            className={styles.submitButton}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Se înregistrează...' : 'Înregistrare'}
          </button>
        </form>

        <div style={{ margin: '1.5rem 0', textAlign: 'center', position: 'relative' }}>
          <hr style={{ border: 'none', borderTop: '1px solid #ddd', margin: '0' }} />
          <span style={{ 
            position: 'absolute', 
            top: '-10px', 
            left: '50%', 
            transform: 'translateX(-50%)', 
            background: '#fff', 
            padding: '0 1rem', 
            color: '#666', 
            fontSize: '0.9rem' 
          }}>
            sau
          </span>
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
          <button
            type="button"
            onClick={() => handleSocialLogin('google')}
            disabled={isSubmitting}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.5rem',
              padding: '0.75rem',
              border: '1px solid #ddd',
              borderRadius: '4px',
              background: '#fff',
              cursor: isSubmitting ? 'not-allowed' : 'pointer',
              fontSize: '0.95rem',
              fontWeight: '500',
              opacity: isSubmitting ? 0.7 : 1,
              transition: 'background 0.2s, border-color 0.2s'
            }}
            onMouseEnter={(e) => !isSubmitting && (e.currentTarget.style.background = '#f8f9fa')}
            onMouseLeave={(e) => !isSubmitting && (e.currentTarget.style.background = '#fff')}
          >
            <svg width="18" height="18" viewBox="0 0 24 24">
              <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            Înregistrează-te cu Google
          </button>
        </div>

        {message && <p className={styles.message}>{message}</p>}
        <p className={styles.linkText}>
          Ai deja cont?{' '}
          <Link href="/login">
            Autentifică-te aici
          </Link>
        </p>
      </main>
    </div>
  );
} 