/* Filter tabs styling */
.statusFilterTabs {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0;
}

.filterTab {
  padding: 12px 16px;
  border: none;
  background: transparent;
  color: #6b7280;
  border-radius: 6px 6px 0 0;
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.filterTab:hover {
  background: #f3f4f6;
  color: #374151;
}

.filterTab.active {
  font-weight: 600;
  color: white;
}

.filterTab.active.allTab {
  background: #3b82f6;
  border-bottom: 2px solid #3b82f6;
}

.filterTab.active.completedTab {
  background: #10b981;
  border-bottom: 2px solid #10b981;
}

.filterTab.active.processingTab {
  background: #f59e0b;
  border-bottom: 2px solid #f59e0b;
}

/* Page styling */
.videoGenerationsPage {
  min-height: 100vh;
  background: #f9fafb;
}

.videoGenerationsPage .container {
  max-width: 1200px;
} 