"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import styles from './forgotpassword.module.css';
import { supabase } from '../../lib/supabaseClient';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);
    setMessage(null);

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });

    setIsSubmitting(false);
    
    if (error) {
      setMessage(`Error: ${error.message}`);
    } else {
      setMessage("Password reset instructions sent to your email!");
    }
  };

  return (
    <div className={styles.container}>
      <main className={styles.main}>
        <h1 className={styles.title}>Reset Password</h1>
        <p className={styles.description}>
          Enter your email address and we'll send you instructions to reset your password.
        </p>
        <form className={styles.form} onSubmit={handleSubmit}>
          <div className={styles.inputGroup}>
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Your email"
              required
            />
          </div>
          <button 
            type="submit" 
            className={styles.submitButton}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Sending...' : 'Send Reset Instructions'}
          </button>
        </form>
        {message && <p className={message.includes('Error') ? styles.errorMessage : styles.successMessage}>{message}</p>}
        <p className={styles.linkText}>
          Remember your password?{' '}
          <Link href="/login">
            Back to login
          </Link>
        </p>
      </main>
    </div>
  );
} 