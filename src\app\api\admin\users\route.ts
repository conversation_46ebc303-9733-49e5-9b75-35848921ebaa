import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Define the interface for user data
interface UserRow {
  id: string;
  email: string;
  telegram_id: string | null;
  telegram_username: string | null;
  whatsapp_id: string | null;
  whatsapp_name: string | null;
  tokens: number;
  message_count: number;
  last_active: string | null;
  created_at: string;
  is_admin: boolean;
  is_bot_user: boolean;
  raw_user_meta_data: any;
}

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Missing or invalid authorization header' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    
    // Create Supabase client with service role key for server-side operations
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase configuration');
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Verify the user's token and check if they're an admin
    const { data: user, error: userError } = await supabase.auth.getUser(token);
    
    if (userError || !user?.user) {
      console.error('Authentication error:', userError);
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('user_id', user.user.id)
      .single();

    if (profileError || profile?.role !== 'admin') {
      console.error('Admin check error:', profileError);
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 });
    }

    // Call the SQL function to get admin users with activity
    const { data: users, error } = await supabase
      .rpc('get_admin_users_with_activity');

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 });
    }

    // Transform the data to match the expected UserRow interface
    const transformedUsers: UserRow[] = users.map((user: any) => ({
      id: user.id,
      email: user.email,
      telegram_id: user.telegram_id,
      telegram_username: user.telegram_username,
      whatsapp_id: user.whatsapp_id,
      whatsapp_name: user.whatsapp_name,
      tokens: user.tokens || 0,
      message_count: user.message_count || 0,
      last_active: user.last_active,
      created_at: user.created_at,
      is_admin: user.is_admin || false,
      is_bot_user: user.is_bot_user || false,
      raw_user_meta_data: user.raw_user_meta_data || {}
    }));

    return NextResponse.json({ users: transformedUsers });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 