import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client using the service role key (keep this key secret)
const supabaseServiceRole = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: Request) {
  try {
    const { userId, externalIdentityId, tokensToAdd, reason } = await request.json();
    
    if (tokensToAdd === undefined || tokensToAdd <= 0) {
      return NextResponse.json(
        { error: "tokensToAdd must be a positive number" },
        { status: 400 }
      );
    }

    if (!userId && !externalIdentityId) {
      return NextResponse.json(
        { error: "Either userId or externalIdentityId is required" },
        { status: 400 }
      );
    }

    let wallet;
    let walletError;

    // Find the wallet based on userId or externalIdentityId
    if (userId) {
      const { data, error } = await supabaseServiceRole
        .from("wallets")
        .select("*")
        .eq("user_id", userId)
        .maybeSingle();
      wallet = data;
      walletError = error;
    } else if (externalIdentityId) {
      const { data, error } = await supabaseServiceRole
        .from("wallets")
        .select("*")
        .eq("external_identity_id", externalIdentityId)
        .maybeSingle();
      wallet = data;
      walletError = error;
    }

    if (walletError) {
      return NextResponse.json({ error: walletError.message }, { status: 500 });
    }

    if (!wallet) {
      return NextResponse.json({ error: "Wallet not found for user" }, { status: 404 });
    }

    const currentBalance = wallet.balance || 0;
    const newBalance = currentBalance + Number(tokensToAdd);

    // Update the wallet balance
    const { error: updateError } = await supabaseServiceRole
      .from("wallets")
      .update({ 
        balance: newBalance,
        updated_at: new Date().toISOString()
      })
      .eq("id", wallet.id);

    if (updateError) {
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }

    // Create a token transaction record
    const { error: transactionError } = await supabaseServiceRole
      .from("token_transactions")
      .insert({
        wallet_id: wallet.id,
        amount: Number(tokensToAdd),
        description: reason || "Admin top-up",
        transaction_type: "admin_topup"
      });

    if (transactionError) {
      console.error("Failed to create transaction record:", transactionError);
      // Don't fail the request if transaction record fails, just log it
    }

    return NextResponse.json({ 
      success: true, 
      previousBalance: currentBalance,
      newBalance: newBalance,
      tokensAdded: Number(tokensToAdd)
    });
  } catch (err: any) {
    return NextResponse.json({ error: err.message }, { status: 500 });
  }
} 