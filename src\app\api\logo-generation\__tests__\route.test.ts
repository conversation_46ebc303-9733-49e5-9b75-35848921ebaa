import { POST } from '../route';
import { NextRequest } from 'next/server';

// Mock the dependencies
jest.mock('@supabase/supabase-js');
jest.mock('@fal-ai/client');

import { createClient } from '@supabase/supabase-js';
import { fal } from '@fal-ai/client';

const mockCreateClient = createClient as jest.Mock;
const mockFal = fal as jest.Mocked<typeof fal>;

describe('POST /api/logo-generation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock environment variables
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
    process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key';
    process.env.FAL_API_KEY = 'test-fal-api-key';
    process.env.OPENAI_API_KEY = 'test-openai-key';
  });

  describe('Authentication', () => {
    it('should return 401 when no authorization header is provided', async () => {
      const request = new NextRequest('http://localhost/api/logo-generation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt: 'test logo', style: 'minimalist' }),
      });

      const response = await POST(request);
      const body = await response.json();

      expect(response.status).toBe(401);
      expect(body.error).toBe('Authorization header is required');
    });

    it('should return 401 when authorization header is malformed', async () => {
      const request = new NextRequest('http://localhost/api/logo-generation', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'InvalidHeader'
        },
        body: JSON.stringify({ prompt: 'test logo', style: 'minimalist' }),
      });

      const response = await POST(request);
      const body = await response.json();

      expect(response.status).toBe(401);
      expect(body.error).toBe('Authorization header is required');
    });
  });

  describe('Basic functionality', () => {
    it('should handle authentication and admin access requirements', async () => {
      const request = new NextRequest('http://localhost/api/logo-generation', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({ prompt: 'test logo', style: 'minimalist' }),
      });

      const response = await POST(request);
      const body = await response.json();
      
      // Should not be 401 (authentication header is provided)
      expect(response.status).not.toBe(401);
      
      // The response should be defined
      expect(response).toBeDefined();
      expect(body).toBeDefined();
      
      // Should either succeed or fail with proper error handling
      // (500 for missing env vars, or 200 for success)
      expect([200, 500]).toContain(response.status);
    });
  });

  describe('Environment validation', () => {
    it('should check for required environment variables', async () => {
      // Test that the route requires proper environment setup
      const request = new NextRequest('http://localhost/api/logo-generation', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        },
        body: JSON.stringify({ prompt: 'test logo', style: 'minimalist' }),
      });

      // The route should attempt to process the request
      const response = await POST(request);
      
      // Should return some response (not crash)
      expect(response).toBeDefined();
      expect(typeof response.status).toBe('number');
    });
  });
}); 