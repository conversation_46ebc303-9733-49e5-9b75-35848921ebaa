import { loadStripe } from '@stripe/stripe-js'
import { logger } from '../utils/logger'

// This is the publishable key - safe to use on client side
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)

export default stripePromise

// Helper function for creating checkout sessions
export const createCheckoutSession = async (packageId: string, userId: string) => {
  try {
    const response = await fetch('/api/stripe/create-checkout-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        packageId,
        userId
      })
    })

    if (!response.ok) {
      throw new Error('Failed to create checkout session')
    }

    const { url } = await response.json()
    
    // Redirect to Stripe Checkout
    window.location.href = url
    
  } catch (error) {
    logger.error('Error creating checkout session:', error)
    throw error
  }
}

// Helper function to fetch token packages
export const fetchTokenPackages = async () => {
  try {
    const response = await fetch('/api/stripe/token-packages')
    
    if (!response.ok) {
      throw new Error('Failed to fetch token packages')
    }
    
    const data = await response.json()
    return data.packages
    
  } catch (error) {
    logger.error('Error fetching token packages:', error)
    throw error
  }
} 