.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main {
  flex: 1;
  padding: 2rem;
}

/* Hero Section */
.hero {
  display: flex;
  align-items: center;
  gap: 4rem;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 1rem;
  margin-bottom: 4rem;
}

.heroContent {
  flex: 1;
}

.title {
  font-size: 3.5rem;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  color: #1a1a1a;
}

.description {
  font-size: 1.25rem;
  color: #4a4a4a;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
}

.primaryButton {
  padding: 0.75rem 1.5rem;
  background-color: #0070f3;
  color: white;
  border-radius: 0.5rem;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
}

.primaryButton:hover {
  background-color: #0051a8;
}

.secondaryButton {
  padding: 0.75rem 1.5rem;
  background-color: white;
  color: #0070f3;
  border: 2px solid #0070f3;
  border-radius: 0.5rem;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s;
}

.secondaryButton:hover {
  background-color: #f0f7ff;
}

.heroVideoContainer {
  flex: 1;
  position: relative;
}

.videoFrame {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.imageOverlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.videoDecoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 1rem;
  pointer-events: none;
}

.videoWrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.videoControls {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(0, 0, 0, 0.5);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
}

.controlDot {
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}

.progressBar {
  width: 100px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.progressFill {
  width: 30%;
  height: 100%;
  background: white;
  border-radius: 2px;
}

/* Features Section */
.features {
  padding: 4rem 0;
}

.sectionTitle {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #1a1a1a;
}

.featureGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  padding: 0 2rem;
}

.featureCard {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.featureCard:hover {
  transform: translateY(-5px);
}

.featureIcon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.featureCard h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #1a1a1a;
}

.featureCard p {
  color: #4a4a4a;
  line-height: 1.6;
}

/* CTA Section */
.cta {
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #c3cfe2 0%, #f5f7fa 100%);
  border-radius: 1rem;
  margin: 4rem 0;
}

.cta h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #1a1a1a;
}

.cta p {
  font-size: 1.25rem;
  color: #4a4a4a;
  margin-bottom: 2rem;
}

/* Footer */
.footer {
  background-color: #1a1a1a;
  color: white;
  padding: 4rem 2rem;
}

.footerContent {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 2fr 3fr;
  gap: 4rem;
}

.footerLogo p {
  margin-top: 1rem;
  color: #a0a0a0;
}

.footerLinks {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.footerLinkColumn h4 {
  margin-bottom: 1rem;
  color: white;
}

.footerLinkColumn a {
  display: block;
  color: #a0a0a0;
  text-decoration: none;
  margin-bottom: 0.5rem;
}

.footerLinkColumn a:hover {
  color: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero {
    flex-direction: column;
    text-align: center;
  }

  .ctaButtons {
    justify-content: center;
  }

  .heroVideoContainer {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 2.5rem;
  }

  .footerContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footerLinks {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2rem;
  }

  .ctaButtons {
    flex-direction: column;
  }

  .featureGrid {
    grid-template-columns: 1fr;
  }
} 