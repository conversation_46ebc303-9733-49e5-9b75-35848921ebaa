import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { fal } from '@fal-ai/client';

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export async function GET(request: NextRequest) {
  try {
    console.log('Checking video generation status...');

    const { searchParams } = new URL(request.url);
    const requestId = searchParams.get('id');

    if (!requestId) {
      return NextResponse.json({ 
        error: 'Request ID is required' 
      }, { status: 400 });
    }

    // Get user from auth header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');
    
    if (!token) {
      return NextResponse.json({ 
        error: 'Authentication required' 
      }, { status: 401 });
    }

    // Verify user authentication
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ 
        error: 'Invalid authentication' 
      }, { status: 401 });
    }

    // Get the request from database
    const { data: videoRequest, error: fetchError } = await supabaseAdmin
      .from('image_to_video_requests')
      .select('*')
      .eq('id', requestId)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('Error fetching video request:', fetchError);
      return NextResponse.json({ 
        error: 'Video request not found',
        details: fetchError.message
      }, { status: 404 });
    }

    if (!videoRequest) {
      return NextResponse.json({ 
        error: 'Video request not found or access denied' 
      }, { status: 404 });
    }

    // If status is already completed or failed, return immediately
    if (videoRequest.status === 'completed' || videoRequest.status === 'failed') {
      return NextResponse.json({
        request_id: videoRequest.id,
        status: videoRequest.status,
        video_url: videoRequest.video_url,
        image_url: videoRequest.image_url,
        prompt: videoRequest.prompt,
        duration: videoRequest.duration,
        resolution: videoRequest.resolution,
        tokens_charged: videoRequest.token_cost,
        error_message: videoRequest.error_message
      });
    }

    // If status is processing and we have a fal_request_id, poll FAL.ai
    if (videoRequest.status === 'processing' && videoRequest.fal_request_id) {
      try {
        console.log(`Polling FAL.ai for request: ${videoRequest.fal_request_id}`);
        
        // Get status from FAL.ai
        const falStatus = await fal.queue.status("fal-ai/bytedance/seedance/v1/pro/image-to-video", {
          requestId: videoRequest.fal_request_id,
          logs: false
        });

        console.log(`FAL.ai status:`, falStatus);

        if (falStatus.status === 'COMPLETED') {
          // Get the completed result
          const falResult = await fal.queue.result("fal-ai/bytedance/seedance/v1/pro/image-to-video", {
            requestId: videoRequest.fal_request_id
          });

          console.log('FAL.ai result:', falResult);

          const videoUrl = falResult.data?.video?.url;
          
          if (videoUrl) {
            // Update database with completed video
            await supabaseAdmin
              .from('image_to_video_requests')
              .update({
                status: 'completed',
                video_url: videoUrl,
                updated_at: new Date().toISOString()
              })
              .eq('id', requestId);

            return NextResponse.json({
              request_id: videoRequest.id,
              status: 'completed',
              video_url: videoUrl,
              image_url: videoRequest.image_url,
              prompt: videoRequest.prompt,
              duration: videoRequest.duration,
              resolution: videoRequest.resolution,
              tokens_charged: videoRequest.token_cost
            });
          } else {
            // No video URL in response - mark as failed
            await supabaseAdmin
              .from('image_to_video_requests')
              .update({
                status: 'failed',
                error_message: 'Video generation completed but no video URL received',
                updated_at: new Date().toISOString()
              })
              .eq('id', requestId);

            return NextResponse.json({
              request_id: videoRequest.id,
              status: 'failed',
              error_message: 'Video generation completed but no video URL received',
              image_url: videoRequest.image_url,
              prompt: videoRequest.prompt,
              duration: videoRequest.duration,
              resolution: videoRequest.resolution,
              tokens_charged: videoRequest.token_cost
            });
          }
        } else if ('error' in falStatus && falStatus.error) {
          // Handle error case - check if there's an error field
          const errorMessage = (falStatus as any).error || 'Video generation failed';
          
          await supabaseAdmin
            .from('image_to_video_requests')
            .update({
              status: 'failed',
              error_message: errorMessage,
              updated_at: new Date().toISOString()
            })
            .eq('id', requestId);

          // Refund tokens
          const { data: wallet } = await supabaseAdmin
            .from('wallets')
            .select('id, balance')
            .eq('user_id', user.id)
            .single();

          if (wallet) {
            const refundAmount = videoRequest.token_cost || 62;
            
            await supabaseAdmin
              .from('wallets')
              .update({ 
                balance: wallet.balance + refundAmount,
                updated_at: new Date().toISOString()
              })
              .eq('user_id', user.id);

            // Create refund transaction
            await supabaseAdmin
              .from('token_transactions')
              .insert({
                wallet_id: wallet.id,
                amount: refundAmount,
                description: `Refund: Video generation failed - ${videoRequest.prompt?.substring(0, 50) || 'No prompt'}`,
                transaction_type: 'refund'
              });
          }

          return NextResponse.json({
            request_id: videoRequest.id,
            status: 'failed',
            error_message: errorMessage,
            image_url: videoRequest.image_url,
            prompt: videoRequest.prompt,
            duration: videoRequest.duration,
            resolution: videoRequest.resolution,
            tokens_charged: videoRequest.token_cost
          });
        } else {
          // Still processing (IN_PROGRESS, IN_QUEUE, etc.)
          return NextResponse.json({
            request_id: videoRequest.id,
            status: 'processing',
            image_url: videoRequest.image_url,
            prompt: videoRequest.prompt,
            duration: videoRequest.duration,
            resolution: videoRequest.resolution,
            tokens_charged: videoRequest.token_cost
          });
        }

      } catch (falError: any) {
        console.error('Error polling FAL.ai:', falError);
        
        // If we can't reach FAL.ai, just return processing status
        // Don't mark as failed yet - could be temporary network issue
        return NextResponse.json({
          request_id: videoRequest.id,
          status: 'processing',
          image_url: videoRequest.image_url,
          prompt: videoRequest.prompt,
          duration: videoRequest.duration,
          resolution: videoRequest.resolution,
          tokens_charged: videoRequest.token_cost
        });
      }
    }

    // Default return for processing without fal_request_id
    return NextResponse.json({
      request_id: videoRequest.id,
      status: videoRequest.status,
      image_url: videoRequest.image_url,
      prompt: videoRequest.prompt,
      duration: videoRequest.duration,
      resolution: videoRequest.resolution,
      tokens_charged: videoRequest.token_cost,
      error_message: videoRequest.error_message
    });

  } catch (error: any) {
    console.error('Error in video status check:', error);
    return NextResponse.json(
      { error: 'Failed to check video status', details: error.message },
      { status: 500 }
    );
  }
} 