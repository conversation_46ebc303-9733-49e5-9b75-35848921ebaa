"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import styles from './login.module.css';
import { supabase } from '../../lib/supabaseClient';
import Image from 'next/image';
import { logger } from '../../utils/logger';

export default function Login() {
  const router = useRouter();
  const [message, setMessage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);
    setMessage(null);
    const formData = new FormData(event.currentTarget);
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;

    const { error, data } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    setIsSubmitting(false);
    if (error) {
      setMessage(`Error: ${error.message}`);
    } else {
      setMessage("Login successful!");
      logger.log('User data:', data);
      router.push("/dashboard");
    }
  };

  const handleSocialLogin = async (provider: 'google' | 'facebook' | 'github') => {
    setIsSubmitting(true);
    setMessage(null);
    
    // Determine the correct redirect URL based on environment
    const currentOrigin = window.location.origin;
    const redirectUrl = currentOrigin.includes('localhost') 
      ? 'http://localhost:3000/auth/callback'
      : 'https://aivis.ro/auth/callback';
    
    const { error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: redirectUrl
      }
    });

    setIsSubmitting(false);
    if (error) {
      setMessage(`Error: ${error.message}`);
    }
  };

  const handleInputChange = () => {
    if (message) {
      setMessage(null);
    }
  };

  return (
    <div className={styles.container}>
      <main className={styles.main}>
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginBottom: '1.5rem' }}>
          <Link href="/" style={{ display: 'inline-block' }}>
            <Image src="/aivis-wide.png" alt="Aivis Logo" width={180} height={48} style={{ marginBottom: 12, objectFit: 'contain', cursor: 'pointer' }} />
          </Link>
        </div>
        <h1 className={styles.title}>Autentificare</h1>
        <form className={styles.form} onSubmit={handleSubmit}>
          <div className={styles.inputGroup}>
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              placeholder="Emailul tău"
              required
              onChange={handleInputChange}
            />
          </div>
          <div className={styles.inputGroup}>
            <label htmlFor="password">Parolă</label>
            <input
              type="password"
              id="password"
              name="password"
              placeholder="Parola ta"
              required
              onChange={handleInputChange}
            />
          </div>
          <button 
            type="submit" 
            className={styles.submitButton}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Se autentifică...' : 'Autentificare'}
          </button>
        </form>

        <div style={{ margin: '1.5rem 0', textAlign: 'center', position: 'relative' }}>
          <hr style={{ border: 'none', borderTop: '1px solid #ddd', margin: '0' }} />
          <span style={{ 
            position: 'absolute', 
            top: '-10px', 
            left: '50%', 
            transform: 'translateX(-50%)', 
            background: '#fff', 
            padding: '0 1rem', 
            color: '#666', 
            fontSize: '0.9rem' 
          }}>
            sau
          </span>
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
          <button
            type="button"
            onClick={() => handleSocialLogin('google')}
            disabled={isSubmitting}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.5rem',
              padding: '0.75rem',
              border: '1px solid #ddd',
              borderRadius: '4px',
              background: '#fff',
              cursor: isSubmitting ? 'not-allowed' : 'pointer',
              fontSize: '0.95rem',
              fontWeight: '500',
              opacity: isSubmitting ? 0.7 : 1,
              transition: 'background 0.2s, border-color 0.2s'
            }}
            onMouseEnter={(e) => !isSubmitting && (e.currentTarget.style.background = '#f8f9fa')}
            onMouseLeave={(e) => !isSubmitting && (e.currentTarget.style.background = '#fff')}
          >
            <svg width="18" height="18" viewBox="0 0 24 24">
              <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            Continuă cu Google
          </button>

          {/* Facebook and GitHub buttons disabled for now */}
          {/*
          <button
            type="button"
            onClick={() => handleSocialLogin('facebook')}
            disabled={isSubmitting}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.5rem',
              padding: '0.75rem',
              border: '1px solid #ddd',
              borderRadius: '4px',
              background: '#fff',
              cursor: isSubmitting ? 'not-allowed' : 'pointer',
              fontSize: '0.95rem',
              fontWeight: '500',
              opacity: isSubmitting ? 0.7 : 1,
              transition: 'background 0.2s, border-color 0.2s'
            }}
            onMouseEnter={(e) => !isSubmitting && (e.currentTarget.style.background = '#f8f9fa')}
            onMouseLeave={(e) => !isSubmitting && (e.currentTarget.style.background = '#fff')}
          >
            <svg width="18" height="18" viewBox="0 0 24 24">
              <path fill="#1877f2" d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
            </svg>
            Continuă cu Facebook
          </button>

          <button
            type="button"
            onClick={() => handleSocialLogin('github')}
            disabled={isSubmitting}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.5rem',
              padding: '0.75rem',
              border: '1px solid #ddd',
              borderRadius: '4px',
              background: '#fff',
              cursor: isSubmitting ? 'not-allowed' : 'pointer',
              fontSize: '0.95rem',
              fontWeight: '500',
              opacity: isSubmitting ? 0.7 : 1,
              transition: 'background 0.2s, border-color 0.2s'
            }}
            onMouseEnter={(e) => !isSubmitting && (e.currentTarget.style.background = '#f8f9fa')}
            onMouseLeave={(e) => !isSubmitting && (e.currentTarget.style.background = '#fff')}
          >
            <svg width="18" height="18" viewBox="0 0 24 24">
              <path fill="#333" d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            Continuă cu GitHub
          </button>
          */}
        </div>

        {message && (
          <div className={styles.messageContainer}>
            <p className={styles.message}>{message}</p>
            {message.includes("Error") && (
              <p className={styles.forgotPassword}>
                <Link href="/forgotpassword">Ai uitat parola?</Link>
              </p>
            )}
          </div>
        )}
        <p className={styles.linkText}>
          Nu ai cont?{' '}
          <Link href="/register">
            Înregistrează-te aici
          </Link>
        </p>
      </main>
    </div>
  );
} 