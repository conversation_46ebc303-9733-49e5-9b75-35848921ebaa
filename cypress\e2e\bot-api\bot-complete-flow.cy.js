describe('Complete Bot API Flow', () => {
  const telegramId = 'telegram-test-id-123';
  const base64Image = 'iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAKElEQVQ4jWNgYGD4Twzu6FhFFGYYNXDUwGFpIAk2E4dHDRw1cDgaCAASFOffhEIO3gAAAABJRU5ErkJggg==';
  
  let conversationId;
  let walletId;
  let generatedImageUrl;
  let initialWalletBalance;
  let externalIdentityId;
  let uploadedFileUrl;
  
  it('1. should retrieve user data before starting', () => {
    cy.getUserData({ telegramId }).then((response) => {
      cy.log(`Initial User Data: ${JSON.stringify(response.body)}`);
      
      if (response.status === 200 && response.body.exists) {
        // User exists, store wallet data
        expect(response.body).to.have.property('wallet');
        walletId = response.body.wallet.id;
        initialWalletBalance = response.body.wallet.balance;
        
        cy.log(`Found existing user with wallet ID: ${walletId}`);
        cy.log(`Initial wallet balance: ${initialWalletBalance}`);
      } else {
        cy.log('User not found or exists without wallet');
      }
    });
  });
  
  it('2. should receive a message and start a conversation', () => {
    const messagePayload = {
      "messageId": "test-message-id-" + Date.now(),
      "firstName": "Test",
      "lastName": "User",
      "languageCode": "en",
      "chatId": "test-chat-id-" + Date.now(),
      "text": "Hello! I'd like an image of mountains.",
      "provider": "telegram",
      "providerUserId": telegramId,
      "providerBotId": "test-bot-id"
    };
    
    cy.botReceiveMessage(messagePayload).then((response) => {
      cy.log(`Receive message response: ${JSON.stringify(response.body)}`);
      
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('conversationId');
      
      conversationId = response.body.data.conversationId;
      
      // Use wallet ID from previous test or get it from response
      if (!walletId && response.body.data.walletId) {
        walletId = response.body.data.walletId;
      }
      
      // Store external identity ID for file upload
      externalIdentityId = response.body.data.externalIdentityId;
      
      cy.log(`Conversation ID: ${conversationId}`);
      cy.log(`Wallet ID: ${walletId || 'Not available'}`);
      cy.log(`External Identity ID: ${externalIdentityId || 'Not available'}`);
    });
  });
  
  it('3. should upload a file from the user', () => {
    // Skip if required data is missing
    if (!conversationId || !externalIdentityId) {
      cy.log('Skipping test: missing conversation ID or external identity ID');
      return;
    }
    
    const uploadPayload = {
      "conversationId": conversationId,
      "externalIdentityId": externalIdentityId,
      "fileBase64": base64Image,
      "fileMetadata": {
        "originalFilename": "user_reference_image.png",
        "mimeType": "image/png",
        "size": 1024
      },
      "recordAsMessage": true,
      "sender": "user",
      "textMessage": "Here's a reference image for the mountains I want."
    };
    
    cy.botUploadFile(uploadPayload).then((response) => {
      cy.log(`Upload file response: ${JSON.stringify(response.body)}`);
      
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('uploadId');
      expect(response.body.data).to.have.property('fileUrl');
      
      uploadedFileUrl = response.body.data.fileUrl;
      cy.log(`Uploaded file URL: ${uploadedFileUrl}`);
    });
  });
  
  it('4. should generate an image', () => {
    // Skip if required data is missing
    if (!conversationId || !walletId) {
      cy.log('Skipping test: missing conversation ID or wallet ID');
      return;
    }
    
    const generatePayload = {
      "conversationId": conversationId,
      "walletId": walletId,
      "tokenCost": 5,
      "operation": "generate",
      "promptText": "Beautiful mountains with snow caps and a blue sky",
      "parameters": {
        "style": "realistic",
        "width": 1024,
        "height": 1024
      },
      "imageBase64": base64Image,
      "imageMimeType": "image/png"
    };
    
    cy.botGenerateImage(generatePayload).then((response) => {
      cy.log(`Generate image response: ${JSON.stringify(response.body)}`);
      
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('success', true);
      
      if (response.body.data) {
        expect(response.body.data).to.have.property('imageId');
        expect(response.body.data).to.have.property('resultUrl');
        
        generatedImageUrl = response.body.data.resultUrl;
        cy.log(`Generated image URL: ${generatedImageUrl}`);
      }
    });
  });
  
  it('5. should send a message with the generated image', () => {
    // Skip if required data is missing
    if (!conversationId || !generatedImageUrl) {
      cy.log('Skipping test: missing conversation ID or image URL');
      return;
    }
    
    const sendMessagePayload = {
      "conversationId": conversationId,
      "text": "Here is your mountain image!",
      "imageUrl": generatedImageUrl
    };
    
    cy.botSendMessage(sendMessagePayload).then((response) => {
      cy.log(`Send message response: ${JSON.stringify(response.body)}`);
      
      // Only accept 200 status code now that we've fixed the payload format
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('success', true);
      cy.log('Message sent successfully');
    });
  });
  
  it('6. should upload a file from the bot', () => {
    // Skip if required data is missing
    if (!conversationId || !externalIdentityId || !generatedImageUrl) {
      cy.log('Skipping test: missing required data');
      return;
    }
    
    const botUploadPayload = {
      "conversationId": conversationId,
      "externalIdentityId": externalIdentityId,
      "fileUrl": generatedImageUrl, // Using the generated image URL
      "fileMetadata": {
        "originalFilename": "bot_generated_image.png",
        "mimeType": "image/png"
      },
      "recordAsMessage": true,
      "sender": "bot",
      "textMessage": "I've saved this generated image for future reference."
    };
    
    cy.botUploadFile(botUploadPayload).then((response) => {
      cy.log(`Bot upload file response: ${JSON.stringify(response.body)}`);
      
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('uploadId');
      cy.log('Bot file uploaded successfully');
    });
  });
  
  it('7. should verify user data has been updated after image generation', () => {
    // Wait a moment for database to update
    cy.wait(1000);
    
    cy.getUserData({ telegramId }).then((response) => {
      cy.log(`Updated User Data: ${JSON.stringify(response.body)}`);
      
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('exists', true);
      
      // Verify wallet balance has changed if we had an initial balance
      if (initialWalletBalance !== undefined) {
        expect(response.body.wallet.balance).to.be.at.most(initialWalletBalance);
        cy.log(`Wallet balance changed from ${initialWalletBalance} to ${response.body.wallet.balance}`);
      }
      
      // Verify images count
      expect(response.body.stats).to.have.property('images_generated');
      cy.log(`User has generated ${response.body.stats.images_generated} images total`);
      
      // Verify conversation count
      expect(response.body.stats).to.have.property('conversation_count');
      cy.log(`User has ${response.body.stats.conversation_count} conversations total`);
    });
  });
}); 