"use client";

import React, { useState, useEffect, useRef } from "react";
import styles from "../admin.module.css";
import { supabase } from "@/lib/supabaseClient";

interface MessageData {
  id: string;
  conversation_id: string;
  sender: string;
  content: string | null;
  created_at: string;
  has_image: boolean;
  image_url?: string;
  provider: string;
  user_id: string; // telegram_id or whatsapp_id
  user_name: string;
}

export default function MessagesPage() {
  const [messages, setMessages] = useState<MessageData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isRealtime, setIsRealtime] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  
  // Refs for audio elements
  const incomingAudioRef = useRef<HTMLAudioElement | null>(null);
  const outgoingAudioRef = useRef<HTMLAudioElement | null>(null);

  // Initialize audio elements on client side
  useEffect(() => {
    // Create audio elements for notification sounds
    if (typeof window !== 'undefined') {
      incomingAudioRef.current = new Audio('/sounds/message-incoming.mp3');
      outgoingAudioRef.current = new Audio('/sounds/message-outgoing.mp3');
      
      // Set volume to a gentle level
      if (incomingAudioRef.current) incomingAudioRef.current.volume = 0.4;
      if (outgoingAudioRef.current) outgoingAudioRef.current.volume = 0.3;
    }
  }, []);

  // Play notification sound based on message sender
  const playNotificationSound = (sender: string) => {
    if (!soundEnabled) return;
    
    try {
      if (sender === 'user' && incomingAudioRef.current) {
        incomingAudioRef.current.currentTime = 0; // Reset to start
        incomingAudioRef.current.play();
      } else if (sender === 'bot' && outgoingAudioRef.current) {
        outgoingAudioRef.current.currentTime = 0; // Reset to start
        outgoingAudioRef.current.play();
      }
    } catch (err) {
      console.error('Error playing notification sound:', err);
    }
  };

  // Close image modal when clicking outside of it
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const modal = document.getElementById('image-modal');
      if (modal && !modal.contains(e.target as Node) && selectedImage) {
        setSelectedImage(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [selectedImage]);

  // Escape key to close modal
  useEffect(() => {
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && selectedImage) {
        setSelectedImage(null);
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [selectedImage]);

  // Fetch data on component mount and set up real-time subscription
  useEffect(() => {
    fetchRecentMessages();

    // Set up real-time subscription if enabled
    let subscription: any;

    if (isRealtime) {
      subscription = supabase
        .channel('messages-changes')
        .on('postgres_changes', {
          event: 'INSERT',
          schema: 'public',
          table: 'messages'
        }, (payload) => {
          // When a new message is inserted, fetch its full data with relationships
          fetchMessageWithDetails(payload.new.id);
        })
        .subscribe();
    }

    // Cleanup subscription on component unmount
    return () => {
      if (subscription) {
        supabase.removeChannel(subscription);
      }
    };
  }, [isRealtime]);

  // Helper function to get image URL
  const getImageUrl = async (imageId: string | null, uploadId: string | null): Promise<string | null> => {
    if (imageId) {
      // Get URL from images table
      const { data, error } = await supabase
        .from('images')
        .select('url')
        .eq('id', imageId)
        .single();
      
      if (error || !data) return null;
      return data.url;
    } else if (uploadId) {
      // Get URL from user_uploads table
      const { data, error } = await supabase
        .from('user_uploads')
        .select('file_url')
        .eq('id', uploadId)
        .single();
      
      if (error || !data) return null;
      return data.file_url;
    }
    
    return null;
  };

  // Function to fetch a single message with all its details
  const fetchMessageWithDetails = async (messageId: string) => {
    try {
      const { data, error } = await supabase
        .from('messages')
        .select(`
          id,
          conversation_id,
          sender,
          content,
          created_at,
          image_id,
          upload_id,
          conversation:bot_conversations(
            provider,
            external_identity:external_identities(
              id,
              telegram_id,
              whatsapp_id,
              telegram_first_name,
              telegram_last_name,
              whatsapp_name
            )
          )
        `)
        .eq('id', messageId)
        .single();

      if (error) {
        console.error('Error fetching new message details:', error);
        return;
      }

      // Process the data to get it in the format we need
      const conversation = data.conversation as any;
      const identity = conversation?.external_identity as any;
      const platform = conversation?.provider || 'unknown';
      const userId = platform === 'telegram' 
        ? identity?.telegram_id 
        : identity?.whatsapp_id;
      const userName = platform === 'telegram'
        ? `${identity?.telegram_first_name || ''} ${identity?.telegram_last_name || ''}`.trim()
        : identity?.whatsapp_name || '';
      
      // Get image URL if message has an image
      const hasImage = !!data.image_id || !!data.upload_id;
      let imageUrl = null;
      
      if (hasImage) {
        imageUrl = await getImageUrl(data.image_id, data.upload_id);
      }
        
      const newMessage: MessageData = {
        id: data.id,
        conversation_id: data.conversation_id,
        sender: data.sender,
        content: data.content,
        created_at: data.created_at,
        has_image: hasImage,
        image_url: imageUrl || undefined,
        provider: platform,
        user_id: userId || 'Unknown',
        user_name: userName || 'Unknown User'
      };

      // Play notification sound
      playNotificationSound(newMessage.sender);

      // Add the new message to the top of the list and remove the last one if we have more than 20
      setMessages(prevMessages => {
        const updatedMessages = [newMessage, ...prevMessages];
        if (updatedMessages.length > 20) {
          return updatedMessages.slice(0, 20);
        }
        return updatedMessages;
      });
    } catch (err) {
      console.error('Error processing new message:', err);
    }
  };

  // Function to fetch recent messages
  const fetchRecentMessages = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Fetch the latest 20 messages with related conversation and user data
      const { data, error } = await supabase
        .from('messages')
        .select(`
          id,
          conversation_id,
          sender,
          content,
          created_at,
          image_id,
          upload_id,
          conversation:bot_conversations(
            provider,
            external_identity:external_identities(
              id,
              telegram_id,
              whatsapp_id,
              telegram_first_name,
              telegram_last_name,
              whatsapp_name
            )
          )
        `)
        .order('created_at', { ascending: false })
        .limit(20);
      
      if (error) {
        throw error;
      }
      
      // Process messages and get image URLs
      const processedMessages = await Promise.all(data.map(async (msg: any) => {
        const identity = msg.conversation?.external_identity;
        const platform = msg.conversation?.provider || 'unknown';
        const userId = platform === 'telegram' 
          ? identity?.telegram_id 
          : identity?.whatsapp_id;
        const userName = platform === 'telegram'
          ? `${identity?.telegram_first_name || ''} ${identity?.telegram_last_name || ''}`.trim()
          : identity?.whatsapp_name || '';
        
        const hasImage = !!msg.image_id || !!msg.upload_id;
        let imageUrl = null;
        
        if (hasImage) {
          imageUrl = await getImageUrl(msg.image_id, msg.upload_id);
        }
          
        return {
          id: msg.id,
          conversation_id: msg.conversation_id,
          sender: msg.sender,
          content: msg.content,
          created_at: msg.created_at,
          has_image: hasImage,
          image_url: imageUrl || undefined,
          provider: platform,
          user_id: userId || 'Unknown',
          user_name: userName || 'Unknown User'
        };
      }));
      
      setMessages(processedMessages);
    } catch (err: any) {
      setError(`Error fetching messages: ${err.message}`);
      console.error('Error fetching messages:', err);
    } finally {
      setLoading(false);
    }
  };

  // Toggle real-time updates
  const toggleRealtime = () => {
    setIsRealtime(!isRealtime);
  };
  
  // Toggle sound notifications
  const toggleSound = () => {
    setSoundEnabled(!soundEnabled);
  };

  return (
    <div className={styles.messagesSection}>
      <div className={styles.sectionHeader}>
        <h2>Recent Messages</h2>
        <div className={styles.controlsGroup}>
          <div className={styles.togglesContainer}>
            <div className={styles.realtimeControl}>
              <label className={styles.realtimeToggle}>
                <input 
                  type="checkbox" 
                  checked={isRealtime} 
                  onChange={toggleRealtime} 
                />
                <span className={styles.toggleSwitch}></span>
                Real-time updates <span className={styles.toggleStatus}>{isRealtime ? 'ON' : 'OFF'}</span>
              </label>
            </div>
            <div className={styles.soundControl}>
              <label className={styles.soundToggle}>
                <input 
                  type="checkbox" 
                  checked={soundEnabled} 
                  onChange={toggleSound} 
                />
                <span className={styles.toggleSwitch}></span>
                Sound notifications <span className={styles.toggleStatus}>{soundEnabled ? 'ON' : 'OFF'}</span>
              </label>
            </div>
          </div>
          <button 
            className={styles.refreshButton}
            onClick={fetchRecentMessages}
            disabled={loading || isRealtime}
          >
            Refresh
          </button>
        </div>
      </div>
      
      {loading && <p className={styles.loadingText}>Loading messages...</p>}
      {error && <p className={styles.errorText}>{error}</p>}
      
      {!loading && !error && (
        <>
          <p className={styles.statsText}>
            Showing latest {messages.length} messages
            {isRealtime && <span className={styles.realtimeIndicator}>• Live</span>}
          </p>
          
          {messages.length === 0 ? (
            <p>No messages found.</p>
          ) : (
            <div className={styles.messagesTable}>
              <table className={styles.table}>
                <thead>
                  <tr>
                    <th>Time</th>
                    <th>User</th>
                    <th>Platform</th>
                    <th>Direction</th>
                    <th>Content</th>
                    <th>Images</th>
                  </tr>
                </thead>
                <tbody>
                  {messages.map((message) => (
                    <tr key={message.id} className={message.sender === 'bot' ? styles.botMessage : styles.userMessage}>
                      <td className={styles.messageTime}>
                        {new Date(message.created_at).toLocaleString()}
                      </td>
                      <td className={styles.messageUser}>
                        {message.user_name}
                        <div className={styles.messageUserId}>{message.user_id}</div>
                      </td>
                      <td className={styles.messagePlatform}>
                        <span className={`${styles.platformBadge} ${styles[message.provider]}`}>
                          {message.provider === 'telegram' && '📱 '}
                          {message.provider === 'whatsapp' && '💬 '}
                          {message.provider}
                        </span>
                      </td>
                      <td className={styles.messageDirection}>
                        {message.sender === 'user' ? 'Incoming' : 'Outgoing'}
                      </td>
                      <td className={styles.messageContent}>
                        {message.content || <em>No text content</em>}
                      </td>
                      <td className={styles.messageHasImage}>
                        {message.has_image ? (
                          <button 
                            className={styles.viewImageButton}
                            onClick={() => setSelectedImage(message.image_url || null)}
                          >
                            View Image
                          </button>
                        ) : (
                          <span className={styles.noImageBadge}>No Image</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </>
      )}

      {/* Image Modal */}
      {selectedImage && (
        <div className={styles.imageModalOverlay}>
          <div className={styles.imageModal} id="image-modal">
            <button 
              className={styles.closeModalButton}
              onClick={() => setSelectedImage(null)}
            >
              ×
            </button>
            <div className={styles.imageContainer}>
              <img 
                src={selectedImage} 
                alt="Message attachment" 
                className={styles.modalImage}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 