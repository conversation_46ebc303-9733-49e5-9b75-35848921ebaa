.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
}

.nav {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.navLink {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.2s;
}

.navLink:hover {
  color: #0070f3;
}

.navButton {
  padding: 0.5rem 1rem;
  background-color: transparent;
  color: #333;
  border: 1px solid #333;
  border-radius: 0.25rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.navButton:hover {
  background-color: #333;
  color: white;
}

.errorText {
  color: red;
  font-weight: 500;
}

/* Hamburger Menu */
.hamburger {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 200;
}

.hamburgerLine {
  display: block;
  width: 100%;
  height: 3px;
  background-color: #333;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.hamburger.active .hamburgerLine:nth-child(1) {
  transform: translateY(9px) rotate(45deg);
}

.hamburger.active .hamburgerLine:nth-child(2) {
  opacity: 0;
}

.hamburger.active .hamburgerLine:nth-child(3) {
  transform: translateY(-9px) rotate(-45deg);
}

.main {
  flex: 1;
  padding: 2rem;
}

/* Hero Section */
.hero {
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url('/aivis-hero-image-small.jpg');
  background-size: cover;
  background-position: center;
  border-radius: 1rem;
  margin-bottom: 4rem;
  position: relative;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: white;
}

.hero p {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1.5rem;
}

.launchNotice {
  font-weight: 500;
  color: #66b3ff;
  margin-bottom: 1.5rem;
}

.heroSubscribeForm {
  max-width: 500px;
  margin: 0 auto;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.primaryButton {
  padding: 0.75rem 1.5rem;
  background-color: #0070f3;
  color: white;
  border-radius: 0.5rem;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.primaryButton:hover {
  background-color: #0051a8;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.secondaryButton {
  padding: 0.75rem 1.5rem;
  background-color: transparent;
  color: white;
  border: 2px solid white;
  border-radius: 0.5rem;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
}

.secondaryButton:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

/* Services Section */
.services {
  padding: 2rem 0;
}

.services h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 2rem;
}

.serviceSection {
  display: flex;
  align-items: center;
  gap: 3rem;
  margin-bottom: 3rem;
  padding: 0 2rem;
}

.serviceSectionReversed {
  flex-direction: row-reverse;
}

.serviceImage {
  flex: 1;
  position: relative;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
}

.serviceImg {
  width: 100%;
  height: auto;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.serviceImage:hover .serviceImg {
  transform: scale(1.05);
}

.serviceContent {
  flex: 1;
  padding: 2rem;
}

.serviceContent h3 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  color: #1a1a1a;
}

.serviceContent p {
  font-size: 1.1rem;
  color: #4a4a4a;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.serviceContent ul {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
}

.serviceContent li {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
  position: relative;
  font-size: 1.1rem;
  color: #4a4a4a;
}

.serviceContent li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #0070f3;
  font-weight: bold;
}

.serviceLink {
  display: inline-block;
  color: #0070f3;
  text-decoration: none;
  font-weight: 500;
  font-size: 1.1rem;
  padding: 0.5rem 0;
  border-bottom: 2px solid #0070f3;
  transition: all 0.2s;
}

.serviceLink:hover {
  color: #0051a8;
  border-bottom-color: #0051a8;
}

.comingSoonBadge {
  display: inline-block;
  background-color: #f2994a;
  color: white;
  font-size: 0.8rem;
  padding: 0.3rem 0.6rem;
  border-radius: 1rem;
  margin-left: 0.8rem;
  font-weight: 500;
  vertical-align: middle;
  text-transform: uppercase;
}

/* How It Works Section */
.howItWorks {
  padding: 4rem 0;
  background-color: #f5f7fa;
}

.howItWorks h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
}

.steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  padding: 0 2rem;
}

.step {
  text-align: center;
  padding: 2rem;
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.step:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.stepIcon {
  margin: 0 auto 1.5rem;
  display: block;
}

.stepNumber {
  width: 30px;
  height: 30px;
  background-color: #0070f3;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 0.875rem;
}

.step h3 {
  margin-bottom: 1rem;
  color: #1a1a1a;
  font-size: 1.3rem;
}

.step p {
  color: #4a4a4a;
  line-height: 1.5;
}

/* Features Section */
.features {
  padding: 4rem 0;
}

.features h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
}

.featureGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  padding: 0 2rem;
}

.feature {
  text-align: center;
  padding: 2rem;
}

.featureIcon {
  margin: 0 auto 1.5rem;
  display: block;
  transition: transform 0.3s ease;
}

.feature:hover .featureIcon {
  transform: translateY(-5px);
}

.feature h3 {
  margin-bottom: 1rem;
  color: #1a1a1a;
}

.feature p {
  color: #4a4a4a;
}

/* CTA Section */
.cta {
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #c3cfe2 0%, #f5f7fa 100%);
  border-radius: 1rem;
  margin: 4rem 0;
}

.cta h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #1a1a1a;
}

.cta p {
  font-size: 1.25rem;
  color: #4a4a4a;
  margin-bottom: 2rem;
}

/* Subscription Form Styles */
.subscribeForm {
  max-width: 600px;
  margin: 0 auto;
}

.inputGroup {
  display: flex;
  margin-bottom: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
  overflow: hidden;
}

.emailInput {
  flex: 1;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  border: none;
  outline: none;
}

.subscribeButton {
  padding: 1rem 1.5rem;
  background-color: #0070f3;
  color: white;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.subscribeButton:hover {
  background-color: #0051a8;
}

.formDisclaimer {
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 1rem;
}

.formDisclaimer a {
  color: #0070f3;
  text-decoration: none;
}

.formDisclaimer a:hover {
  text-decoration: underline;
}

.successMessage {
  color: #4caf50;
  font-weight: 500;
  margin: 1rem 0;
  padding: 0.5rem;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: 0.5rem;
}

.errorMessage {
  color: #f44336;
  font-weight: 500;
  margin: 1rem 0;
  padding: 0.5rem;
  background-color: rgba(244, 67, 54, 0.1);
  border-radius: 0.5rem;
}

/* Footer */
.footer {
  background-color: #1a1a1a;
  color: white;
  padding: 4rem 2rem;
}

.footerContent {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 2fr 3fr;
  gap: 4rem;
}

.footerLogo p {
  margin-top: 1rem;
  color: #a0a0a0;
}

.footerLinks {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.footerLinkColumn h4 {
  margin-bottom: 1rem;
  color: white;
}

.footerLinkColumn a {
  display: block;
  color: #a0a0a0;
  text-decoration: none;
  margin-bottom: 0.5rem;
}

.footerLinkColumn a:hover {
  color: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .serviceSection {
    flex-direction: column;
    gap: 2rem;
  }

  .serviceSectionReversed {
    flex-direction: column;
  }

  .serviceImage {
    width: 100%;
  }

  .serviceContent {
    text-align: center;
  }

  .serviceContent li {
    text-align: left;
  }
}

@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }
  
  .nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: white;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    z-index: 100;
    opacity: 0;
    pointer-events: none;
    transform: translateY(-100%);
    transition: all 0.3s ease;
  }
  
  .nav.menuOpen {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
  }
  
  .navLink {
    font-size: 1.25rem;
  }
  
  .navButton {
    font-size: 1.25rem;
    padding: 0.75rem 1.5rem;
  }
  
  .hero h1 {
    font-size: 2rem;
  }
  
  .footerContent {
    grid-template-columns: 1fr;
  }
  
  .footerLinks {
    grid-template-columns: 1fr;
  }
  
  .inputGroup {
    flex-direction: column;
    gap: 1rem;
  }
  
  .emailInput {
    width: 100%;
    border-radius: 0.5rem;
  }
  
  .subscribeButton {
    width: 100%;
  }
}

@media (max-width: 480px) {
  /* Smaller text overall */


  .container {
    font-size: 0.9rem;
  }

  /* Header adjustments */
  .header {
    padding: 0.75rem 1rem;
  }

  /* Hero section */
  .hero {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
  }

  .hero h1 {
    font-size: 1.75rem;
    margin-bottom: 0.75rem;
  }

  .hero p {
    font-size: 1rem;
    margin-bottom: 1rem;
  }

  /* Buttons */
  .ctaButtons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .primaryButton, .secondaryButton {
    padding: 0.6rem 1.25rem;
    font-size: 0.95rem;
  }

  /* Service sections - reduce whitespace */
  .services {
    padding: 1rem 0;
  }
  
  .services h2 {
    font-size: 1.75rem;
    margin-bottom: 1.5rem;
  }

  .serviceSection {
    margin-bottom: 2rem;
    padding: 0 1rem;
    gap: 1.5rem;
  }

  .serviceContent {
    padding: 1rem 0.5rem;
  }

  .serviceContent h3 {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .serviceContent p {
    font-size: 0.95rem;
    margin-bottom: 1rem;
  }

  .serviceContent ul {
    margin-bottom: 1rem;
  }

  .serviceContent li {
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
  }

  /* How it works section */
  .howItWorks {
    padding: 2rem 0;
  }

  .howItWorks h2 {
    font-size: 1.75rem;
    margin-bottom: 1.5rem;
  }

  .step {
    padding: 1.5rem 1rem;
  }

  .step h3 {
    font-size: 1.1rem;
  }

  /* Features section */
  .features {
    padding: 2rem 0;
  }

  .features h2 {
    font-size: 1.75rem;
    margin-bottom: 1.5rem;
  }

  .feature {
    padding: 1rem;
  }

  /* Layout grids */
  .serviceGrid,
  .steps,
  .featureGrid {
    grid-template-columns: 1fr;
  }
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
}

.modal {
  background-color: white;
  border-radius: 1rem;
  padding: 2rem;
  max-width: 500px;
  width: 100%;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal h2 {
  margin-bottom: 1rem;
  font-size: 1.75rem;
  color: #1a1a1a;
}

.modal p {
  margin-bottom: 1.5rem;
  color: #4a4a4a;
}

.closeButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  cursor: pointer;
  color: #6c757d;
  padding: 0.25rem 0.5rem;
}

.closeButton:hover {
  color: #1a1a1a;
}

.ctaStatusMessage {
  text-align: center;
  margin-top: 1rem;
}

/* Mortal Kombat gallery responsive */
@media (max-width: 600px) {
  .serviceImage {
    max-width: 100vw;
    min-width: 0;
    padding: 0;
  }
  .serviceImage > div {
    min-width: 0 !important;
    width: 100% !important;
    padding: 0 !important;
  }
  .serviceImage img {
    width: 45vw !important;
    height: 60vw !important;
    max-width: 180px;
    max-height: 240px;
  }
}

.mkGalleryRow {
  position: relative;
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: center;
}

.mkGalleryImage {
  width: 200px;
  height: 300px;
  object-fit: cover;
  border-radius: 16px;
  border: 1px solid #eee;
  transition: opacity 0.15s;
}

.mkGalleryArrow {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  font-size: 48px;
  color: #0070f3;
  user-select: none;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 600px) {
  .mkGalleryRow {
    gap: 4vw;
  }
  .mkGalleryImage {
    width: 45vw !important;
    height: 60vw !important;
    max-width: 180px;
    max-height: 240px;
  }
  .mkGalleryArrow {
    font-size: 32px;
  }
}

.mkGalleryImageFade {
  transition: opacity 0.15s;
}

.mkGalleryImageFade.pointer {
  cursor: pointer;
}

.mkGalleryImageFade.default {
  cursor: default;
}

.mkGalleryContainer {
  background: #fafbfc;
  border-radius: 12px;
  box-shadow: 0 2px 8px #0001;
  padding: 8px 8px 0 8px;
  min-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 500px;
}

.mkGalleryLabel {
  font-size: 16px;
  color: #888;
  margin-top: 8px;
  margin-bottom: 8px;
  text-align: center;
}

@media (max-width: 600px) {
  .mkGalleryContainer {
    min-width: 0;
    max-width: 100vw;
    padding: 4px 2px 0 2px;
  }
  .mkGalleryLabel {
    font-size: 14px;
    margin-top: 4px;
    margin-bottom: 4px;
  }
}