import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { fal } from '@fal-ai/client';

// Create a Supabase client specifically for server-side use with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

// Configure the FAL client with the API key
fal.config({
  credentials: process.env.FAL_API_KEY || ''
});

// Character transformation prompts - same as start route
const CHARACTER_PROMPTS = {
  scorpion: "Dress this person as <PERSON><PERSON><PERSON> from Mortal Kombat. Keep their face, body, and background unchanged but add: yellow ninja outfit with black trim, <PERSON><PERSON><PERSON>'s signature skull mask covering lower face, kunai weapon with chain rope. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle fire/ember particle effects around the person without changing the background. Photorealistic, professional costume photography.",
  'sub-zero': "Dress this person as Sub-<PERSON> from Mortal Kombat. Keep their face, body, and background unchanged but add: blue ninja outfit with ice-blue accents, <PERSON>-<PERSON>'s signature ice mask covering lower face, frost effects around hands only. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle ice particle effects around the person without changing the background. Photorealistic, professional costume photography.",
  raiden: "Dress this person as Raiden from Mortal Kombat. Keep their face, body, and background unchanged but add: white and blue traditional outfit with lightning patterns, Raiden's conical straw hat, subtle blue glow in eyes. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle electrical spark effects around the person without changing the background. Photorealistic, professional costume photography.",
  'liu-kang': "Dress this person as Liu Kang from Mortal Kombat. Keep their face, body, and background unchanged but add: red martial arts outfit with black trim and dragon motifs, red bandana/headband. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle fire particle effects around fists without changing the background. Photorealistic, professional costume photography.",
  kitana: "Dress this person as Kitana from Mortal Kombat. Keep their face, body, and background unchanged but add: royal blue and black outfit with elegant design, decorative mask covering lower face, steel fans as weapons. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle magical sparkle effects around the person without changing the background. Photorealistic, professional costume photography.",
  'johnny-cage': "Dress this person as Johnny Cage from Mortal Kombat. Keep their face, body, and background unchanged but add: designer sunglasses, black military pants, open vest showing chest, confident Hollywood action star look. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle green energy glow around hands without changing the background. Photorealistic, professional costume photography.",
  mileena: "Dress this person as Mileena from Mortal Kombat. Keep their face, body, and background unchanged but add: purple ninja outfit with revealing design, Mileena's signature sai weapons, pink/purple mask covering lower face. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle dark energy effects around the person without changing the background. Photorealistic, professional costume photography.",
  'kung-lao': "Dress this person as Kung Lao from Mortal Kombat. Keep their face, body, and background unchanged but add: traditional Shaolin monk robes in blue and white, Kung Lao's signature razor-rimmed hat. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle wind effects around the hat without changing the background. Photorealistic, professional costume photography.",
  sindel: "Dress this person as Sindel from Mortal Kombat. Keep their face, body, and background unchanged but add: regal purple and black outfit with silver accents, long flowing white hair, Sindel's signature spiked tiara, and mystical aura. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle purple energy waves around the person without changing the background. Photorealistic, professional costume photography.",
  fujin: "Dress this person as Fujin from Mortal Kombat. Keep their face, body, and background unchanged but add: wind god attire with white and blue robes, Fujin's signature long ponytail, silver armor, and a staff. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle wind swirl effects around the person without changing the background. Photorealistic, professional costume photography."
};

// Cost estimation - users always pay 10 tokens for image generation regardless of backend model
const COST_ESTIMATES = {
  image_transform: 0.10, // Users always pay 10 tokens ($0.10) for image generation
  video_generation: {
    '360p': 0.30,
    '540p': 0.30, 
    '720p': 0.40,
    '1080p': 0.80
  }
};

interface ConfirmResizeRequest {
  resizedImageUrl: string;
  character_type: string;
  video_settings: {
    duration: 5 | 8;
    resolution: '360p' | '540p' | '720p' | '1080p';
    style?: 'anime' | '3d_animation' | 'comic';
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: ConfirmResizeRequest = await request.json();
    const { resizedImageUrl, character_type, video_settings } = body;

    if (!resizedImageUrl || !character_type || !video_settings) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get user from auth header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required - no token found' },
        { status: 401 }
      );
    }

    // Get user info
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      console.error('Auth error:', authError);
      return NextResponse.json(
        { error: 'Invalid authentication', details: authError?.message },
        { status: 401 }
      );
    }

    // Get user's wallet
    const { data: wallet, error: walletError } = await supabaseAdmin
      .from('wallets')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (walletError || !wallet) {
      return NextResponse.json(
        { error: 'User wallet not found' },
        { status: 404 }
      );
    }

    // Get selected image generator
    const { data: configData, error: configError } = await supabaseAdmin
      .from('config_options')
      .select('value')
      .eq('key', 'mk_image_generator')
      .single();

    const selectedImageGenerator = (configData?.value?.selected) || 'gpt-image-1';
    console.log('Using image generator:', selectedImageGenerator);

    // Calculate estimated cost
    const estimatedCost = COST_ESTIMATES.image_transform + 
                         COST_ESTIMATES.video_generation[video_settings.resolution];

    // Check if user has sufficient balance (convert to tokens, assume 1 token = $0.01)
    const requiredTokens = Math.ceil(estimatedCost * 100);
    
    if (wallet.balance < requiredTokens) {
      return NextResponse.json(
        { error: 'Insufficient token balance', required: requiredTokens, available: wallet.balance },
        { status: 402 }
      );
    }

    // Create generation request record using the resized image
    const { data: requestData, error: requestError } = await supabaseAdmin
      .from('web_generation_requests')
      .insert({
        user_id: user.id,
        wallet_id: wallet.id,
        flow_type: 'mortal_kombat',
        status: 'pending',
        current_step: 1,
        total_steps: 3,
        estimated_cost: estimatedCost,
        input_data: {
          character_type: character_type,
          video_settings: video_settings,
          uploaded_image_url: resizedImageUrl, // Use the resized image
          transformation_prompt: CHARACTER_PROMPTS[character_type as keyof typeof CHARACTER_PROMPTS],
          image_generator: selectedImageGenerator,
          was_resized: true // Flag to indicate this was a resized image
        }
      })
      .select()
      .single();

    if (requestError) {
      console.error('Request creation error:', requestError);
      return NextResponse.json(
        { error: 'Failed to create generation request' },
        { status: 500 }
      );
    }

    console.log('Proceeding with transformation using resized image:', resizedImageUrl);

    // Now proceed with the normal transformation flow...
    // [Rest of the transformation logic would go here, similar to the start route]
    
    return NextResponse.json({
      success: true,
      request_id: requestData.id,
      message: 'Proceeding with resized image...'
    });

  } catch (error: any) {
    console.error('Confirm resize error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to confirm resize', 
        details: error.message || error.toString() 
      },
      { status: 500 }
    );
  }
} 