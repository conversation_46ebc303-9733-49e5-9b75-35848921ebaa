import '@testing-library/jest-dom'
import { configure } from '@testing-library/dom';
import { logger } from './src/utils/logger';

// Mock Next.js Web APIs for API route testing
global.TextEncoder = require('util').TextEncoder;
global.TextDecoder = require('util').TextDecoder;

// Mock Request and Response for API route testing
global.Request = class Request {
  constructor(url, options = {}) {
    this.url = url;
    this.method = options.method || 'GET';
    // Create headers map with case-insensitive get method
    const headerEntries = Object.entries(options.headers || {});
    this.headers = new Map(headerEntries);
    // Add case-insensitive get method
    const originalGet = this.headers.get.bind(this.headers);
    this.headers.get = (key) => {
      // Try exact match first
      let value = originalGet(key);
      if (value !== undefined) return value;
      
      // Try case-insensitive match
      for (const [headerKey, headerValue] of this.headers.entries()) {
        if (headerKey.toLowerCase() === key.toLowerCase()) {
          return headerValue;
        }
      }
      return undefined;
    };
    this.body = options.body;
  }
  
  async formData() {
    return this.body;
  }
  
  async json() {
    return JSON.parse(this.body);
  }
};

global.Response = class Response {
  constructor(body, options = {}) {
    this.body = body;
    this.status = options.status || 200;
    this.headers = new Map(Object.entries(options.headers || {}));
  }
  
  async json() {
    return JSON.parse(this.body);
  }
  
  static json(data, options = {}) {
    return new Response(JSON.stringify(data), {
      status: options.status || 200,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    });
  }
};

// Mock NextResponse specifically
jest.mock('next/server', () => ({
  NextResponse: {
    json: (data, options = {}) => {
      const response = new global.Response(JSON.stringify(data), {
        status: options.status || 200,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      });
      return response;
    }
  },
  NextRequest: global.Request
}));

global.Headers = class Headers extends Map {};

// FormData polyfill for file upload testing
global.FormData = class FormData {
  constructor(form) {
    this.data = new Map();
    
    // If a form element is passed, extract its input values
    if (form) {
      const inputs = form.querySelectorAll('input, select, textarea');
      inputs.forEach(input => {
        if (input.name && input.value !== undefined) {
          this.data.set(input.name, input.value);
        }
      });
    }
  }
  
  append(key, value, filename) {
    this.data.set(key, value);
  }
  
  get(key) {
    return this.data.get(key);
  }
  
  has(key) {
    return this.data.has(key);
  }
  
  set(key, value) {
    this.data.set(key, value);
  }
  
  delete(key) {
    return this.data.delete(key);
  }
  
  entries() {
    return this.data.entries();
  }
  
  keys() {
    return this.data.keys();
  }
  
  values() {
    return this.data.values();
  }
};

// Fixed File polyfill that doesn't try to set read-only properties
global.File = class File {
  constructor(parts, name, options = {}) {
    this.parts = parts;
    this.name = name;
    // Define type as a non-writable property
    Object.defineProperty(this, 'type', {
      value: options.type || '',
      writable: false,
      enumerable: true
    });
    this.size = parts.reduce((acc, part) => acc + (part.length || 0), 0);
  }
  
  async arrayBuffer() {
    const text = this.parts.join('');
    return new TextEncoder().encode(text).buffer;
  }
};

global.Blob = class Blob {
  constructor(parts = [], options = {}) {
    this.size = parts.reduce((acc, part) => acc + (part.length || 0), 0);
    Object.defineProperty(this, 'type', {
      value: options.type || '',
      writable: false,
      enumerable: true
    });
    this.parts = parts;
  }
  
  async arrayBuffer() {
    const text = this.parts.join('');
    return new TextEncoder().encode(text).buffer;
  }
};

// Mock @supabase/supabase-js for all createClient usage - moved to top for proper hoisting
jest.mock('@supabase/supabase-js', () => {
  const mockMaybeSingle = jest.fn(() => Promise.resolve({ data: { balance: 100 }, error: null }));
  const mockSingle = jest.fn(() => Promise.resolve({ data: { value: { selected: 'gpt-image-1' } }, error: null }));
  const mockEq = jest.fn(() => ({ single: mockSingle, maybeSingle: mockMaybeSingle }));
  const mockSelect = jest.fn(() => ({ eq: mockEq }));
  const mockInsert = jest.fn(() => ({ select: mockSelect }));
  const mockFrom = jest.fn(() => ({ select: mockSelect, insert: mockInsert }));
  const supabaseMock = {
    from: mockFrom,
    auth: {
      getSession: jest.fn(() => Promise.resolve({ 
        data: { 
          session: { 
            user: { id: 'test-user-id', email: '<EMAIL>' }, 
            access_token: 'mock-token'
          } 
        }, 
        error: null 
      })),
      getUser: jest.fn(() => Promise.resolve({ 
        data: { 
          user: { id: 'test-user-id', email: '<EMAIL>' } 
        }, 
        error: null 
      })),
    },
  };
  const createClientMock = jest.fn(() => supabaseMock);
  
  return {
    createClient: createClientMock,
  };
});



// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  }),
  usePathname: () => '/admin/image-editor',
  useSearchParams: () => new URLSearchParams(),
}))

// Mock Next.js image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} />
  },
}))

// Mock Next.js link component
jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ children, href, ...props }) => {
    return <a href={href} {...props}>{children}</a>
  },
}))

// Mock Redux hooks (if needed in components)
jest.mock('react-redux', () => ({
  useDispatch: () => jest.fn(),
  useSelector: (selector) => selector({
    user: {
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
        role: 'admin',
        tokens: 100,
      }
    }
  }),
}))

// Mock fetch globally
global.fetch = jest.fn()

// Mock FileReader
global.FileReader = class {
  readAsDataURL = jest.fn(() => {
    this.onload && this.onload({
      target: {
        result: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABA...'
      }
    })
  })
}

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mocked-object-url')
global.URL.revokeObjectURL = jest.fn()

// Skip location mock as it's causing issues

// Suppress console errors in tests unless explicitly needed
const originalError = console.error
beforeAll(() => {
  console.error = (...args) => {
    if (process.env.NODE_ENV === 'test') {
      logger.error(...args);
      return;
    }
    originalError.call(console, ...args)
  }
})

afterAll(() => {
  console.error = originalError
}) 

// Mock Supabase client for src/lib/supabaseClient
jest.mock('./src/lib/supabaseClient', () => {
  const mockMaybeSingle = jest.fn(() => Promise.resolve({ data: { balance: 100 }, error: null }));
  const mockSingle = jest.fn(() => Promise.resolve({ data: { value: { selected: 'gpt-image-1' } }, error: null }));
  const mockEq = jest.fn(() => ({ single: mockSingle, maybeSingle: mockMaybeSingle }));
  const mockSelect = jest.fn(() => ({ eq: mockEq }));
  const mockFrom = jest.fn(() => ({ select: mockSelect }));
  const supabaseMock = {
    from: mockFrom,
    auth: {
      getSession: jest.fn(() => Promise.resolve({ 
        data: { 
          session: { 
            user: { id: 'test-user-id', email: '<EMAIL>' }, 
            access_token: 'mock-token'
          } 
        }, 
        error: null 
      })),
    },
  };
  const createClientMock = jest.fn(() => supabaseMock);
  
  // Support both CommonJS and ESModule imports
  return {
    __esModule: true,
    default: supabaseMock, // For default import
    supabase: supabaseMock, // For named import
    createClient: createClientMock, // For createClient import
  };
}); 

configure({
  getElementError: (message, container) => {
    let html = '';
    if (container && container.innerHTML) {
      html = container.innerHTML.split('\n').slice(0, 10).join('\n');
    }
    return new Error([message, html].join('\n\n'));
  }
}); 