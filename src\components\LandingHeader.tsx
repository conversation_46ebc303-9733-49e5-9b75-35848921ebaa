"use client"

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import styles from './LandingHeader.module.css';

const LandingHeader: React.FC = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  
  // Prevent body scrolling when menu is open
  useEffect(() => {
    if (menuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [menuOpen]);
  
  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  return (
    <header className={styles.header}>
      <div className={styles.container}>
        <div className={styles.logo}>
          <Link href="/">
            <Image 
              src="/main-logo.png"
              alt="PodVideos Logo" 
              width={100} 
              height={0}
              style={{ height: 'auto' }}
              priority
            />
          </Link>
        </div>
        
        <button 
          className={`${styles.hamburger} ${menuOpen ? styles.active : ''}`} 
          onClick={toggleMenu}
          aria-label="Toggle navigation menu"
        >
          <span className={styles.hamburgerLine}></span>
          <span className={styles.hamburgerLine}></span>
          <span className={styles.hamburgerLine}></span>
        </button>
        
        <nav className={`${styles.navigation} ${menuOpen ? styles.menuOpen : ''}`}>
          <ul className={styles.navList}>
            <li className={styles.navItem}>
              <Link href="/" className={styles.navLink} onClick={() => setMenuOpen(false)}>Home</Link>
            </li>
            <li className={styles.navItem}>
              <Link href="/features" className={styles.navLink} onClick={() => setMenuOpen(false)}>Features</Link>
            </li>
            <li className={styles.navItem}>
              <Link href="/how-it-works" className={styles.navLink} onClick={() => setMenuOpen(false)}>How It Works</Link>
            </li>
            <li className={styles.navItem}>
              <Link href="/pricing" className={styles.navLink} onClick={() => setMenuOpen(false)}>Pricing</Link>
            </li>
          </ul>
        </nav>
        
        <div className={`${styles.authButtons} ${menuOpen ? styles.menuOpen : ''}`}>
          <Link href="/login" className={styles.loginButton} onClick={() => setMenuOpen(false)}>
            Log In
          </Link>
          <Link href="/register" className={styles.signupButton} onClick={() => setMenuOpen(false)}>
            Sign Up
          </Link>
        </div>
      </div>
    </header>
  );
};

export default LandingHeader; 