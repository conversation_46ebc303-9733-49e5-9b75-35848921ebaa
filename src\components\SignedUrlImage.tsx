import React, { useEffect, useState } from 'react';
import { logger } from '../utils/logger';

interface SignedUrlImageProps {
  filePath: string;
  alt: string;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

const SignedUrlImage: React.FC<SignedUrlImageProps> = ({ filePath, alt, className, style, onClick }) => {
  const [signedUrl, setSignedUrl] = useState<string | null>(null);

  useEffect(() => {
    async function fetchSignedUrl() {
      try {
        const res = await fetch("/api/fetch-signed-url", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ filePath }),
        });
        const data = await res.json();
        if (data.signedUrl) {
          setSignedUrl(data.signedUrl);
        } else {
          logger.error("Error fetching signed URL:", data.error);
        }
      } catch (error) {
        logger.error("Error fetching signed URL", error);
      }
    }
    fetchSignedUrl();
  }, [filePath]);

  if (!signedUrl) {
    return <div>Loading image...</div>;
  }
  return <img src={signedUrl} alt={alt} className={className} style={style} onClick={onClick} />;
};

export default SignedUrlImage; 