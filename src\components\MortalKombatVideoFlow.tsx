'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Card, Button, Form, Al<PERSON>, <PERSON>, <PERSON>, Modal } from 'react-bootstrap';
import { useRouter, useSearchParams } from 'next/navigation';
import { supabase } from '../lib/supabaseClient';
import Header from '@/components/Header';
import VideoPlayer from '@/components/VideoPlayer';
import { useTranslation } from '@/hooks/useTranslation';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../store';
import { fetchUserProfile } from '../store/userSlice';
import { logger } from '../utils/logger';

// Types
interface VideoSettings {
  duration: 5 | 8;
  resolution: '360p' | '540p' | '720p' | '1080p';
  style?: 'anime' | '3d_animation' | 'comic';
}

interface GenerationRequest {
  request_id: string;
  status: string;
  current_step: number;
  total_steps: number;
  progress_percentage: number;
  transformation?: {
    original_image_url: string;
    transformed_image_url: string;
    character_type: string;
    prompt_used: string;
  };
  video?: {
    url: string;
    duration: number;
    resolution: string;
  };
  all_videos?: Array<{
    id: string;
    video_url: string;
    duration: number;
    resolution: string;
    prompt_text: string;
    token_cost: number;
    fal_request_id: string;
    created_at: string;
    is_regeneration: boolean;
  }>;
  final_output?: {
    original_image_url: string;
    transformed_image_url: string;
    video_url: string;
  };
  next_action: string;
  estimated_cost?: number;
  actual_cost?: number;
  error_message?: string;
}

const characterOptions = [
  { value: 'scorpion', label: 'Scorpion', description: 'The vengeful specter with fire powers' },
  { value: 'sub-zero', label: 'Sub-Zero', description: 'The ice-wielding Lin Kuei warrior' },
  { value: 'raiden', label: 'Raiden', description: 'The thunder god protector' },
  { value: 'liu-kang', label: 'Liu Kang', description: 'The dragon fire monk champion' },
  { value: 'kitana', label: 'Kitana', description: 'The fan-wielding Edenian princess' },
  { value: 'johnny-cage', label: 'Johnny Cage', description: 'The Hollywood action star fighter' },
  { value: 'mileena', label: 'Mileena', description: 'The savage Tarkatan-Edenian hybrid' },
  { value: 'kung-lao', label: 'Kung Lao', description: 'The razor-hat wielding monk' },
  { value: 'sindel', label: 'Sindel', description: 'The regal queen with mystical powers' },
  { value: 'fujin', label: 'Fujin', description: 'The wind god with divine power' }
];

const resolutionOptions = [
  { value: '360p', label: '360p', cost: 20 },
  { value: '540p', label: '540p', cost: 25 },
  { value: '720p', label: '720p (Recommended)', cost: 30 },
  { value: '1080p', label: '1080p (Premium)', cost: 50 }
];

interface MortalKombatVideoFlowProps {
    isFreeGeneration: boolean;
    freeGenerationId?: string;
    webGenerationRequestId?: string | null;
}

export function MortalKombatVideoFlow({
    isFreeGeneration,
    freeGenerationId,
    webGenerationRequestId,
  }: MortalKombatVideoFlowProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useDispatch<AppDispatch>();
  const { user, loading: userLoading } = useSelector((state: RootState) => state.user);
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generationRequest, setGenerationRequest] = useState<GenerationRequest | null>(null);
  const [lang, setLang] = useState<'en' | 'ro'>('ro');
  const { t } = useTranslation(lang);
  const [imageGenerator, setImageGenerator] = useState<string | null>(null);
  const [hasAttemptedFetch, setHasAttemptedFetch] = useState(false);
  const [generationLoading, setGenerationLoading] = useState(false);

  // Form state
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [selectedImagePreview, setSelectedImagePreview] = useState<string | null>(null);
  const [characterType, setCharacterType] = useState<string>('scorpion');
  const [videoSettings, setVideoSettings] = useState<VideoSettings>({
    duration: 5,
    resolution: isFreeGeneration ? '540p' : '720p',
    style: 'anime'
  });
  const [showCustomPrompt, setShowCustomPrompt] = useState(false);
  const [customVideoPrompt, setCustomVideoPrompt] = useState<string>('');
  
  // Resize confirmation modal state
  const [showResizeModal, setShowResizeModal] = useState(false);
  const [resizeData, setResizeData] = useState<{
    originalImage: { url: string; width: number; height: number; size: number };
    resizedPreview: { url: string; width: number; height: number; size: number };
    settings: any;
    freeGenerationId?: string;
  } | null>(null);
  const [resizeProcessing, setResizeProcessing] = useState(false);
  const [normalProcessing, setNormalProcessing] = useState(false);

  // Fetch user profile from Redux if not loaded
  useEffect(() => {
    if (!isFreeGeneration && !user && !userLoading && !hasAttemptedFetch) {
      dispatch(fetchUserProfile());
      setHasAttemptedFetch(true);
    }
  }, [isFreeGeneration, user, userLoading, dispatch, hasAttemptedFetch]);

  // Set initial defaults and auto-adjust settings for free generations
  useEffect(() => {
    setVideoSettings(prev => {
      let newSettings = { ...prev };
      
      if (isFreeGeneration) {
        // Force 5 seconds for free generations
        if (prev.duration === 8) {
          newSettings.duration = 5;
        }
        
        // Force 540p for free generations (avoid 720p/1080p)
        if (prev.resolution === '720p' || prev.resolution === '1080p') {
          newSettings.resolution = '540p'; // Default to 540p for free generations
        }
        
        // Set initial default to 540p for free generations if not already set appropriately
        if (prev.resolution === '720p') {
          newSettings.resolution = '540p';
        }
      } else {
        // For paid generations, ensure we start with 720p as default
        // Only change if it was previously set to a free-tier resolution
        if (prev.resolution === '540p' || prev.resolution === '360p') {
          newSettings.resolution = '720p'; // Default to 720p for paid generations
        }
      }
      
      return newSettings;
    });
  }, [isFreeGeneration]);

  // Ensure hasAttemptedFetch is set if user is present (prevents infinite spinner)
  useEffect(() => {
    if (!isFreeGeneration && user && !hasAttemptedFetch) {
      setHasAttemptedFetch(true);
    }
  }, [isFreeGeneration, user, hasAttemptedFetch]);

  // Redirect to login if not authenticated (only after loading finishes and fetch was attempted)
  useEffect(() => {
    if (!isFreeGeneration && hasAttemptedFetch && !user && !userLoading) {
      router.push('/login');
    }
  }, [isFreeGeneration, user, userLoading, router, hasAttemptedFetch]);

  // Load existing request if ID in URL
  useEffect(() => {
    const loadRequest = async (requestId: string) => {
      try {
        const headers: HeadersInit = {};
        if (!isFreeGeneration) {
          const { data: { session } } = await supabase.auth.getSession();
          if (!session) {
            setError('Please log in to continue');
            return;
          }
          headers['Authorization'] = `Bearer ${session.access_token}`;
        }

        const apiEndpoint = isFreeGeneration 
          ? `/api/free-generations/${requestId}`
          : `/api/web-generations/${requestId}`;
        
        const response = await fetch(apiEndpoint, { headers });
        
        if (response.ok) {
          const data = await response.json();
          setGenerationRequest(data);
          setCurrentStep(data.current_step || 1);
        }
      } catch (error) {
        logger.error('Error loading existing request:', error);
      }
    };

    if (isFreeGeneration) {
        if (webGenerationRequestId) {
            loadRequest(webGenerationRequestId);
        }
    } else {
        if (!user) return;
        const requestId = searchParams?.get('id');
        if (requestId) {
          loadRequest(requestId);
        }
    }
  }, [user, searchParams, isFreeGeneration, webGenerationRequestId]);

  useEffect(() => {
    // Fetch the currently selected image generator from Supabase
    const fetchImageGenerator = async () => {
      const { data, error } = await supabase
        .from('config_options')
        .select('value')
        .eq('key', 'mk_image_generator')
        .single();
      if (!error && data && data.value && data.value.selected) {
        setImageGenerator(data.value.selected);
      } else {
        setImageGenerator(null);
      }
    };
    fetchImageGenerator();
  }, []);

  // Add paste event listener
  useEffect(() => {
    const handlePaste = (event: ClipboardEvent) => {
      // Only handle paste when we're on step 1 (upload step)
      if (currentStep !== 1) return;
      
      const items = event.clipboardData?.items;
      if (!items) return;

      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.type.indexOf('image') !== -1) {
          const file = item.getAsFile();
          if (file) {
            handleImageFile(file);
          }
          break;
        }
      }
    };

    document.addEventListener('paste', handlePaste);
    return () => {
      document.removeEventListener('paste', handlePaste);
    };
  }, [currentStep]);

  // Polling for status updates
  useEffect(() => {
    if (!generationRequest?.request_id) return;

    const pollStatus = async () => {
      try {
        const headers: HeadersInit = {};
        if (!isFreeGeneration) {
            const { data: { session } } = await supabase.auth.getSession();
            if (!session) return;
            headers['Authorization'] = `Bearer ${session.access_token}`;
        }

              const apiEndpoint = isFreeGeneration 
        ? `/api/free-generations/${generationRequest.request_id}`
        : `/api/web-generations/${generationRequest.request_id}`;
      
      const response = await fetch(apiEndpoint, {
        headers,
      });

        if (response.ok) {
          const data = await response.json();
          
          // Debug logging for free generations
          if (isFreeGeneration) {
            logger.log('Free generation poll response:', {
              status: data.status,
              current_step: data.current_step,
              hasTransformation: !!data.transformation,
              transformation: data.transformation,
              hasOutputData: !!data.output_data,
              outputData: data.output_data,
              inputData: data.input_data,
              timestamp: new Date().toISOString()
            });
          }
          
          // Store previous state to detect changes
          const hadTransformation = !!generationRequest.transformation;
          const hasTransformation = !!data.transformation;
          
          // For free generations, if transformation object is missing but we have the raw data, construct it
          if (isFreeGeneration && !data.transformation && data.output_data?.transformed_image_url && data.status === 'awaiting_approval') {
            const originalImageUrl = data.input_data?.original_image_url || 
                                   data.input_data?.uploaded_image_url ||
                                   data.input_data?.image_url;
            const characterType = data.input_data?.settings?.character_type || 
                                data.input_data?.character_type ||
                                data.output_data?.character;
            
            data.transformation = {
              original_image_url: originalImageUrl,
              transformed_image_url: data.output_data.transformed_image_url,
              character_type: characterType,
              prompt_used: data.output_data.character || characterType || ''
            };
            
            logger.log('Frontend: Constructed transformation object from raw data:', data.transformation);
          }
          
          setGenerationRequest(data);
          
          // Use current_step from database response instead of hardcoding based on status
          if (data.current_step) {
            setCurrentStep(data.current_step);
          } else {
            // Fallback logic for backwards compatibility
            if (data.status === 'completed') {
              setCurrentStep(4);
            } else if (data.status === 'awaiting_approval') {
              setCurrentStep(2);
            } else {
              setCurrentStep(1);
            }
          }
          
          // Debug logging for step changes
          if (isFreeGeneration) {
            logger.log('Step update:', {
              oldStep: currentStep,
              newStep: data.current_step || (data.status === 'awaiting_approval' ? 2 : 1),
              status: data.status,
              hasTransformation: hasTransformation,
              transformationJustLoaded: !hadTransformation && hasTransformation
            });
          }
          
          if (data.status === 'completed' || data.status === 'failed') {
            // Stop polling
            return;
          }
        }
      } catch (error) {
        logger.error('Polling error:', error);
      }
    };

    // Poll every 3 seconds for transformation processing and video generation
    if (generationRequest.status === 'in_progress' || 
        generationRequest.status === 'processing' ||
        (generationRequest.status === 'pending' && generationRequest.current_step === 2)) {
      const interval = setInterval(pollStatus, 3000);
      return () => clearInterval(interval);
    }
    
    // For free generations, be more aggressive with polling
    if (isFreeGeneration) {
      // Poll if we're in any processing state OR if we're awaiting approval but don't have transformation data yet
      const shouldPoll = (
        generationRequest.status === 'in_progress' ||
        generationRequest.status === 'pending' ||
        generationRequest.status === 'processing' ||
        (generationRequest.status === 'awaiting_approval' && !generationRequest.transformation)
      );
      
      if (shouldPoll) {
        logger.log('Free generation: Starting polling for status:', generationRequest.status);
        const interval = setInterval(pollStatus, 2000);
        return () => clearInterval(interval);
      }
    }
  }, [generationRequest, isFreeGeneration, currentStep]);

  const handleImageFile = (file: File) => {
    if (!file.type.startsWith('image/')) {
      setError('Please select a valid image file');
      return;
    }

    setSelectedImage(file);
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setSelectedImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
    setError(null); // Clear any previous errors
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleImageFile(file);
    }
  };

  const calculateEstimatedCost = () => {
    if (isFreeGeneration) return 0;
    const imageCost = 10; // 10 tokens for image generation
    const videoCost = resolutionOptions.find(r => r.value === videoSettings.resolution)?.cost || 30;
    return imageCost + videoCost;
  };

  const loadExistingRequest = async (requestId: string) => {
    setGenerationLoading(true);
    try {
        const headers: HeadersInit = {};
        if (!isFreeGeneration) {
            const { data: { session } } = await supabase.auth.getSession();
            if (!session) return;
            headers['Authorization'] = `Bearer ${session.access_token}`;
        }

      const apiEndpoint = isFreeGeneration 
        ? `/api/free-generations/${requestId}`
        : `/api/web-generations/${requestId}`;
      
      const response = await fetch(apiEndpoint, {
        headers,
      });

      if (response.ok) {
        const data = await response.json();
        setGenerationRequest(data);
        
        // Use current_step from database response instead of hardcoding based on status
        if (data.current_step) {
          setCurrentStep(data.current_step);
        } else {
          // Fallback logic for backwards compatibility
          if (data.status === 'completed') {
            setCurrentStep(4);
          } else if (data.status === 'awaiting_approval') {
            setCurrentStep(2);
          } else {
            setCurrentStep(1);
          }
        }

        // Set video settings from the request data
        if (data.input_settings?.video_settings) {
          setVideoSettings(data.input_settings.video_settings);
        }
        if (data.input_settings?.character_type) {
          setCharacterType(data.input_settings.character_type);
        }
      }
    } catch (error) {
      logger.error('Failed to load existing request:', error);
    } finally {
      setGenerationLoading(false);
    }
  };

  const startGeneration = async () => {
    if (!selectedImage) return;
    if (!isFreeGeneration && !user) return;

    setIsLoading(true);
    setNormalProcessing(true);
    setError(null);

    // Add page unload warning while generation is in progress
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      e.preventDefault();
      e.returnValue = ''; // Required for Chrome
      return ''; // Required for other browsers
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);

    try {
        const headers: HeadersInit = {};
        let url = '';
        const formData = new FormData();

        if (isFreeGeneration) {
            url = '/api/free-generations/mortal-kombat/start';
            formData.append('freeGenerationId', freeGenerationId!);
        } else {
            const { data: { session } } = await supabase.auth.getSession();
            if (!session) {
              setError('Authentication required');
              return;
            }
            headers['Authorization'] = `Bearer ${session.access_token}`;
            url = '/api/web-generations/mortal-kombat/start';
        }

      formData.append('image', selectedImage);
      formData.append('settings', JSON.stringify({
        character_type: characterType,
        video_settings: videoSettings
      }));

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: formData
      });

      const data = await response.json();

      if (response.ok) {
        // Check if image requires resize confirmation
        if (data.requiresResizeConfirmation) {
          setResizeData(data);
          setShowResizeModal(true);
          setNormalProcessing(false); // Close normal processing modal when showing resize modal
          return;
        }

        // For free generations, ensure we have the correct request_id field
        if (isFreeGeneration) {
          data.request_id = data.id || data.request_id;
        }
        
        setGenerationRequest(data);
        if (!isFreeGeneration) {
            dispatch(fetchUserProfile());
        }
        setNormalProcessing(false); // Close processing modal on success
        
        // Debug: Log the response to understand the flow
        logger.log('Start generation response:', data);
        
        // Set the current step based on the response status
        if (data.status === 'awaiting_approval') {
          setCurrentStep(2); // Show approval step
          logger.log('Setting step to 2 (approval)');
        } else if (data.status === 'in_progress') {
          setCurrentStep(2); // Show processing step
          logger.log('Setting step to 2 (processing)');
        } else if (data.status === 'pending') {
          setCurrentStep(2); // Show processing step
          logger.log('Setting step to 2 (pending)');
        } else {
          setCurrentStep(2); // Default to step 2
          logger.log('Setting step to 2 (default)');
        }
        
        // For free generations, immediately poll for updated data after start
        const requestId = isFreeGeneration ? data.id : data.request_id;
        if (isFreeGeneration && requestId) {
          logger.log('Free generation: Scheduling immediate poll for updated data, request ID:', requestId);
          setTimeout(async () => {
            try {
              const pollResponse = await fetch(`/api/free-generations/${requestId}`);
              if (pollResponse.ok) {
                const pollData = await pollResponse.json();
                logger.log('Immediate poll response:', pollData);
                // Ensure we use the correct ID field for consistency
                pollData.request_id = pollData.id || pollData.request_id;
                setGenerationRequest(pollData);
              } else {
                logger.error('Immediate poll failed:', pollResponse.status, pollResponse.statusText);
              }
            } catch (error) {
              logger.error('Immediate poll error:', error);
            }
          }, 1000); // Poll after 1 second
        } else if (isFreeGeneration) {
          logger.error('Free generation: No ID in start response!', data);
        }
        
        // Update URL with request ID
        if (!isFreeGeneration) {
            router.push(`/mortal-kombat-video?id=${data.request_id}`);
        }
      } else {
        logger.error('Start generation error:', data);
        setError(`${data.error || 'Failed to start generation'}${data.details ? ` - ${data.details}` : ''}`);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to start generation');
    } finally {
      // Remove the beforeunload warning when generation completes (success or error)
      window.removeEventListener('beforeunload', handleBeforeUnload);
      setIsLoading(false);
      setNormalProcessing(false);
    }
  };

  const approveTransformation = async (approved: boolean, reason?: string) => {
    if (!generationRequest?.request_id) return;

    setIsLoading(true);
    setError(null);

    try {
        const headers: HeadersInit = {
            'Content-Type': 'application/json'
        };
        if (!isFreeGeneration) {
            const { data: { session } } = await supabase.auth.getSession();
            if (!session) {
              setError('Authentication required');
              return;
            }
            headers['Authorization'] = `Bearer ${session.access_token}`;
        }

      const apiEndpoint = isFreeGeneration 
        ? `/api/free-generations/${generationRequest.request_id}/approve-transformation`
        : `/api/web-generations/${generationRequest.request_id}/approve-transformation`;
      
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify({ approved, reason })
      });

      const data = await response.json();

      if (response.ok) {
        setGenerationRequest(data);
        if (approved) {
          setCurrentStep(3); // Move to video generation step
          // Automatically start video generation after approval
          await generateVideo();
        } else {
          setCurrentStep(1); // Go back to start for rejection
        }
      } else {
        setError(data.error || 'Failed to process approval');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to process approval');
    } finally {
      setIsLoading(false);
    }
  };

  const regenerateTransformation = async () => {
    if (!generationRequest?.request_id) return;

    setIsLoading(true);
    setError(null);

    try {
        const headers: HeadersInit = {
            'Content-Type': 'application/json'
        };
        if (!isFreeGeneration) {
            const { data: { session } } = await supabase.auth.getSession();
            if (!session) {
              setError('Authentication required');
              return;
            }
            headers['Authorization'] = `Bearer ${session.access_token}`;
        }

      // Call the start endpoint again with regenerate=true to keep the same image
      const response = await fetch(`/api/web-generations/${generationRequest.request_id}/regenerate-transformation`, {
        method: 'POST',
        headers,
        body: JSON.stringify({})
      });

      const data = await response.json();

      if (response.ok) {
        setGenerationRequest(data);
        // Stay on step 2, but status will determine if we show processing or approval
        setCurrentStep(2);
      } else {
        setError(data.error || 'Failed to regenerate transformation');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to regenerate transformation');
    } finally {
      setIsLoading(false);
    }
  };

  const generateVideo = async () => {
    if (!generationRequest?.request_id) return;

    setIsLoading(true);
    setError(null);

    try {
        const headers: HeadersInit = {
            'Content-Type': 'application/json'
        };
        if (!isFreeGeneration) {
            const { data: { session } } = await supabase.auth.getSession();
            if (!session) {
              setError('Authentication required');
              return;
            }
            headers['Authorization'] = `Bearer ${session.access_token}`;
        }

      const apiEndpoint = isFreeGeneration 
        ? `/api/free-generations/${generationRequest.request_id}/generate-video`
        : `/api/web-generations/${generationRequest.request_id}/generate-video`;
      
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify({ 
          custom_video_prompt: customVideoPrompt || undefined 
        })
      });

      const data = await response.json();

      if (response.ok) {
        setGenerationRequest(data);
        setCurrentStep(4); // Updated: now step 4 instead of 5
        if (!isFreeGeneration) {
            dispatch(fetchUserProfile());
        }
      } else {
        setError(data.error || 'Failed to generate video');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to generate video');
    } finally {
      setIsLoading(false);
    }
  };

  const regenerateVideo = async () => {
    if (!generationRequest?.request_id) return;

    setIsLoading(true);
    setError(null);

    try {
        const headers: HeadersInit = {
            'Content-Type': 'application/json'
        };
        if (!isFreeGeneration) {
            const { data: { session } } = await supabase.auth.getSession();
            if (!session) {
              setError('Authentication required');
              return;
            }
            headers['Authorization'] = `Bearer ${session.access_token}`;
        }

      const response = await fetch(`/api/web-generations/${generationRequest.request_id}/regenerate-video`, {
        method: 'POST',
        headers,
        body: JSON.stringify({})
      });

      const data = await response.json();

      if (response.ok) {
        setGenerationRequest(data);
        await loadExistingRequest(generationRequest.request_id);
        if (!isFreeGeneration) {
            dispatch(fetchUserProfile());
        }
      } else {
        setError(data.error || 'Failed to regenerate video');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to regenerate video');
    } finally {
      setIsLoading(false);
    }
  };

  const cancelVideoGeneration = async () => {
    if (!generationRequest?.request_id) return;

    setIsLoading(true);
    setError(null);

    try {
        const headers: HeadersInit = {
            'Content-Type': 'application/json'
        };
        if (!isFreeGeneration) {
            const { data: { session } } = await supabase.auth.getSession();
            if (!session) {
              setError('Authentication required');
              return;
            }
            headers['Authorization'] = `Bearer ${session.access_token}`;
        }

      const response = await fetch(`/api/web-generations/${generationRequest.request_id}/cancel-video`, {
        method: 'POST',
        headers,
        body: JSON.stringify({})
      });

      const data = await response.json();

      if (response.ok) {
        setGenerationRequest(data);
        setCurrentStep(2); // Go back to approval step
      } else {
        setError(data.error || 'Failed to cancel video generation');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to cancel video generation');
    } finally {
      setIsLoading(false);
    }
  };

  const getDefaultVideoPrompt = (character: string) => {
    const prompts: { [key: string]: string } = {
      'scorpion': 'Seamless transformation from normal person to Scorpion from Mortal Kombat with mystical fire effects, ninja movements, and supernatural energy',
      'sub-zero': 'Seamless transformation from normal person to Sub-Zero from Mortal Kombat with ice and frost effects, martial arts stance, and freezing energy',
      'raiden': 'Seamless transformation from normal person to Raiden from Mortal Kombat with lightning and electrical effects, godly presence, and thunder energy',
      'liu-kang': 'Seamless transformation from normal person to Liu Kang from Mortal Kombat with dragon fire effects, martial arts mastery, and spiritual energy',
      'kitana': 'Seamless transformation from normal person to Kitana from Mortal Kombat with royal grace, fan weapons, and Edenian elegance',
      'johnny-cage': 'Seamless transformation from normal person to Johnny Cage from Mortal Kombat with Hollywood flair, sunglasses, and action hero charisma',
      'mileena': 'Seamless transformation from normal person to Mileena from Mortal Kombat with savage ferocity, sai weapons, and Tarkatan features',
      'kung-lao': 'Seamless transformation from normal person to Kung Lao from Mortal Kombat with monk discipline, razor hat weapon, and martial arts expertise'
    };
    return prompts[character] || `Seamless transformation from normal person to ${character} from Mortal Kombat with dramatic transformation effects, mystical energy, and fighting stance`;
  };

  const handleCustomPromptSubmit = () => {
    setShowCustomPrompt(false);
  };

  const resetToDefaultPrompt = () => {
    setCustomVideoPrompt(getDefaultVideoPrompt(characterType));
  };

  const handleResizeConfirmation = async (confirmed: boolean) => {
    if (!resizeData) return;

    if (!confirmed) {
      setShowResizeModal(false);
      setResizeData(null);
      return;
    }

    // User confirmed, proceed with resized image
    setResizeProcessing(true);
    setIsLoading(true);
    setError(null);

    // Add page unload warning while resize generation is in progress
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      e.preventDefault();
      e.returnValue = ''; // Required for Chrome
      return ''; // Required for other browsers
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);

    try {
      // Fetch the resized image from the preview URL and convert to File
      const response = await fetch(resizeData.resizedPreview.url);
      if (!response.ok) {
        throw new Error(`Failed to fetch resized image: ${response.status} ${response.statusText}`);
      }
      
      const blob = await response.blob();
      
      // Create a File object from the blob with proper metadata
      const originalFileName = resizeData.settings ? 'uploaded-image' : 'resized-image';
      const resizedFile = new File([blob], `${originalFileName}.jpg`, { 
        type: 'image/jpeg',
        lastModified: Date.now()
      });
      
      // Create FormData and send to normal start endpoint
      const formData = new FormData();
      formData.append('image', resizedFile);
      
      const settings = {
        character_type: characterType,
        video_settings: videoSettings
      };
      formData.append('settings', JSON.stringify(settings));

      if (isFreeGeneration && resizeData.freeGenerationId) {
        formData.append('freeGenerationId', resizeData.freeGenerationId);
      }

      const headers: HeadersInit = {};
      let url = '';

      if (isFreeGeneration) {
        url = '/api/free-generations/mortal-kombat/start';
      } else {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          setError('Authentication required');
          return;
        }
        headers['Authorization'] = `Bearer ${session.access_token}`;
        url = '/api/web-generations/mortal-kombat/start';
      }
      
      // Debug logging
      logger.log('Sending resized file to start endpoint:', {
        fileName: resizedFile.name,
        fileSize: resizedFile.size,
        fileType: resizedFile.type,
        originalDimensions: `${resizeData.originalImage.width}x${resizeData.originalImage.height}`,
        resizedDimensions: `${resizeData.resizedPreview.width}x${resizeData.resizedPreview.height}`,
        settings: settings,
        url: url
      });

      const apiResponse = await fetch(url, {
        method: 'POST',
        headers,
        body: formData
      });

      let data;
      try {
        data = await apiResponse.json();
      } catch (parseError) {
        logger.error('Failed to parse response as JSON:', parseError);
        setError(`Server error: ${apiResponse.status} ${apiResponse.statusText}`);
        setResizeProcessing(false);
        return;
      }

      if (apiResponse.ok) {
        // Handle the response like normal flow
        if (data.requiresResizeConfirmation) {
          // Shouldn't happen since we're using resized image, but handle just in case
          setResizeProcessing(false);
          setResizeData(data);
          setShowResizeModal(true);
        } else {
          // For free generations, ensure we have the correct request_id field
          if (isFreeGeneration) {
            data.request_id = data.id || data.request_id;
          }
          
          setGenerationRequest(data);
          setCurrentStep(2);
          if (!isFreeGeneration) {
            dispatch(fetchUserProfile());
          }
          
          // Close modal and reset states only after successful processing
          setShowResizeModal(false);
          setResizeData(null);
          setResizeProcessing(false);
          
          // For free generations, immediately poll for updated data after resize confirmation
          const requestId = isFreeGeneration ? data.id : data.request_id;
          if (isFreeGeneration && requestId) {
            logger.log('Free generation resize: Scheduling immediate poll for updated data, request ID:', requestId);
            setTimeout(async () => {
              try {
                const pollResponse = await fetch(`/api/free-generations/${requestId}`);
                if (pollResponse.ok) {
                  const pollData = await pollResponse.json();
                  logger.log('Immediate poll response after resize:', pollData);
                  // Ensure we use the correct ID field for consistency
                  pollData.request_id = pollData.id || pollData.request_id;
                  setGenerationRequest(pollData);
                } else {
                  logger.error('Immediate poll failed after resize:', pollResponse.status, pollResponse.statusText);
                }
              } catch (error) {
                logger.error('Immediate poll error after resize:', error);
              }
            }, 1000); // Poll after 1 second
          } else if (isFreeGeneration) {
            logger.error('Free generation resize: No ID in start response!', data);
          }
        }
      } else {
        logger.error('API Response Error:', {
          status: apiResponse.status,
          statusText: apiResponse.statusText,
          data: data,
          url: url,
          headers: Object.fromEntries(apiResponse.headers.entries())
        });
        setError(data.error || `Server error: ${apiResponse.status} ${apiResponse.statusText}`);
        setResizeProcessing(false);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to proceed with resized image');
      setResizeProcessing(false);
    } finally {
      // Remove the beforeunload warning when resize generation completes (success or error)
      window.removeEventListener('beforeunload', handleBeforeUnload);
      setIsLoading(false);
    }
  };

  const renderStepIndicator = () => {
    const steps = [t('step_upload'), t('step_approve'), t('step_video'), t('step_complete')];
    
    return (
      <div className="d-flex justify-content-between mb-4">
        {steps.map((step, index) => (
          <div key={index} className={`text-center ${index + 1 <= currentStep ? 'text-primary' : 'text-muted'}`}>
            <div className={`rounded-circle d-inline-flex align-items-center justify-content-center ${
              index + 1 <= currentStep ? 'bg-primary text-white' : 'bg-light'
            }`} style={{ width: '40px', height: '40px' }}>
              {index + 1}
            </div>
            <div className="small mt-1">{step}</div>
          </div>
        ))}
      </div>
    );
  };

  const renderStep1 = () => (
    <Card>
      <Card.Body>
        {!isFreeGeneration && <div className="alert alert-info text-center mb-4">
          {t('pricing_info')}
        </div>}
        <h3>{t('step1_title')}</h3>
        <p className="text-muted">{t('step1_subtitle')}</p>
        
        <Form.Group className="mb-4">
          <Form.Label style={{ fontSize: '1.1rem', fontWeight: '600', marginBottom: '12px' }} data-testid="mk-upload-step">
            {t('upload_image')}
          </Form.Label>
          <div 
            className="upload-drop-zone"
            style={{
              border: '3px dashed #dee2e6',
              borderRadius: '12px',
              padding: '48px 24px',
              textAlign: 'center',
              backgroundColor: '#fafbfc',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              minHeight: '200px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
              marginBottom: '12px'
            }}
            onClick={() => document.getElementById('image-upload-input')?.click()}
            onDrop={(e) => {
              e.preventDefault();
              const files = e.dataTransfer.files;
              if (files.length > 0) {
                handleImageFile(files[0]);
              }
            }}
            onDragOver={(e) => {
              e.preventDefault();
              e.currentTarget.style.borderColor = '#0d6efd';
              e.currentTarget.style.backgroundColor = '#f0f7ff';
            }}
            onDragLeave={(e) => {
              e.preventDefault();
              e.currentTarget.style.borderColor = '#dee2e6';
              e.currentTarget.style.backgroundColor = '#fafbfc';
            }}
          >
            <input
              id="image-upload-input"
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              style={{ display: 'none' }}
            />
            
            {!selectedImagePreview ? (
              <>
                <div style={{
                  width: '64px',
                  height: '64px',
                  backgroundColor: '#e9ecef',
                  borderRadius: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: '16px'
                }}>
                  <svg width="32" height="32" fill="#6c757d" viewBox="0 0 24 24">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                  </svg>
                </div>
                <h5 style={{ fontSize: '1.2rem', fontWeight: '600', color: '#495057', marginBottom: '8px' }}>
                  {t('click_to_upload')}
                </h5>
                <p style={{ fontSize: '1rem', color: '#6c757d', marginBottom: '8px' }}>
                  {t('or_drag_and_drop')}
                </p>
                <p style={{ fontSize: '0.9rem', color: '#6c757d' }}>
                  {t('paste_image_hint')}
                </p>
              </>
            ) : (
              <div style={{ width: '100%', maxWidth: '300px' }}>
                <img 
                  src={selectedImagePreview} 
                  alt="Preview" 
                  style={{ 
                    width: '100%', 
                    height: 'auto', 
                    borderRadius: '8px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                  }} 
                />
                <p style={{ fontSize: '0.9rem', color: '#6c757d', marginTop: '12px' }}>
                  {t('click_to_change_image')}
                </p>
              </div>
            )}
          </div>
        </Form.Group>

        <Form.Group className="mb-3">
          <Form.Label>{t('select_character')}</Form.Label>
          <div className="d-flex gap-2 flex-wrap">
            {characterOptions.map(char => (
              <div 
                key={char.value}
                className={`character-box ${characterType === char.value ? 'active' : ''}`}
                onClick={() => setCharacterType(char.value)}
                style={{
                  border: '2px solid #dee2e6',
                  borderColor: characterType === char.value ? '#0d6efd' : '#dee2e6',
                  backgroundColor: characterType === char.value ? '#f8f9fa' : 'white',
                  borderRadius: '8px',
                  padding: '12px 16px',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  textAlign: 'center',
                  minWidth: '120px',
                  fontWeight: characterType === char.value ? '600' : '400',
                  flex: '1 1 calc(25% - 6px)',
                  minHeight: '60px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <div style={{ fontSize: '0.95rem' }}>{char.label}</div>
              </div>
            ))}
          </div>
          {/* Character Description */}
          {characterType && (
            <div 
              className="character-description mt-3"
              style={{
                padding: '16px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px',
                border: '1px solid #e9ecef',
                fontSize: '0.9rem',
                color: '#495057',
                fontStyle: 'italic'
              }}
            >
              <strong>{characterOptions.find(char => char.value === characterType)?.label}:</strong>{' '}
              {characterOptions.find(char => char.value === characterType)?.description}
            </div>
          )}
        </Form.Group>

        <Form.Group className="mb-3">
          <Form.Label>{t('video_duration')}</Form.Label>
          <div className="d-flex gap-3">
            <div 
              className={`duration-box ${videoSettings.duration === 5 ? 'active' : ''}`}
              onClick={() => setVideoSettings(prev => ({ ...prev, duration: 5 }))}
              style={{
                border: '2px solid #dee2e6',
                borderColor: videoSettings.duration === 5 ? '#0d6efd' : '#dee2e6',
                backgroundColor: videoSettings.duration === 5 ? '#f8f9fa' : 'white',
                borderRadius: '8px',
                padding: '16px 24px',
                cursor: 'pointer',
                transition: 'all 0.2s',
                textAlign: 'center',
                minWidth: '120px',
                fontWeight: videoSettings.duration === 5 ? '600' : '400'
              }}
            >
              <div style={{ fontSize: '1.1rem', marginBottom: '4px' }}>5 {t('seconds')}</div>
              <div style={{ fontSize: '0.85rem', color: '#6c757d' }}>{t('standard')}</div>
            </div>
            <div 
              className={`duration-box ${videoSettings.duration === 8 ? 'active' : ''} ${isFreeGeneration ? 'disabled' : ''}`}
              onClick={() => !isFreeGeneration && setVideoSettings(prev => ({ ...prev, duration: 8 }))}
              style={{
                border: '2px solid #dee2e6',
                borderColor: videoSettings.duration === 8 && !isFreeGeneration ? '#0d6efd' : '#dee2e6',
                backgroundColor: videoSettings.duration === 8 && !isFreeGeneration ? '#f8f9fa' : 'white',
                borderRadius: '8px',
                padding: '16px 24px',
                cursor: isFreeGeneration ? 'not-allowed' : 'pointer',
                transition: 'all 0.2s',
                textAlign: 'center',
                minWidth: '120px',
                fontWeight: videoSettings.duration === 8 && !isFreeGeneration ? '600' : '400',
                opacity: isFreeGeneration ? 0.5 : 1,
                position: 'relative'
              }}
            >
              <div style={{ fontSize: '1.1rem', marginBottom: '4px' }}>8 {t('seconds')}</div>
              <div style={{ fontSize: '0.85rem', color: '#6c757d' }}>
                {isFreeGeneration ? t('premium_only') : t('extended')}
              </div>
              {isFreeGeneration && (
                <div style={{
                  position: 'absolute',
                  top: '4px',
                  right: '4px',
                  fontSize: '0.7rem',
                  background: '#ffc107',
                  color: '#000',
                  padding: '2px 4px',
                  borderRadius: '3px',
                  fontWeight: 'bold'
                }}>
                  PRO
                </div>
              )}
            </div>
          </div>
        </Form.Group>

        <Form.Group className="mb-3">
          <Form.Label>{t('video_resolution')}</Form.Label>
          <div className="d-flex gap-2 flex-wrap">
            {resolutionOptions.map(res => {
              const isPremiumRes = isFreeGeneration && (res.value === '720p' || res.value === '1080p');
              const isDisabled = isPremiumRes;
              const isSelected = videoSettings.resolution === res.value && !isDisabled;
              
              return (
                <div 
                  key={res.value}
                  className={`resolution-box ${isSelected ? 'active' : ''} ${isDisabled ? 'disabled' : ''}`}
                  onClick={() => !isDisabled && setVideoSettings(prev => ({ ...prev, resolution: res.value as any }))}
                  style={{
                    border: '2px solid #dee2e6',
                    borderColor: isSelected ? '#0d6efd' : '#dee2e6',
                    backgroundColor: isSelected ? '#f8f9fa' : 'white',
                    borderRadius: '8px',
                    padding: '12px 16px',
                    cursor: isDisabled ? 'not-allowed' : 'pointer',
                    transition: 'all 0.2s',
                    textAlign: 'center',
                    minWidth: '100px',
                    fontWeight: isSelected ? '600' : '400',
                    flex: '1',
                    opacity: isDisabled ? 0.5 : 1,
                    position: 'relative'
                  }}
                >
                  <div style={{ fontSize: '1rem', marginBottom: '2px' }}>{res.label}</div>
                  <div style={{ fontSize: '0.8rem', color: '#6c757d' }}>
                    {isFreeGeneration ? (isPremiumRes ? t('premium_only') : t('free')) : `${res.cost} ${t('tokens')}`}
                  </div>
                  {isPremiumRes && (
                    <div style={{
                      position: 'absolute',
                      top: '4px',
                      right: '4px',
                      fontSize: '0.7rem',
                      background: '#ffc107',
                      color: '#000',
                      padding: '2px 4px',
                      borderRadius: '3px',
                      fontWeight: 'bold'
                    }}>
                      PRO
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </Form.Group>

        <div className="d-flex justify-content-between align-items-center">
          <div>
            <strong>{t('estimated_cost')}: {calculateEstimatedCost()} {isFreeGeneration ? '' : t('tokens')}</strong>
          </div>
          <div className="d-flex gap-2">
            <Button 
              variant="primary" 
              onClick={startGeneration}
              disabled={!selectedImage || isLoading}
              data-testid="mk-start-generation"
            >
              {isLoading ? <Spinner animation="border" size="sm" /> : t('start_generation')}
            </Button>
          </div>
        </div>
      </Card.Body>
    </Card>
  );

  const renderStep2Processing = () => (
    <Card>
      <Card.Body>
        <h3>{t('step2_processing_title')}</h3>
        <p className="text-muted">{t('step2_processing_subtitle')}</p>
        <div className="text-center">
          <Spinner animation="border" className="mb-3" />
          <p>{t('step2_processing_wait')}</p>
        </div>
      </Card.Body>
    </Card>
  );

  const renderStep2Approve = () => (
    <Card>
      <Card.Body>
        <h3>{t('step2_approve_title')}</h3>
        <p className="text-muted">{t('step2_approve_subtitle')}</p>
        {generationRequest?.transformation ? (
          <>
            <Row className="mb-4">
              <Col md={6}>
                <h5>{t('original')}</h5>
                <img src={generationRequest.transformation.original_image_url} alt="Original" className="img-fluid rounded" />
              </Col>
              <Col md={6}>
                <h5>{t('transformed')} ({generationRequest.transformation.character_type})</h5>
                <img src={generationRequest.transformation.transformed_image_url} alt="Transformed" className="img-fluid rounded" />
              </Col>
            </Row>

            <div className="d-flex gap-2">
              <Button 
                variant="success" 
                onClick={() => approveTransformation(true)}
                disabled={isLoading}
              >
                {isLoading ? <Spinner animation="border" size="sm" /> : t('approve_and_generate')}
              </Button>
              <Button 
                variant="outline-secondary" 
                onClick={regenerateTransformation}
                disabled={isLoading}
              >
                {t('regenerate_character')}
              </Button>
            </div>
          </>
        ) : (
          // Show loading state while transformation data is loading
          <div className="text-center">
            <Spinner animation="border" className="mb-3" />
            <p>{t('step2_processing_wait')}</p>
          </div>
        )}
      </Card.Body>
    </Card>
  );

  const renderStep3 = () => (
    <Card>
      <Card.Body style={{ padding: '3rem 2rem', minHeight: '340px', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
        <h3>{t('step3_title')}</h3>
        <p className="text-muted">{t('step3_subtitle')}</p>
        <div className="text-center mt-5 mb-4">
          <Spinner animation="border" className="mb-3" style={{ width: 60, height: 60 }} />
          <p style={{ fontSize: '1.15rem', marginTop: 24 }}>{t('step3_generating')}</p>
          <Button 
            variant="outline-secondary" 
            className="mt-4"
            onClick={cancelVideoGeneration}
            disabled={isLoading}
          >
            {t('cancel_generation')}
          </Button>
        </div>
      </Card.Body>
    </Card>
  );

  const renderStep4 = () => (
    <Card>
      <Card.Body>
        <h3>{t('step4_title')}</h3>
        {generationRequest?.final_output && (
          <>
            <Row className="mb-4">
              <Col md={4}>
                <h5>{t('step4_original')}</h5>
                <img src={generationRequest.final_output.original_image_url} alt={t('step4_original')} className="img-fluid rounded" />
              </Col>
              <Col md={4}>
                <h5>{t('step4_transformed')}</h5>
                <img src={generationRequest.final_output.transformed_image_url} alt={t('step4_transformed')} className="img-fluid rounded" />
              </Col>
              <Col md={4}>
                <h5>{t('step4_final_video')}</h5>
                {generationRequest.all_videos && generationRequest.all_videos.length > 1 ? (
                  <div>
                    <p className="small text-muted mb-2">
                      {t('step4_videos_generated_latest', { count: generationRequest.all_videos.length })}
                    </p>
                    <VideoPlayer src={generationRequest.final_output.video_url} className="mb-2" />
                    <details>
                      <summary className="small text-muted" style={{ cursor: 'pointer' }}>
                        {t('step4_view_all_videos', { count: generationRequest.all_videos.length })}
                      </summary>
                      <div className="mt-2">
                        {generationRequest.all_videos.map((video, index) => (
                          <div key={video.id} className="mb-2">
                            <small className="text-muted">
                              {t('step4_video_number', {
                                number: index + 1,
                                type: video.is_regeneration ? t('step4_video_type_regenerated') : t('step4_video_type_original'),
                                tokens: video.token_cost
                              })}
                            </small>
                            <VideoPlayer src={video.video_url} className="" style={{ maxHeight: '200px' }} />
                          </div>
                        ))}
                      </div>
                    </details>
                  </div>
                ) : (
                  <VideoPlayer src={generationRequest.final_output.video_url} />
                )}
              </Col>
            </Row>

            <div className="d-flex gap-2 flex-wrap">
              <a 
                href={generationRequest.final_output.video_url} 
                target="_blank" 
                download
                className="btn btn-primary"
              >
                {t('step4_download_video')}
              </a>
              {!isFreeGeneration && <Button 
                variant="outline-warning" 
                onClick={regenerateVideo}
                disabled={isLoading}
              >
                {isLoading ? <Spinner animation="border" size="sm" /> : t('step4_regenerate_video', { tokens: resolutionOptions.find(r => r.value === videoSettings.resolution)?.cost })}
              </Button>}
              {isFreeGeneration ? (
                <div className="text-center w-100">
                  <div className="alert alert-success mt-3">
                    <h5 className="alert-heading">{t('step4_free_cta_title')}</h5>
                    <p className="mb-3">{t('step4_free_cta_subtitle')}</p>
                    <div className="d-flex gap-2 justify-content-center">
                      <Button 
                        variant="primary"
                        onClick={() => router.push('/login')}
                      >
                        {t('step4_free_login')}
                      </Button>
                      <Button 
                        variant="outline-primary"
                        onClick={() => router.push('/register')}
                      >
                        {t('step4_free_register')}
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <Button 
                  variant="outline-primary" 
                  onClick={() => {
                    setCurrentStep(1);
                    setGenerationRequest(null);
                    setSelectedImage(null);
                    setSelectedImagePreview(null);
                    router.push('/mortal-kombat-video');
                  }}
                >
                  {t('step4_create_another')}
                </Button>
              )}
            </div>

            {!isFreeGeneration && (
              <div className="mt-3 text-muted">
                <small>{t('step4_total_cost', { tokens: calculateEstimatedCost() })}</small>
              </div>
            )}
          </>
        )}
      </Card.Body>
    </Card>
  );

  // Show loading spinner in the main content area, but always render Header and Container
  const hasIdParam = !!searchParams?.get('id');
  const showContentLoader = !isFreeGeneration && (userLoading || !hasAttemptedFetch || (generationLoading && hasIdParam));

  return (
    <>
      <Header isFreeGeneration={isFreeGeneration} />
      <Container className="py-5">
        <Row className="justify-content-center">
          <Col lg={8}>
            {showContentLoader ? (
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '40vh' }}>
                <Spinner animation="border" variant="primary" role="status" />
              </div>
            ) : (
              <>
                <div className="text-center mb-5">
                  {/* Language Selector */}
                   {/* Show current image generator to admins only */}
                  {user?.role === 'admin' && imageGenerator && (
                      <div className="mb-3">
                        <select value={lang} onChange={e => setLang(e.target.value as 'en' | 'ro')} className="form-select w-auto d-inline-block">
                          <option value="en">English</option>
                          <option value="ro">Română</option>
                        </select>
                      </div>
                    )}
                  <h1 data-testid="mk-main-title">⚔️ {t('mortal_kombat_title')}</h1>
                  <p className="lead">
                    {isFreeGeneration 
                      ? t('free_description')
                      : t('description')
                    }
                  </p>
                  {generationRequest?.request_id && !isFreeGeneration && (
                    <div className="mt-3">
                      <Badge bg="secondary" className="px-3 py-2">
                        Generation ID: {generationRequest.request_id.slice(0, 8)}...
                      </Badge>
                      <Button 
                        variant="outline-secondary" 
                        size="sm" 
                        className="ms-2"
                        onClick={() => {
                          const url = `${window.location.origin}/mortal-kombat-video?id=${generationRequest.request_id}`;
                          navigator.clipboard.writeText(url);
                          // Could add a toast notification here
                        }}
                      >
                        📋 Copy Link
                      </Button>
                      <div className="small text-muted mt-2">
                        Bookmark this page to return to your generation
                      </div>
                    </div>
                  )}
                </div>

                {/* Show current image generator to admins only */}
                {user?.role === 'admin' && imageGenerator && (
                  <div style={{ margin: '24px 0', padding: '12px 16px', background: '#f6f8fa', borderRadius: 6, border: '1px solid #e1e4e8', fontSize: 16 }}>
                    <strong>Current Image Generator:</strong> <span style={{ fontFamily: 'monospace', color: '#333' }}>{imageGenerator}</span>
                  </div>
                )}

                {renderStepIndicator()}

                {error && (
                  <Alert variant="danger" className="mb-4">
                    {error}
                  </Alert>
                )}

                {currentStep === 1 && renderStep1()}
                {currentStep === 2 && (
                  // For free generations, use status to determine which screen to show
                  isFreeGeneration ? 
                    (generationRequest?.status === 'awaiting_approval' ? renderStep2Approve() : renderStep2Processing()) :
                    // For regular generations, use transformation data availability
                    (generationRequest?.transformation ? renderStep2Approve() : renderStep2Processing())
                )}
                {currentStep === 3 && renderStep3()}
                {currentStep === 4 && renderStep4()}
              </>
            )}
          </Col>
        </Row>
      </Container>

      {/* Custom Prompt Modal */}
      <Modal show={showCustomPrompt} onHide={() => setShowCustomPrompt(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{t('customize_prompt_title') || 'Customize Video Prompt'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group className="mb-3">
            <Form.Label>{t('video_generation_prompt_label') || 'Video Generation Prompt'}</Form.Label>
            <Form.Control
              as="textarea"
              rows={4}
              value={customVideoPrompt || getDefaultVideoPrompt(characterType)}
              onChange={(e) => setCustomVideoPrompt(e.target.value)}
              placeholder={getDefaultVideoPrompt(characterType)}
            />
            <Form.Text className="text-muted">
              {t('video_generation_prompt_help') || 'Describe how you want the transformation video to look. Be specific about effects, movements, and style.'}
            </Form.Text>
          </Form.Group>
          
          <div className="d-flex gap-2">
            <Button variant="outline-secondary" onClick={resetToDefaultPrompt}>
              {t('reset_to_default') || 'Reset to Default'}
            </Button>
            <Button variant="primary" onClick={handleCustomPromptSubmit}>
              {t('save_custom_prompt') || 'Save Custom Prompt'}
            </Button>
          </div>
          
          <div className="mt-3">
            <small className="text-muted">
              <strong>{t('character_label') || 'Character'}:</strong> {characterOptions.find(c => c.value === characterType)?.label}<br/>
              <strong>{t('default_prompt_label') || 'Default prompt'}:</strong> {getDefaultVideoPrompt(characterType)}
            </small>
          </div>
        </Modal.Body>
      </Modal>

      {/* Processing Modal for Normal Generation */}
      {normalProcessing && (
        <div className="modal fade show" style={{ display: 'block', backgroundColor: 'rgba(0,0,0,0.5)' }} onClick={(e) => e.stopPropagation()}>
          <div className="modal-dialog modal-dialog-centered" onClick={(e) => e.stopPropagation()}>
            <div className="modal-content">
              <div className="modal-body text-center" style={{ padding: '4rem 2rem' }}>
                <Spinner animation="border" className="mb-3" style={{ width: '3rem', height: '3rem' }} />
                <h5>{t('step2_processing_title')}</h5>
                <p className="text-muted">{t('step2_processing_wait')}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Image Resize Confirmation Modal */}
      {showResizeModal && resizeData && (
        <div className="modal fade show" style={{ display: 'block', backgroundColor: 'rgba(0,0,0,0.5)' }} onClick={resizeProcessing ? undefined : () => handleResizeConfirmation(false)}>
          <div className="modal-dialog modal-lg" onClick={(e) => e.stopPropagation()}>
            <div className="modal-content">
              {resizeProcessing ? (
                // Show only spinner during processing
                <div className="modal-body text-center" style={{ padding: '4rem 2rem' }}>
                  <Spinner animation="border" className="mb-3" style={{ width: '3rem', height: '3rem' }} />
                  <h5>{t('modal_image_resize_processing')}</h5>
                  <p className="text-muted">{t('step2_processing_wait')}</p>
                </div>
              ) : (
                <>
                  <div className="modal-header">
                    <h5 className="modal-title">{t('modal_image_resize_title')}</h5>
                    <button type="button" className="btn-close" onClick={() => handleResizeConfirmation(false)}></button>
                  </div>
                  <div className="modal-body">
                    <div className="alert alert-info">
                      <strong>{t('modal_image_resize_message')}</strong>
                      <br />
                      {t('modal_image_resize_original')}: {resizeData.originalImage.width}×{resizeData.originalImage.height}px ({(resizeData.originalImage.size / 1024 / 1024).toFixed(2)}MB)
                      <br />
                      {t('modal_image_resize_optimized')}: {resizeData.resizedPreview.width}×{resizeData.resizedPreview.height}px ({(resizeData.resizedPreview.size / 1024 / 1024).toFixed(2)}MB)
                    </div>
                    
                    <div className="row">
                      <div className="col-md-6">
                        <h6>{t('modal_image_resize_original')} Image</h6>
                        <img 
                          src={resizeData.originalImage.url} 
                          alt={t('modal_image_resize_original')} 
                          className="img-fluid border rounded"
                          style={{ maxHeight: '300px', width: '100%', objectFit: 'contain' }}
                        />
                        <small className="text-muted" data-testid="mk-resize-original-size">
                          {resizeData.originalImage.width}×{resizeData.originalImage.height}px
                        </small>
                      </div>
                      <div className="col-md-6">
                        <h6>{t('modal_image_resize_optimized')} Preview</h6>
                        <img 
                          src={resizeData.resizedPreview.url} 
                          alt={t('modal_image_resize_optimized')} 
                          className="img-fluid border rounded"
                          style={{ maxHeight: '300px', width: '100%', objectFit: 'contain' }}
                        />
                        <small className="text-muted" data-testid="mk-resize-optimized-size">
                          {resizeData.resizedPreview.width}×{resizeData.resizedPreview.height}px
                        </small>
                      </div>
                    </div>
                    
                    <div className="mt-3">
                      <p><strong>{t('modal_image_resize_benefits_title')}</strong></p>
                      <ul>
                        <li>✅ {t('modal_image_resize_benefit_1')}</li>
                        <li>✅ {t('modal_image_resize_benefit_2')}</li>
                        <li>✅ {t('modal_image_resize_benefit_3')}</li>
                        <li>✅ {t('modal_image_resize_benefit_4')}</li>
                      </ul>
                    </div>
                  </div>
                  <div className="modal-footer">
                    <button 
                      type="button" 
                      className="btn btn-secondary" 
                      onClick={() => handleResizeConfirmation(false)}
                      disabled={resizeProcessing}
                    >
                      {t('modal_image_resize_cancel')}
                    </button>
                    <button 
                      type="button" 
                      className="btn btn-primary" 
                      onClick={() => handleResizeConfirmation(true)}
                      disabled={resizeProcessing}
                    >
                      {resizeProcessing ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          {t('modal_image_resize_processing')}
                        </>
                      ) : (
                        t('modal_image_resize_continue')
                      )}
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      )}


    </>
  );
}
