describe('Complete Bot Flow with File Upload', () => {
  const telegramId = 'telegram-test-id-file-flow';
  const base64Image = 'iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAKElEQVQ4jWNgYGD4Twzu6FhFFGYYNXDUwGFpIAk2E4dHDRw1cDgaCAASFOffhEIO3gAAAABJRU5ErkJggg==';
  
  let conversationId;
  let walletId;
  let externalIdentityId;
  let uploadedFileUrl;
  let generatedImageUrl;
  
  it('1. should receive a message and start a conversation', () => {
    const messagePayload = {
      "messageId": "test-message-id-" + Date.now(),
      "firstName": "Test",
      "lastName": "User",
      "languageCode": "en",
      "chatId": "test-chat-id-" + Date.now(),
      "text": "Hello! I'd like to share an image with you.",
      "provider": "telegram",
      "providerUserId": telegramId,
      "providerBotId": "test-bot-id"
    };
    
    cy.botReceiveMessage(messagePayload).then((response) => {
      cy.log(`Receive message response: ${JSON.stringify(response.body)}`);
      
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('conversationId');
      
      // Store important IDs for the next steps
      conversationId = response.body.data.conversationId;
      externalIdentityId = response.body.data.externalIdentityId;
      walletId = response.body.data.walletId;
      
      cy.log(`Conversation ID: ${conversationId}`);
      cy.log(`External Identity ID: ${externalIdentityId}`);
      cy.log(`Wallet ID: ${walletId || 'Not available'}`);
    });
  });
  
  it('2. should upload a file from the user', () => {
    // Skip if required data is missing
    if (!conversationId || !externalIdentityId) {
      cy.log('Skipping test: missing conversation ID or external identity ID');
      return;
    }
    
    // Use the helper command for file upload
    cy.completeFileUploadFlow({
      conversationId,
      externalIdentityId,
      fileBase64: base64Image,
      fileName: 'user_photo.png',
      mimeType: 'image/png',
      textMessage: 'Here is my reference photo for the mountains I want.',
      sender: 'user'
    }).then((response) => {
      cy.log(`File upload response: ${JSON.stringify(response.body)}`);
      
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('uploadId');
      expect(response.body.data).to.have.property('fileUrl');
      
      uploadedFileUrl = response.body.data.fileUrl;
      cy.log(`Uploaded file URL: ${uploadedFileUrl}`);
    });
  });
  
  it('3. should send a message acknowledging the upload', () => {
    // Skip if required data is missing
    if (!conversationId) {
      cy.log('Skipping test: missing conversation ID');
      return;
    }
    
    const sendMessagePayload = {
      "conversationId": conversationId,
      "text": "Thanks for sharing your photo! I'll generate something similar to those mountains."
    };
    
    cy.botSendMessage(sendMessagePayload).then((response) => {
      cy.log(`Bot response message: ${JSON.stringify(response.body)}`);
      
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('success', true);
    });
  });
  
  it('4. should generate an image based on the uploaded file', () => {
    // Skip if required data is missing
    if (!conversationId || !walletId) {
      cy.log('Skipping test: missing conversation ID or wallet ID');
      return;
    }
    
    const generatePayload = {
      "conversationId": conversationId,
      "walletId": walletId,
      "tokenCost": 5,
      "operation": "generate",
      "promptText": "Mountains similar to the reference image, but with more snow and a sunset",
      "parameters": {
        "style": "realistic",
        "width": 1024,
        "height": 1024
      },
      "imageBase64": base64Image,
      "imageMimeType": "image/png"
    };
    
    cy.botGenerateImage(generatePayload).then((response) => {
      cy.log(`Generate image response: ${JSON.stringify(response.body)}`);
      
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('success', true);
      
      if (response.body.data) {
        expect(response.body.data).to.have.property('imageId');
        expect(response.body.data).to.have.property('resultUrl');
        
        generatedImageUrl = response.body.data.resultUrl;
        cy.log(`Generated image URL: ${generatedImageUrl}`);
      }
    });
  });
  
  it('5. should send the generated image back to the user', () => {
    // Skip if required data is missing
    if (!conversationId || !generatedImageUrl) {
      cy.log('Skipping test: missing conversation ID or image URL');
      return;
    }
    
    const sendMessagePayload = {
      "conversationId": conversationId,
      "text": "Here is your generated mountain image with added snow and sunset!",
      "imageUrl": generatedImageUrl
    };
    
    cy.botSendMessage(sendMessagePayload).then((response) => {
      cy.log(`Send message with image response: ${JSON.stringify(response.body)}`);
      
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('success', true);
    });
  });
  
  it('6. should upload the generated image as a bot file', () => {
    // Skip if required data is missing
    if (!conversationId || !externalIdentityId || !generatedImageUrl) {
      cy.log('Skipping test: missing required data');
      return;
    }
    
    // Use the helper command for file upload with bot as sender
    cy.completeFileUploadFlow({
      conversationId,
      externalIdentityId,
      fileUrl: generatedImageUrl,
      fileName: 'generated_mountains.png',
      mimeType: 'image/png',
      textMessage: "I've saved this image to your conversation history.",
      sender: 'bot'
    }).then((response) => {
      cy.log(`Bot file upload response: ${JSON.stringify(response.body)}`);
      
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('uploadId');
    });
  });
  
  it('7. should verify the entire conversation flow is recorded', () => {
    // Skip if required data is missing
    if (!conversationId) {
      cy.log('Skipping test: missing conversation ID');
      return;
    }
    
    // We would ideally check the conversation history here
    // This is a placeholder for that functionality
    cy.log('Full conversation flow completed successfully');
    cy.log(`Conversation ID: ${conversationId}`);
    cy.log(`User-uploaded file: ${uploadedFileUrl || 'Not available'}`);
    cy.log(`Bot-generated image: ${generatedImageUrl || 'Not available'}`);
  });
}); 