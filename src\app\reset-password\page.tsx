"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import styles from './reset-password.module.css';
import { supabase } from '../../lib/supabaseClient';

export default function ResetPassword() {
  const router = useRouter();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [message, setMessage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resetToken, setResetToken] = useState<string | null>(null);

  // Extract the reset token from the URL when the component mounts
  useEffect(() => {
    // The token is in the hash part of the URL (#access_token=...)
    const hash = window.location.hash;
    if (hash && hash.includes('access_token=')) {
      const token = hash.split('access_token=')[1].split('&')[0];
      setResetToken(token);
    } else {
      setMessage("Error: Invalid or missing reset token. Please request a new password reset link.");
    }
  }, []);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    
    // Validate passwords match
    if (password !== confirmPassword) {
      setMessage("Error: Passwords do not match");
      return;
    }

    // Validate password strength
    if (password.length < 8) {
      setMessage("Error: Password must be at least 8 characters long");
      return;
    }

    setIsSubmitting(true);
    setMessage(null);

    try {
      // Update the user's password using the reset token
      const { error } = await supabase.auth.updateUser({ 
        password: password 
      });

      if (error) {
        setMessage(`Error: ${error.message}`);
      } else {
        setMessage("Password reset successful! Redirecting to login...");
        // Redirect to login page after a short delay
        setTimeout(() => {
          router.push('/login');
        }, 2000);
      }
    } catch (error: any) {
      setMessage(`Error: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={styles.container}>
      <main className={styles.main}>
        <h1 className={styles.title}>Reset Your Password</h1>
        
        {!resetToken ? (
          <div className={styles.errorContainer}>
            <p className={styles.errorMessage}>{message}</p>
            <Link href="/forgotpassword" className={styles.link}>
              Request a new reset link
            </Link>
          </div>
        ) : (
          <>
            <p className={styles.description}>
              Enter your new password below.
            </p>
            <form className={styles.form} onSubmit={handleSubmit}>
              <div className={styles.inputGroup}>
                <label htmlFor="password">New Password</label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter new password"
                  required
                  minLength={8}
                />
              </div>
              <div className={styles.inputGroup}>
                <label htmlFor="confirmPassword">Confirm Password</label>
                <input
                  type="password"
                  id="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm new password"
                  required
                  minLength={8}
                />
              </div>
              <button 
                type="submit" 
                className={styles.submitButton}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Resetting...' : 'Reset Password'}
              </button>
            </form>
            {message && (
              <p className={message.includes('Error') ? styles.errorMessage : styles.successMessage}>
                {message}
              </p>
            )}
          </>
        )}
        
        <p className={styles.linkText}>
          <Link href="/login">
            Back to login
          </Link>
        </p>
      </main>
    </div>
  );
} 