import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client specifically for server-side use with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

interface ApprovalRequest {
  approved: boolean;
  rejection_reason?: string;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: requestId } = await params;
    const body: ApprovalRequest = await request.json();

    // Get the generation request first to check if it's a free one
    const { data: generationRequest, error: requestError } = await supabaseAdmin
      .from('web_generation_requests')
      .select('*')
      .eq('id', requestId)
      .single();

    if (requestError || !generationRequest) {
      return NextResponse.json(
        { error: 'Generation request not found' },
        { status: 404 }
      );
    }

    let userId: string | null = null;

    // If the request has a user_id, we must authenticate the user
    if (generationRequest.user_id) {
        const authHeader = request.headers.get('authorization');
        const token = authHeader?.replace('Bearer ', '') || 
                      request.cookies.get('supabase-auth-token')?.value;

        if (!token) {
          return NextResponse.json(
            { error: 'Authentication required for this request' },
            { status: 401 }
          );
        }

        const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
        
        if (authError || !user || user.id !== generationRequest.user_id) {
          return NextResponse.json(
            { error: 'Invalid or unauthorized user' },
            { status: 403 }
          );
        }
        userId = user.id;
    }


    // Validate request state
    if (generationRequest.status !== 'awaiting_approval' || generationRequest.awaiting_approval_for_step !== 2) {
      return NextResponse.json(
        { error: 'Request is not awaiting transformation approval' },
        { status: 400 }
      );
    }

    if (body.approved) {
      // APPROVAL FLOW
      
      // Update approval step
      await supabaseAdmin
        .from('web_generation_steps')
        .update({
          status: 'approved',
          approved_at: new Date().toISOString(),
          approved_by: userId // Can be null for free users
        })
        .eq('web_request_id', requestId)
        .eq('step_number', 3);

      // Update transformation step to mark as completed and paid
      await supabaseAdmin
        .from('web_generation_steps')
        .update({
          status: 'completed'
        })
        .eq('web_request_id', requestId)
        .eq('step_number', 2);

      // Update main request
      const updatedApprovedSteps = [...(generationRequest.approved_steps || []), 2];
      const costUpdate = generationRequest.user_id ? { actual_cost: (generationRequest.actual_cost || 0) + 0.05 } : {};

      await supabaseAdmin
        .from('web_generation_requests')
        .update({ 
          status: 'approved',
          current_step: 2, // Step 2: image approved
          awaiting_approval_for_step: null,
          approved_steps: updatedApprovedSteps,
          ...costUpdate,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      return NextResponse.json({
        request_id: requestId,
        status: 'approved',
        current_step: 2, // Step 2: image approved  
        approved: true,
        next_action: 'generate_video'
      });

    } else {
      // REJECTION FLOW
      
      // Update approval step with rejection
      await supabaseAdmin
        .from('web_generation_steps')
        .update({
          status: 'rejected',
          rejection_reason: body.rejection_reason || 'User rejected transformation',
          approved_at: new Date().toISOString(),
          approved_by: userId // Can be null for free users
        })
        .eq('web_request_id', requestId)
        .eq('step_number', 3);

      // Update main request
      const updatedRejectedSteps = [...(generationRequest.rejected_steps || []), 2];
      
      await supabaseAdmin
        .from('web_generation_requests')
        .update({ 
          status: 'rejected',
          current_step: 1, // Go back to start since we auto-generate
          awaiting_approval_for_step: null,
          rejected_steps: updatedRejectedSteps,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      return NextResponse.json({
        request_id: requestId,
        status: 'rejected',
        current_step: 1, // Updated: go back to start since we auto-generate
        approved: false,
        rejection_reason: body.rejection_reason,
        next_action: 'restart_flow'
      });
    }

  } catch (error: any) {
    console.error('Approve transformation error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process transformation approval', 
        details: error.message || error.toString() 
      },
      { status: 500 }
    );
  }
} 