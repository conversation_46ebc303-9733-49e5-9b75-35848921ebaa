import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client using the service role key (kept secret)
const supabaseServiceRole = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: Request) {
  try {
    const { chatId } = await request.json();
    if (!chatId) {
      return NextResponse.json({ error: "chatId is required" }, { status: 400 });
    }
    
    // Update the chat record to move its phase to 3.
    const { data: updatedChat, error: updateError } = await supabaseServiceRole
      .from("chats")
      .update({ phase: 3 })
      .eq("id", chatId)
      .select()
      .single();
      
    if (updateError) {
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }
    
    // Insert a system reply notifying the user.
    const systemMsg = "Chat has been moved to phase 3 successfully.";
    const { error: sysReplyError } = await supabaseServiceRole
      .from("chat_replies")
      .insert({
        chat_id: chatId,
        sender_role: "system",
        message: systemMsg,
      })
      .select()
      .single();
      
    if (sysReplyError) {
      return NextResponse.json({ error: sysReplyError.message }, { status: 500 });
    }
    
    return NextResponse.json({ success: true, data: updatedChat });
  } catch (err: any) {
    return NextResponse.json({ error: err.message }, { status: 500 });
  }
} 