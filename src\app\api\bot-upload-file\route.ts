import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { decode } from "base64-arraybuffer";

// Create a Supabase client using the service role key (kept secret)
const supabaseServiceRole = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface BotUploadFileRequest {
  conversationId: string;      // ID of the conversation
  externalIdentityId: string;  // ID of the external identity
  fileUrl?: string;            // URL of the uploaded file (already stored somewhere)
  fileBase64?: string;         // Base64 encoded file data (alternative to fileUrl)
  fileMetadata?: {
    originalFilename?: string;
    mimeType?: string;
    size?: number;
    [key: string]: any;
  };
  recordAsMessage: boolean;    // Whether to also record this upload as a user message
  sender?: 'user' | 'bot';     // Who is sending the message (default: 'user')
  textMessage?: string;        // Optional caption/text message associated with the upload
}

/**
 * Helper function to validate UUID format
 */
function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * Generate a unique filename with timestamp
 */
function generateUniqueFilename(originalFilename: string): string {
  const timestamp = Date.now();
  const extension = originalFilename.includes('.') 
    ? originalFilename.split('.').pop() 
    : '';
  const baseName = originalFilename.includes('.')
    ? originalFilename.substring(0, originalFilename.lastIndexOf('.'))
    : originalFilename;
    
  return `${baseName}_${timestamp}${extension ? '.' + extension : ''}`;
}

/**
 * API endpoint for recording file uploads from the bot (when a user sends an image)
 */
export async function POST(request: Request) {
  try {
    const {
      conversationId,
      externalIdentityId,
      fileUrl,
      fileBase64,
      fileMetadata = {},
      recordAsMessage,
      sender = 'user',
      textMessage
    } = await request.json() as BotUploadFileRequest;
    
    // Validate required fields
    if (!conversationId || !externalIdentityId || (!fileUrl && !fileBase64)) {
      return NextResponse.json({ 
        success: false, 
        error: "Missing required fields: conversationId, externalIdentityId, and either fileUrl or fileBase64" 
      }, { status: 400 });
    }
    
    // Validate UUID format
    if (!isValidUUID(conversationId)) {
      return NextResponse.json({
        success: false,
        error: "Invalid conversationId format. Must be a valid UUID."
      }, { status: 400 });
    }
    
    if (!isValidUUID(externalIdentityId)) {
      return NextResponse.json({
        success: false,
        error: "Invalid externalIdentityId format. Must be a valid UUID."
      }, { status: 400 });
    }

    // Ensure we have a filename with timestamp to make it unique
    const originalFilename = fileMetadata.originalFilename || `upload.${fileMetadata.mimeType?.split('/')[1] || 'bin'}`;
    const uniqueFilename = generateUniqueFilename(originalFilename);
    
    // Create a folder path using conversationId to organize files
    const folderPath = `conversations/${conversationId}/${uniqueFilename}`;
    
    // Handle file upload to Supabase Storage if provided as base64
    let finalFileUrl = fileUrl;
    if (fileBase64) {
      const fileData = decode(fileBase64);
      
      const { data: uploadResult, error: storageError } = await supabaseServiceRole
        .storage
        .from('message-uploads')
        .upload(folderPath, fileData, {
          contentType: fileMetadata.mimeType,
          cacheControl: '3600'
        });
      
      if (storageError) {
        return NextResponse.json({ success: false, error: storageError.message }, { status: 500 });
      }
      
      // Get the URL with an authorized token since the bucket isn't public
      const { data: signedUrlData, error: signedUrlError } = await supabaseServiceRole
        .storage
        .from('message-uploads')
        .createSignedUrl(folderPath, 60 * 60 * 24 * 7); // 7 day expiration
      
      if (signedUrlError) {
        return NextResponse.json({ success: false, error: signedUrlError.message }, { status: 500 });
      }
      
      finalFileUrl = signedUrlData.signedUrl;
    }
    
    // Update fileMetadata with the unique filename
    const updatedMetadata = {
      ...fileMetadata,
      originalFilename: uniqueFilename
    };
    
    // Check if externalIdentityId is in UUID format, and try to fix it if not
    let fixedExternalIdentityId = externalIdentityId;
    
    // Execute SQL to find external identity by its text ID
    const { data: identityData, error: identityError } = await supabaseServiceRole.rpc(
      'find_external_identity_by_id',
      { p_external_identity_id: externalIdentityId }
    );
    
    if (!identityError && identityData) {
      fixedExternalIdentityId = identityData;
      console.log(`Mapped external identity string '${externalIdentityId}' to UUID '${fixedExternalIdentityId}'`);
    } else {
      // Try direct SQL query as a fallback
      const { data: identityQueryData, error: identityQueryError } = await supabaseServiceRole
        .from('external_identities')
        .select('id')
        .eq('external_identity_id', externalIdentityId)
        .single();
        
      if (!identityQueryError && identityQueryData) {
        fixedExternalIdentityId = identityQueryData.id;
        console.log(`Found external identity UUID '${fixedExternalIdentityId}' for '${externalIdentityId}'`);
      }
    }
    
    // Step 1: Record the upload using direct SQL query to avoid type conversion issues
    const { data: uploadData, error: uploadRecordError } = await supabaseServiceRole.rpc(
      'insert_user_upload_from_api',
      {
        p_conversation_id: conversationId,
        p_external_identity_id_text: fixedExternalIdentityId, // Pass as text parameter
        p_file_url: finalFileUrl,
        p_metadata: updatedMetadata || null,
        p_sender: sender
      }
    );
    
    if (uploadRecordError) {
      return NextResponse.json({ 
        success: false, 
        error: uploadRecordError.message,
        details: "Error recording upload. Please check if external identity exists and is valid."
      }, { status: 500 });
    }
    
    const uploadId = uploadData;
    
    // Step 2: If requested, also record it as a message
    let messageId = null;
    if (recordAsMessage) {
      const { data: messageData, error: messageError } = await supabaseServiceRole.rpc(
        'insert_message_from_api',
        {
          p_conversation_id: conversationId,
          p_content: textMessage || null, // Use the textMessage parameter as message content
          p_upload_id: uploadId,
          p_sender: sender
        }
      );
      
      if (messageError) {
        return NextResponse.json({ success: false, error: messageError.message }, { status: 500 });
      }
      
      messageId = messageData;
    }
    
    // Step 3: Get the conversation details
    const { data: conversationData, error: conversationError } = await supabaseServiceRole
      .from('bot_conversations')
      .select('provider, provider_conversation_id')
      .eq('id', conversationId)
      .single();
      
    if (conversationError) {
      return NextResponse.json({ success: false, error: conversationError.message }, { status: 500 });
    }
    
    // Return success response with upload details
    return NextResponse.json({
      success: true,
      data: {
        uploadId,
        messageId,
        conversationId,
        externalIdentityId: fixedExternalIdentityId,
        fileUrl: finalFileUrl,
        filePath: folderPath,
        fileName: uniqueFilename,
        textMessage: textMessage || null,
        provider: conversationData.provider,
        providerConversationId: conversationData.provider_conversation_id
      }
    });
    
  } catch (err: any) {
    console.error('Error in bot-upload-file API:', err);
    return NextResponse.json({ success: false, error: err.message }, { status: 500 });
  }
} 