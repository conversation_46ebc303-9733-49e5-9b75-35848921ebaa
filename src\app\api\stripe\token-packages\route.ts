import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET() {
  try {
    const { data: packages, error } = await supabase
      .from('token_packages')
      .select('*')
      .eq('is_active', true)
      .order('price_cents', { ascending: true })

    if (error) {
      console.error('Error fetching token packages:', error)
      return NextResponse.json(
        { error: 'Failed to fetch token packages' },
        { status: 500 }
      )
    }

    // Format packages for frontend consumption
    const formattedPackages = packages.map(pkg => ({
      id: pkg.id,
      name: pkg.name,
      description: pkg.description,
      tokenAmount: pkg.token_amount,
      priceCents: pkg.price_cents,
      priceFormatted: formatPrice(pkg.price_cents, pkg.currency),
      currency: pkg.currency,
      isActive: pkg.is_active,
      // Calculate value (tokens per dollar)
      tokensPerDollar: Math.round(pkg.token_amount / (pkg.price_cents / 100))
    }))

    return NextResponse.json({
      packages: formattedPackages
    })

  } catch (error) {
    console.error('Error in token packages API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function formatPrice(cents: number, currency: string): string {
  const amount = cents / 100
  return new Intl.NumberFormat('ro-RO', {
    style: 'currency',
    currency: currency
  }).format(amount)
} 