name: Jest Unit Tests

on:
  push:
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      
      - name: Setup Node.js 22.x
        uses: actions/setup-node@v4
        with:
          node-version: '22.x'
          cache: 'npm'
          
      - name: Cache node_modules
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-
          
      - name: Install dependencies
        run: npm ci
      
      - name: Run linting
        run: npm run lint
        
      - name: Run Jest unit tests
        run: npm test -- --coverage --watchAll=false
        env:
          CI: true
          
      - name: Run Jest tests with coverage
        run: npm run test:coverage -- --watchAll=false
        env:
          CI: true
      
      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false
          
      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: jest-results-node22
          path: |
            coverage/
            junit.xml
          retention-days: 30 