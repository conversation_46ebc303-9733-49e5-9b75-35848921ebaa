import React from 'react';
import Button from 'react-bootstrap/Button';
import { format } from 'date-fns';
import styles from './MortalKombatGeneration.module.css';

interface MortalKombatGenerationProps {
  gen: any;
  t: (key: string) => string;
  onViewDetails: (id: string) => void;
}

const MortalKombatGeneration: React.FC<MortalKombatGenerationProps> = ({ gen, t, onViewDetails }) => (
  <div className={styles.mkGenCard}>
    <div className={styles.mkGenLeft}>
      {gen.input_data?.uploaded_image_url && (
        <img src={gen.input_data.uploaded_image_url} alt="Original" className={styles.mkGenImage} />
      )}
      <div className={styles.mkGenInfoBlock}>
        <div className={styles.mkGenInfo}>{t('mortal_kombat_generations_character')}: <span>{gen.input_data?.character_type || '-'}</span></div>
        <div className={styles.mkGenInfo}>{t('mortal_kombat_generations_status')}: <span>{gen.status}</span></div>
        <div className={styles.mkGenInfo}>{t('mortal_kombat_generations_created')}: {gen.created_at ? format(new Date(gen.created_at), 'yyyy-MM-dd HH:mm') : '-'}</div>
      </div>
    </div>
    <Button variant="primary" className={styles.mkGenButton} onClick={() => onViewDetails(gen.id)}>
      {t('mortal_kombat_generations_view_details')}
    </Button>
  </div>
);

export default MortalKombatGeneration; 