import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Create a Supabase client specifically for server-side use with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    );

    // TODO: Add robust authentication to ensure only admins can access this.
    // For now, we'll proceed, but this is a critical security step.

    // Fetch all free generations with their associated web generation requests
    const { data: freeGenerations, error } = await supabase
      .from('free_generations')
      .select(`
        id,
        flow_type,
        status,
        created_at,
        updated_at,
        web_generation_request_id,
        web_generation_requests (
          id,
          status,
          current_step,
          flow_type,
          created_at,
          input_data,
          output_data
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching free generations:', error);
      return NextResponse.json(
        { error: 'Failed to fetch free generations' },
        { status: 500 }
      );
    }

    // Get base URL with proper fallbacks
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 
                   (process.env.NODE_ENV === 'production' ? 'https://aivis.ro' : 'http://localhost:3000');

    // Process the data to include computed fields
    const processedData = freeGenerations?.map(fg => {
      const webGeneration = fg.web_generation_requests as any;
      return {
        id: fg.id,
        flow_type: fg.flow_type,
        status: fg.status,
        created_at: fg.created_at,
        updated_at: fg.updated_at,
        web_generation_request_id: fg.web_generation_request_id,
        link: `${baseUrl}/mk-free/${fg.id}`,
        web_generation: webGeneration,
        character: webGeneration?.input_data?.settings?.character_type || null,
        generation_status: webGeneration?.status || null,
        has_generated: !!webGeneration
      };
    }) || [];

    return NextResponse.json({
      freeGenerations: processedData,
      total: processedData.length
    });

  } catch (error) {
    console.error('Error in free generations API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 