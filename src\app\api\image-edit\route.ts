import { NextRequest, NextResponse } from 'next/server';
import { fal } from '@fal-ai/client';
import { createClient } from '@supabase/supabase-js';

// Configure the FAL client with the API key
fal.config({
  credentials: process.env.FAL_API_KEY || ''
});

// Create Supabase admin client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  { auth: { autoRefreshToken: false, persistSession: false } }
);

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    
    const { 
      prompt, 
      image_url, 
      user_id,
      token_cost = 10,
      guidance_scale = 3.5,
      num_images = 1,
      safety_tolerance = "5",
      output_format = "jpeg",
      aspect_ratio,
      seed 
    } = await request.json();

    // Validate required fields
    if (!prompt || !image_url) {
      return NextResponse.json(
        { error: 'Both prompt and image_url are required' },
        { status: 400 }
      );
    }

    // Check if FAL API key is configured
    if (!process.env.FAL_API_KEY) {
      return NextResponse.json(
        { error: 'FAL API key not configured' },
        { status: 500 }
      );
    }

    // Get user info
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      console.error('Auth error:', authError);
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    // Get user's wallet
    const { data: wallet, error: walletError } = await supabaseAdmin
      .from('wallets')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (walletError || !wallet) {
      return NextResponse.json(
        { error: 'User wallet not found' },
        { status: 404 }
      );
    }

    // Check if user has sufficient balance
    if (wallet.balance < token_cost) {
      return NextResponse.json(
        { error: 'Insufficient token balance', required: token_cost, available: wallet.balance },
        { status: 402 }
      );
    }

    // Deduct tokens from wallet
    const { error: deductError } = await supabaseAdmin
      .from('wallets')
      .update({ 
        balance: wallet.balance - token_cost,
        updated_at: new Date().toISOString()
      })
      .eq('id', wallet.id);

    if (deductError) {
      console.error('Error deducting tokens:', deductError);
      return NextResponse.json(
        { error: 'Failed to deduct tokens' },
        { status: 500 }
      );
    }

    // Record token transaction
    const { error: transactionError } = await supabaseAdmin
      .from('token_transactions')
      .insert({
        wallet_id: wallet.id,
        amount: -token_cost,
        description: `Image edit: ${prompt.slice(0, 100)}`,
        transaction_type: 'consumption'
      });

    if (transactionError) {
      console.error('Error recording transaction:', transactionError);
      // Note: We don't fail the request here as the tokens have already been deducted
    }

    // Submit the request to FAL API
    const result = await fal.subscribe("fal-ai/flux-pro/kontext", {
      input: {
        prompt,
        image_url,
        guidance_scale,
        num_images,
        safety_tolerance,
        output_format,
        ...(aspect_ratio && { aspect_ratio }),
        ...(seed && { seed })
      },
      logs: true,
      onQueueUpdate: (update) => {
        if (update.status === "IN_PROGRESS") {
          console.log("Processing:", update.logs?.map((log) => log.message).join('\n'));
        }
      },
    });

    return NextResponse.json({
      success: true,
      data: result.data,
      requestId: result.requestId
    });

  } catch (error: any) {
    console.error('Image editing error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to edit image', 
        details: error.message || error.toString() 
      },
      { status: 500 }
    );
  }
} 