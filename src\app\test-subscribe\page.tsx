"use client";
import React, { useState } from 'react';
import Link from 'next/link';
import styles from './test.module.css';

export default function TestSubscribe() {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<{message: string, isError: boolean} | null>(null);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setStatus(null);

    try {
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email,
          testMode: true // Explicitly request test mode
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Subscription failed');
      }

      setStatus({
        message: data.message || 'Subscription successful',
        isError: false
      });
      setEmail('');
    } catch (error: any) {
      setStatus({
        message: error.message || 'An error occurred',
        isError: true
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <header className={styles.header}>
        <h1>Test Subscription API</h1>
        <Link href="/" className={styles.backLink}>Back to Home</Link>
      </header>
      
      <div className={styles.content}>
        <section className={styles.instructions}>
          <h2>Testing Instructions</h2>
          <p>This page allows you to test the subscription API without affecting your real Mailchimp list.</p>
          
          <div className={styles.testCases}>
            <h3>Test Cases:</h3>
            <ul>
              <li>
                <strong>Normal subscription:</strong> Enter any valid email (except ones ending with @test.com)
              </li>
              <li>
                <strong>Already subscribed:</strong> Use any email ending with @test.com (e.g., <EMAIL>)
              </li>
              <li>
                <strong>Invalid email:</strong> Enter an invalid email format
              </li>
            </ul>
          </div>
        </section>

        <section className={styles.testForm}>
          <h2>Test Form</h2>
          <form onSubmit={handleSubmit}>
            <div className={styles.formGroup}>
              <label htmlFor="email">Email Address:</label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter email to test"
                required
                disabled={loading}
              />
            </div>
            <button type="submit" disabled={loading} className={styles.submitButton}>
              {loading ? 'Testing...' : 'Test Subscribe'}
            </button>
          </form>

          {status && (
            <div className={status.isError ? styles.error : styles.success}>
              <p>{status.message}</p>
            </div>
          )}
        </section>
      </div>
    </div>
  );
} 