"use client";

import Toast from "react-bootstrap/Toast";
import ToastContainer from "react-bootstrap/ToastContainer";
import Button from 'react-bootstrap/Button';

import { useDispatch } from 'react-redux';
import { fetchUserProfile } from '../../store/userSlice';
import { AppDispatch } from '../../store';

import React, { useEffect, useState } from "react";
import { supabase } from "../../lib/supabaseClient";
import Header from "@/components/Header";
import { logger } from '../../utils/logger';

export default function ProfilePage() {
  const dispatch = useDispatch<AppDispatch>();
  const [showToast, setShowToast] = useState(false);
  const [loading, setLoading] = useState(true);
  const [tokenCount, setTokenCount] = useState<number | null>(null);
  const [chatsCount, setChatsCount] = useState<number>(0);
  const [regDate, setRegDate] = useState<string | null>(null);
  const [telegramLinked, setTelegramLinked] = useState<boolean>(false);
  const [linkLoading, setLinkLoading] = useState(false);
  const [linkError, setLinkError] = useState<string | null>(null);
  const [showLinkModal, setShowLinkModal] = useState(false);
  const [linkCode, setLinkCode] = useState<string | null>(null);
  const [walletBalance, setWalletBalance] = useState<number | null>(null);
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    async function fetchProfile() {
      // Get the current session and user info.
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) {
        logger.error("Error fetching session:", sessionError.message);
        setLoading(false);
        return;
      }
      if (!session || !session.user) {
        // If there's no session, redirect to login.
        window.location.href = "/login";
        setLoading(false);
        return;
      }
      const user = session.user;
      // Set the user's registration date from auth (if available).
      setRegDate(user.created_at);
      
      // Fetch tokens from the profiles table.
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select("tokens")
        .eq("user_id", user.id)
        .single();
      if (profileError) {
        logger.error("Error fetching tokens:", profileError.message);
      } else {
        setTokenCount(profileData.tokens);
      }

      // Count the total number of chats started by the user.
      // Using the head mode with count. In this mode, 'data' is null and 'count' holds the number.
      const { count, error: countError } = await supabase
        .from("chats")
        .select("*", { count: "exact", head: true })
        .eq("user_id", user.id);
      if (countError) {
        logger.error("Error counting chats:", countError.message);
      } else if (count !== null) {
        setChatsCount(count);
      }

      // Check if telegram is linked
      const { data: ext, error: extError } = await supabase
        .from('external_identities')
        .select('telegram_id')
        .eq('user_id', user.id)
        .maybeSingle();
      setTelegramLinked(!!(ext && ext.telegram_id));

      setLoading(false);
    }
    fetchProfile();
  }, []);

  useEffect(() => {
    async function fetchWalletBalance() {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session || !session.user) return;
      setUser(session.user);
      // Fetch wallet balance from wallets table
      const { data: wallet, error } = await supabase
        .from('wallets')
        .select('balance')
        .eq('user_id', session.user.id)
        .maybeSingle();
      if (!error && wallet) {
        setWalletBalance(wallet.balance);
      }
    }
    fetchWalletBalance();
  }, []);

  const handleLinkTelegram = async () => {
    setLinkLoading(true);
    setLinkError(null);
    setLinkCode(null);
    try {
      // Get session
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('Not authenticated');
      // Generate a random 6-digit code
      const code = Math.random().toString(36).substring(2, 8).toUpperCase();
      // Insert into link_codes table
      const { error } = await supabase.from('link_codes').insert({
        code,
        user_id: session.user.id
      });
      if (error) throw error;
      setLinkCode(code);
      setShowLinkModal(true);
    } catch (err: any) {
      setLinkError('Eroare la generarea codului. Încearcă din nou.');
    } finally {
      setLinkLoading(false);
    }
  };

  const handleUnlinkTelegram = async () => {
    setLinkLoading(true);
    setLinkError(null);
    try {
      // Get session
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('Not authenticated');
      // Find the external_identity for this user
      const { data: ext, error: extError } = await supabase
        .from('external_identities')
        .select('id')
        .eq('user_id', session.user.id)
        .maybeSingle();
      if (extError || !ext) throw extError || new Error('No external identity found');
      // Unlink by setting user_id to null
      const { error: updateError } = await supabase
        .from('external_identities')
        .update({ user_id: null })
        .eq('id', ext.id);
      if (updateError) throw updateError;
      setTelegramLinked(false);
    } catch (err: any) {
      setLinkError('Eroare la delink. Încearcă din nou.');
    } finally {
      setLinkLoading(false);
    }
  };

  return (
    <>
      <Header />
      <main style={{ padding: "2rem" }}>
        {loading ? (
          <div>Loading profile...</div>
        ) : (
          <div>
            <h1>Profile</h1>
            <p>Email: {user?.email}</p>
            <p>Wallet Balance: <strong>{walletBalance !== null ? walletBalance : '...'}</strong> tokens</p>
            <p>
              <strong>Total Chats Started:</strong> {chatsCount}
            </p>
            <p>
              <strong>Registered On:</strong> {regDate ? new Date(regDate).toLocaleDateString() : ""}
            </p>
            <div style={{ display: 'flex', alignItems: 'center', gap: 16, margin: '16px 0' }}>
              <Button onClick={handleLinkTelegram} variant="outline-success" disabled={linkLoading}>
                {linkLoading ? 'Se generează cod...' : (telegramLinked ? 'Telegram conectat' : 'Link Telegram')}
              </Button>
              {telegramLinked && (
                <Button
                  variant="outline-danger"
                  onClick={handleUnlinkTelegram}
                  disabled={linkLoading}
                >
                  Delink Telegram
                </Button>
              )}
            </div>
            {linkError && <div style={{ color: 'red', marginBottom: 8 }}>{linkError}</div>}
            {showLinkModal && (
              <div style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, background: 'rgba(0,0,0,0.5)', zIndex: 1000, display: 'flex', alignItems: 'center', justifyContent: 'center' }} onClick={() => setShowLinkModal(false)}>
                <div style={{ background: '#fff', borderRadius: 8, padding: 32, minWidth: 320, position: 'relative' }} onClick={e => e.stopPropagation()}>
                  <button style={{ position: 'absolute', top: 8, right: 12, background: 'none', border: 'none', fontSize: 24, color: '#888', cursor: 'pointer' }} onClick={() => setShowLinkModal(false)}>×</button>
                  <h3>Conectează-ți contul de Telegram</h3>
                  <p>Trimite acest cod către <b>botul Telegram</b> pentru a-ți conecta contul:</p>
                  <div style={{ fontSize: 28, fontWeight: 700, letterSpacing: 2, margin: '1rem 0', color: '#0070f3' }}>{linkCode}</div>
                  <a
                    href={`https://t.me/aivisbot?start=${linkCode}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                      display: 'inline-block',
                      margin: '0.5rem 0 1rem 0',
                      padding: '0.5rem 1.2rem',
                      background: '#229ED9',
                      color: '#fff',
                      borderRadius: 6,
                      fontWeight: 500,
                      textDecoration: 'none',
                      fontSize: 16
                    }}
                  >
                    Trimite codul pe Telegram
                  </a>
                  <p style={{ fontSize: 14, color: '#555' }}>Deschide Telegram, caută botul și trimite-i acest cod ca mesaj.</p>
                  <Button variant="secondary" onClick={() => setShowLinkModal(false)} style={{ marginTop: 16 }}>Închide</Button>
                </div>
              </div>
            )}
          </div>
        )}
      </main>
      <ToastContainer position="top-center" className="p-3">
        <Toast onClose={() => setShowToast(false)} show={showToast} delay={3000} autohide>
          <Toast.Body>Tokens were added successfully</Toast.Body>
        </Toast>
      </ToastContainer>
    </>
  );
} 