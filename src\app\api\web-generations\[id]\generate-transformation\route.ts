import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { fal } from '@fal-ai/client';

// Create a Supabase client specifically for server-side use with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

// Configure the FAL client with the API key
fal.config({
  credentials: process.env.FAL_API_KEY || ''
});

// Transformation function for GPT-Image-1
async function transformWithGPTImage1(imageUrl: string, prompt: string) {
  const result = await fal.subscribe("fal-ai/gpt-image-1/edit-image/byok", {
    input: {
      image_urls: [imageUrl],
      prompt: prompt,
      image_size: "auto",
      num_images: 1,
      quality: "auto",
      openai_api_key: process.env.OPENAI_API_KEY
    },
    logs: true,
    onQueueUpdate: (update) => {
      if (update.status === "IN_PROGRESS") {
        console.log("GPT-Image-1 Processing:", update.logs?.map((log) => log.message).join('\n'));
      }
    },
  });

  return {
    transformedImageUrl: result.data.images[0].url,
    requestId: result.requestId,
    originalResponse: result.data,
    model: 'gpt-image-1/edit-image/byok',
    userTokenCost: 10, // User always pays 10 tokens
    internalCost: 0.04 // Internal API cost $0.04
  };
}

// Transformation function for SeedEdit
async function transformWithSeedEdit(imageUrl: string, prompt: string) {
  const result = await fal.subscribe("fal-ai/bytedance/seededit/v3/edit-image", {
    input: {
      prompt: prompt,
      image_url: imageUrl,
      guidance_scale: 0.5
    },
    logs: true,
    onQueueUpdate: (update) => {
      if (update.status === "IN_PROGRESS") {
        console.log("SeedEdit Processing:", update.logs?.map((log) => log.message).join('\n'));
      }
    },
  });

  return {
    transformedImageUrl: result.data.image.url,
    requestId: result.requestId,
    originalResponse: result.data,
    model: 'fal-ai/bytedance/seededit/v3/edit-image',
    userTokenCost: 10, // User always pays 10 tokens
    internalCost: 0.02 // Internal API cost $0.02 (cheaper for us)
  };
}

// Transformation function for FLUX.1 Kontext [pro]
async function transformWithFluxKontext(imageUrl: string, prompt: string) {
  const result = await fal.subscribe("fal-ai/flux-pro/kontext", {
    input: {
      prompt: prompt,
      image_url: imageUrl,
      guidance_scale: 3.5,
      num_images: 1,
      safety_tolerance: "2",
      output_format: "jpeg"
    },
    logs: true,
    onQueueUpdate: (update) => {
      if (update.status === "IN_PROGRESS") {
        console.log("FLUX Kontext Processing:", update.logs?.map((log) => log.message).join('\n'));
      }
    },
  });

  return {
    transformedImageUrl: result.data.images[0].url,
    requestId: result.requestId,
    originalResponse: result.data,
    model: 'fal-ai/flux-pro/kontext',
    userTokenCost: 10, // User always pays 10 tokens
    internalCost: 0.03 // Internal API cost (estimated)
  };
}

// Get selected image generator from database
async function getSelectedImageGenerator(): Promise<string> {
  const { data, error } = await supabaseAdmin
    .from('config_options')
    .select('value')
    .eq('key', 'mk_image_generator')
    .single();

  if (error || !data || !data.value || !data.value.selected) {
    console.log('No image generator config found, defaulting to gpt-image-1');
    return 'gpt-image-1';
  }

  return data.value.selected;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: requestId } = await params;
    const body = await request.json();
    const customPrompt = body.custom_prompt;

    // Get user from auth header or cookie
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '') || 
                  request.cookies.get('supabase-auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user info
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    // Get the generation request
    const { data: generationRequest, error: requestError } = await supabaseAdmin
      .from('web_generation_requests')
      .select('*')
      .eq('id', requestId)
      .eq('user_id', user.id)
      .single();

    if (requestError || !generationRequest) {
      return NextResponse.json(
        { error: 'Generation request not found' },
        { status: 404 }
      );
    }

    // Validate request state
    if (generationRequest.current_step !== 1) {
      return NextResponse.json(
        { error: 'Invalid request state for transformation generation' },
        { status: 400 }
      );
    }

    // Get selected image generator
    const selectedImageGenerator = await getSelectedImageGenerator();
    console.log('Using image generator for generate-transformation:', selectedImageGenerator);

    // Check required environment variables
    if (!process.env.FAL_API_KEY) {
      return NextResponse.json(
        { error: 'FAL API key not configured' },
        { status: 500 }
      );
    }

    // Check if OpenAI API key is required for GPT-Image-1
    if (selectedImageGenerator === 'gpt-image-1' && !process.env.OPENAI_API_KEY) {
      return NextResponse.json(
        { error: 'OpenAI API key not configured' },
        { status: 500 }
      );
    }

    // Update request status to in_progress
    await supabaseAdmin
      .from('web_generation_requests')
      .update({ 
        status: 'in_progress',
        current_step: 2,
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId);

    // Create step record for transformation
    const { data: stepData, error: stepError } = await supabaseAdmin
      .from('web_generation_steps')
      .insert({
        web_request_id: requestId,
        step_number: 2,
        step_type: 'image_transform',
        status: 'in_progress',
        fal_model: selectedImageGenerator === 'gpt-image-1' ? 'gpt-image-1/edit-image/byok' : selectedImageGenerator === 'fal-seededit-v3' ? 'fal-ai/bytedance/seededit/v3/edit-image' : 'fal-ai/flux-pro/kontext',
        requires_approval: true,
        input_data: {
          image_url: generationRequest.input_data.uploaded_image_url,
          prompt: customPrompt || generationRequest.input_data.transformation_prompt,
          character_type: generationRequest.input_data.character_type,
          image_generator: selectedImageGenerator
        },
        started_at: new Date().toISOString()
      })
      .select()
      .single();

    if (stepError) {
      console.error('Step creation error:', stepError);
      return NextResponse.json(
        { error: 'Failed to create transformation step' },
        { status: 500 }
      );
    }

    // Call FAL API for image transformation
    try {
             let transformationResult;

       if (selectedImageGenerator === 'gpt-image-1') {
         transformationResult = await transformWithGPTImage1(generationRequest.input_data.uploaded_image_url, customPrompt || generationRequest.input_data.transformation_prompt);
       } else if (selectedImageGenerator === 'fal-seededit-v3') {
         transformationResult = await transformWithSeedEdit(generationRequest.input_data.uploaded_image_url, customPrompt || generationRequest.input_data.transformation_prompt);
       } else if (selectedImageGenerator === 'fal-flux-pro-kontext') {
         transformationResult = await transformWithFluxKontext(generationRequest.input_data.uploaded_image_url, customPrompt || generationRequest.input_data.transformation_prompt);
       } else {
         throw new Error(`Unsupported image generator: ${selectedImageGenerator}`);
       }

      // Update step with success
      await supabaseAdmin
        .from('web_generation_steps')
        .update({
          status: 'awaiting_approval',
          fal_request_id: transformationResult.requestId,
          output_data: {
            transformed_image_url: transformationResult.transformedImageUrl,
            original_response: transformationResult.originalResponse
          },
          completed_at: new Date().toISOString(),
          token_cost: transformationResult.userTokenCost
        })
        .eq('id', stepData.id);

      // Update main request
      const updatedOutputData = {
        ...generationRequest.output_data,
        transformed_image_url: transformationResult.transformedImageUrl
      };

      await supabaseAdmin
        .from('web_generation_requests')
        .update({ 
          status: 'awaiting_approval',
          awaiting_approval_for_step: 2,
          output_data: updatedOutputData,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      // Create approval step record
      await supabaseAdmin
        .from('web_generation_steps')
        .insert({
          web_request_id: requestId,
          step_number: 3,
          step_type: 'user_approval',
          status: 'pending',
          requires_approval: true,
          input_data: {
            transformation_step_id: stepData.id,
            original_image_url: generationRequest.input_data.uploaded_image_url,
            transformed_image_url: transformationResult.transformedImageUrl
          }
        });

      return NextResponse.json({
        request_id: requestId,
        status: 'awaiting_approval',
        current_step: 2,
        transformation: {
          original_image_url: generationRequest.input_data.uploaded_image_url,
          transformed_image_url: transformationResult.transformedImageUrl,
          character_type: generationRequest.input_data.character_type,
          prompt_used: customPrompt || generationRequest.input_data.transformation_prompt
        },
        step_cost: transformationResult.userTokenCost,
        step_tokens: transformationResult.userTokenCost,
        next_action: 'approve_transformation'
      });

    } catch (error: any) {
      console.error('Transformation error:', error);
      return NextResponse.json(
        { 
          error: 'Failed to generate transformation', 
          details: error.message || error.toString() 
        },
        { status: 500 }
      );
    }

  } catch (error: any) {
    console.error('Generate transformation error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to generate transformation', 
        details: error.message || error.toString() 
      },
      { status: 500 }
    );
  }
} 