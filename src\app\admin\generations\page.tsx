"use client";

import React, { useState, useEffect } from "react";
import styles from "../admin.module.css";
import { supabase } from "@/lib/supabaseClient";

// Define types for our data
interface GeneratedImage {
  id: string;
  result_url: string;
  created_at: string;
  prompt_text: string | null;
  source_type: string;
  parameters: any;
  token_cost?: number;
  wallet?: {
    id: string;
    balance: number;
  } | null;
  conversation?: {
    id: string;
    provider: string;
    external_identity?: {
      id: string;
      telegram_id?: string;
      whatsapp_id?: string;
      telegram_first_name?: string;
      telegram_last_name?: string;
      whatsapp_name?: string;
      user_id?: string;
    } | null;
  } | null;
}

// Modal component for image details
const ImageDetailsModal = ({ 
  image, 
  user, 
  formatDate, 
  onClose 
}: { 
  image: GeneratedImage;
  user: any;
  formatDate: (date: string) => string;
  onClose: () => void;
}) => {
  const parameters = image.parameters || {};
  
  // Close modal when clicking outside the content
  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };
  
  return (
    <div className={styles.imageModalOverlay} onClick={handleBackdropClick}>
      <div className={styles.imageModal}>
        <button className={styles.closeModalButton} onClick={onClose}>
          ×
        </button>
        
        <div className={styles.modalImageContainer}>
          <img 
            src={image.result_url} 
            alt={image.prompt_text || 'Generated image'} 
            className={styles.modalImage} 
          />
        </div>
        
        <div className={styles.modalDetails}>
          <p className={styles.imageDate}>
            {formatDate(image.created_at)}
          </p>
          
          {image.prompt_text && (
            <p className={styles.imagePrompt}>{image.prompt_text}</p>
          )}
          
          <div className={styles.imageMeta}>
            <span className={styles.imageSource}>
              {image.source_type === 'generated' ? 'AI Generated' : 'AI Edited'}
            </span>
            
            {image.token_cost && (
              <span className={styles.tokenCost}>
                {image.token_cost} tokens
              </span>
            )}
          </div>
          
          {parameters.width && parameters.height && (
            <p className={styles.imageResolution}>
              Resolution: {parameters.width}×{parameters.height}
            </p>
          )}
          
          {parameters.style && (
            <p className={styles.imageStyle}>
              Style: {parameters.style}
            </p>
          )}
          
          <div className={styles.userInfoCard}>
            <div className={styles.imageUserDetails}>
              <span 
                className={`${styles.platformBadge} ${styles[user.platform]}`}
              >
                {user.platform}
              </span>
              <span className={styles.imageUserName}>
                {user.name}
              </span>
              {user.id && (
                <span className={styles.userId}>
                  {user.id}
                </span>
              )}
            </div>
            {image.wallet?.balance !== undefined && (
              <div className={styles.walletInfo}>
                Balance: {image.wallet.balance} tokens
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default function GenerationsPage() {
  const [images, setImages] = useState<GeneratedImage[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedImage, setSelectedImage] = useState<GeneratedImage | null>(null);

  // Fetch data on component mount
  useEffect(() => {
    fetchGeneratedImages();
  }, []);

  // Function to fetch generated images
  const fetchGeneratedImages = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Fetch images with more comprehensive joins to get user details
      const { data, error } = await supabase
        .from('images')
        .select(`
          id, 
          result_url, 
          created_at, 
          prompt_text, 
          source_type,
          parameters,
          wallet:wallet_id (
            id,
            balance,
            user_id,
            external_identity_id
          ),
          conversation:conversation_id (
            id,
            provider,
            external_identity:external_identity_id (
              id,
              telegram_id, 
              whatsapp_id,
              telegram_first_name,
              telegram_last_name,
              whatsapp_name,
              user_id
            )
          )
        `)
        .order('created_at', { ascending: false });
      
      if (error) {
        throw error;
      }
      
      // For web users, get their email addresses
      const userData = await Promise.all(
        data.map(async (img: any) => {
          // Calculate token cost based on parameters (example calculation)
          let tokenCost = 0;
          if (img.parameters) {
            // Base cost
            tokenCost = 10;
            
            // Add cost based on resolution
            if (img.parameters.width && img.parameters.height) {
              const totalPixels = img.parameters.width * img.parameters.height;
              if (totalPixels > 1000000) { // 1MP
                tokenCost += 5;
              }
            }
            
            // Add cost for realistic style
            if (img.parameters.style === 'realistic') {
              tokenCost += 2;
            }
          }
          
          return {
            ...img,
            token_cost: tokenCost
          };
        })
      );
      
      setImages(userData);
    } catch (err: any) {
      setError(`Error fetching images: ${err.message}`);
      console.error('Error fetching images:', err);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to format date in a more readable way
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    
    // Format date: May 11, 2025
    const formattedDate = date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
    
    // Format time: 4:35 PM
    const formattedTime = date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
    
    return `${formattedDate} at ${formattedTime}`;
  };

  // Helper function to get user display name
  const getUserDisplay = (image: GeneratedImage) => {
    // Telegram user
    if (image.conversation?.external_identity?.telegram_id) {
      const firstName = image.conversation.external_identity.telegram_first_name || '';
      const lastName = image.conversation.external_identity.telegram_last_name || '';
      const displayName = [firstName, lastName].filter(Boolean).join(' ');
      
      return {
        name: displayName || image.conversation.external_identity.telegram_id,
        id: image.conversation.external_identity.telegram_id,
        platform: 'telegram'
      };
    }
    
    // WhatsApp user
    if (image.conversation?.external_identity?.whatsapp_id) {
      return {
        name: image.conversation.external_identity.whatsapp_name || image.conversation.external_identity.whatsapp_id,
        id: image.conversation.external_identity.whatsapp_id,
        platform: 'whatsapp'
      };
    }
    
    return {
      name: 'Unknown User',
      platform: 'unknown'
    };
  };

  return (
    <div className={styles.generationsSection}>
      <div className={styles.sectionHeader}>
        <h2>Telegram generations</h2>
        
        <div className={styles.controlsGroup}>
          <div className={styles.togglesContainer}>
            <div className={styles.detailsControl}>
              <label className={styles.detailsToggle}>
                <input
                  type="checkbox"
                  checked={showDetails}
                  onChange={() => setShowDetails(!showDetails)}
                />
                <span className={styles.toggleSwitch}></span>
                <span className={styles.toggleLabel}>
                  Show Details
                </span>
                <span className={styles.toggleStatus}>
                  {showDetails ? 'On' : 'Off'}
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
      
      {loading && <p className={styles.loadingText}>Loading image data...</p>}
      {error && <p className={styles.errorText}>{error}</p>}
      
      {!loading && !error && (
        <>
          <p className={styles.statsText}>Total images: {images.length}</p>
          
          {images.length === 0 ? (
            <p>No images found.</p>
          ) : (
            <div className={showDetails ? styles.imagesGrid : styles.imagesSimpleGrid}>
              {images.map((image) => {
                const user = getUserDisplay(image);
                const parameters = image.parameters || {};
                
                return showDetails ? (
                  // Detailed view
                  <div key={image.id} className={styles.imageCard}>
                    <div className={styles.cardImageContainer}>
                      <img 
                        src={image.result_url} 
                        alt={image.prompt_text || 'Generated image'} 
                        className={styles.generatedImage} 
                      />
                    </div>
                    <div className={styles.imageDetails}>
                      <p className={styles.imageDate}>
                        {formatDate(image.created_at)}
                      </p>
                      {image.prompt_text && (
                        <p className={styles.imagePrompt}>{image.prompt_text}</p>
                      )}
                      
                      <div className={styles.imageMeta}>
                        <span className={styles.imageSource}>
                          {image.source_type === 'generated' ? 'AI Generated' : 'AI Edited'}
                        </span>
                        
                        {image.token_cost && (
                          <span className={styles.tokenCost}>
                            {image.token_cost} tokens
                          </span>
                        )}
                      </div>
                      
                      {parameters.width && parameters.height && (
                        <p className={styles.imageResolution}>
                          Resolution: {parameters.width}×{parameters.height}
                        </p>
                      )}
                      
                      {parameters.style && (
                        <p className={styles.imageStyle}>
                          Style: {parameters.style}
                        </p>
                      )}
                      
                      <div className={styles.userInfoCard}>
                        <div className={styles.imageUserDetails}>
                          <span 
                            className={`${styles.platformBadge} ${styles[user.platform]}`}
                          >
                            {user.platform}
                          </span>
                          <span className={styles.imageUserName}>
                            {user.name}
                          </span>
                          {user.id && (
                            <span className={styles.userId}>
                              {user.id}
                            </span>
                          )}
                        </div>
                        {image.wallet?.balance !== undefined && (
                          <div className={styles.walletInfo}>
                            Balance: {image.wallet.balance} tokens
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ) : (
                  // Simple view - just the image
                  <div 
                    key={image.id} 
                    className={styles.imageSimpleCard}
                    onClick={() => setSelectedImage(image)}
                  >
                    <div className={styles.cardImageContainer}>
                      <img 
                        src={image.result_url} 
                        alt={image.prompt_text || 'Generated image'} 
                        className={styles.generatedImage} 
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          )}
          
          {/* Modal for displaying image details when an image is clicked in simple mode */}
          {selectedImage && (
            <ImageDetailsModal
              image={selectedImage}
              user={getUserDisplay(selectedImage)}
              formatDate={formatDate}
              onClose={() => setSelectedImage(null)}
            />
          )}
        </>
      )}
    </div>
  );
} 