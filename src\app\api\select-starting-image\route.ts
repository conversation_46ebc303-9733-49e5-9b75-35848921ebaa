import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client using the service role key (do not expose this key client-side)
const supabaseServiceRole = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: Request) {
  try {
    const { chatId, replyId } = await request.json();

    if (!chatId || !replyId) {
      return NextResponse.json(
        { error: "chatId and replyId are required" },
        { status: 400 }
      );
    }

    // Update the chat record with the selected starting image and progress to phase 4.
    const { data: updatedChat, error: updateError } = await supabaseServiceRole
      .from("chats")
      .update({ starting_image_reply_id: replyId, phase: 4 })
      .eq("id", chatId);

    if (updateError) {
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }

    // Insert a system message notifying the user.
    const systemMessage = "Thanks for choosing the starting image. We will use that in order to create your video";
    const { error: systemError } = await supabaseServiceRole
      .from("chat_replies")
      .insert({
        chat_id: chatId,
        sender_role: "system",
        message: systemMessage,
      })
      .select();

    if (systemError) {
      return NextResponse.json({ error: systemError.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data: updatedChat });
  } catch (err: any) {
    return NextResponse.json({ error: err.message }, { status: 500 });
  }
} 