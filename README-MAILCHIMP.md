# Mailchimp Integration for Aivis

This README provides instructions for setting up and configuring the Mailchimp email subscription system for your Aivis platform.

## Setup Instructions

### 1. Create a Mailchimp Account

If you don't already have a Mailchimp account:
1. Go to [Mailchimp.com](https://mailchimp.com/) and sign up for an account
2. Choose the Free plan (supports up to 500 contacts)

### 2. Create an Audience (List)

1. In your Mailchimp dashboard, go to "Audience" > "Audience dashboard"
2. Click "Create Audience" and follow the setup wizard
3. Once created, go to "Settings" > "Audience name and defaults" to find your Audience ID

### 3. Get Your API Keys

1. Click on your profile icon in the bottom left
2. Select "Account & billing"
3. Go to "Extras" > "API keys"
4. Create a new API key with appropriate permissions
5. Note the API key and your server prefix (the part in your Mailchimp URL, e.g., "us1" in "https://us1.admin.mailchimp.com")

### 4. Configure Your Application

You have two options to set up the Mailchimp credentials:

#### Option 1: Environment Variables (Recommended)

Set the following environment variables on your hosting platform:

```
MAILCHIMP_API_KEY=your_api_key_here
MAILCHIMP_SERVER_PREFIX=your_server_prefix_here (e.g., us1)
MAILCHIMP_LIST_ID=your_audience_id_here
```

#### Option 2: Direct Configuration (Development Only)

For local development, you can update the values in `src/utils/mailchimpConfig.ts`:

```typescript
const MAILCHIMP_API_KEY = 'your-api-key-here';
const MAILCHIMP_SERVER_PREFIX = 'us1'; // e.g., us1, us2
const MAILCHIMP_LIST_ID = 'your-list-id-here';
```

> **WARNING**: Never commit your actual API keys to version control! Use environment variables for production.

## Testing

### Test Mode

There's a dedicated test page at `/test-subscribe` that allows you to test the subscription flow without adding real subscribers to your Mailchimp list.

To enable test mode for development:
- Set `MAILCHIMP_TEST_MODE=true` in your environment

### Production Mode

The homepage subscription form always operates in production mode and will add real subscribers to your Mailchimp list.

## Managing Subscribers

1. Log in to your Mailchimp account
2. Go to "Audience" > "All contacts"
3. Here you can view, manage, and export your subscriber list

## Creating Email Campaigns

When you're ready to announce your platform launch:

1. Log in to your Mailchimp account
2. Go to "Campaigns" 
3. Click "Create Campaign" and choose "Email"
4. Follow the wizard to create and send your announcement email

## Support

For Mailchimp-specific questions, refer to the [Mailchimp API Documentation](https://mailchimp.com/developer/marketing/api/) 