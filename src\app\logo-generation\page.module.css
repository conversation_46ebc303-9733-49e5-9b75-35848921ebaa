.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
}

.header p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 2rem;
}

.langSelector {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.langSelector select {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
}

.generationForm {
  max-width: 800px;
  margin: 0 auto;
  background: #f9f9f9;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.formGroup {
  margin-bottom: 2rem;
}

.label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.textarea {
  width: 100%;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  resize: vertical;
  min-height: 100px;
}

.textarea:focus {
  outline: none;
  border-color: #0070f3;
  box-shadow: 0 0 0 3px rgba(0, 112, 243, 0.1);
}

.styleGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.styleButton {
  padding: 1rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.styleButton:hover {
  border-color: #0070f3;
  background: #f0f8ff;
}

.styleButton.selected {
  border-color: #0070f3;
  background: #0070f3;
  color: white;
}

.costInfo {
  background: #e8f4f8;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  border-left: 4px solid #0070f3;
}

.costInfo p {
  margin: 0;
  color: #0070f3;
  font-weight: 600;
}

.generateButton {
  width: 100%;
  padding: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
}

.error {
  background: #fee;
  border: 1px solid #fcc;
  color: #c00;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  text-align: center;
}

.result {
  margin-top: 2rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.result h3 {
  color: #28a745;
  margin-bottom: 1rem;
  text-align: center;
}

.imageContainer {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.generatedImage {
  max-width: 400px;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.resultActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.resultActions button {
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .generationForm {
    padding: 1.5rem;
  }
  
  .styleGrid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
  
  .resultActions {
    flex-direction: column;
  }
  
  .generatedImage {
    max-width: 100%;
    height: auto;
  }
} 