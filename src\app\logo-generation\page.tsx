"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import Header from '@/components/Header';
import Button from 'react-bootstrap/Button';
import styles from './page.module.css';
import { useTranslation } from '@/hooks/useTranslation';
import { supabase } from '../../lib/supabaseClient';

export default function LogoGenerationPage() {
  const router = useRouter();
  const { user } = useSelector((state: RootState) => state.user);
  const [lang, setLang] = useState<'en' | 'ro'>('ro');
  const { t } = useTranslation(lang);
  
  const [prompt, setPrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState('minimalist');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const styles_options = [
    { value: 'minimalist', label: t('logo_style_minimalist') },
    { value: 'black_white', label: t('logo_style_black_white') },
    { value: 'modern', label: t('logo_style_modern') },
    { value: 'vintage', label: t('logo_style_vintage') },
    { value: 'colorful', label: t('logo_style_colorful') },
    { value: 'corporate', label: t('logo_style_corporate') },
  ];

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      setError('Please enter a logo description');
      return;
    }

    setIsGenerating(true);
    setError(null);
    setGeneratedImage(null);

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/api/logo-generation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          prompt,
          style: selectedStyle,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Logo generation failed');
      }

      const result = await response.json();
      setGeneratedImage(result.imageUrl);
    } catch (err: any) {
      setError(err.message || 'Failed to generate logo');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = async () => {
    if (!generatedImage) return;

    try {
      const response = await fetch(generatedImage);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `logo-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Download failed:', err);
    }
  };

  return (
    <div>
      <Header />
      <main className={styles.container}>
        <div className={styles.header}>
          <h1>{t('logo_generation_title')}</h1>
          <p>{t('logo_generation_subtitle')}</p>
          <div className={styles.langSelector}>
            <select value={lang} onChange={e => setLang(e.target.value as 'en' | 'ro')}>
              <option value="ro">Română</option>
              <option value="en">English</option>
            </select>
          </div>
        </div>

        <div className={styles.generationForm}>
          <div className={styles.formGroup}>
            <label htmlFor="prompt" className={styles.label}>
              {t('logo_prompt_label')}
            </label>
            <textarea
              id="prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder={t('logo_prompt_placeholder')}
              className={styles.textarea}
              rows={4}
            />
          </div>

          <div className={styles.formGroup}>
            <label className={styles.label}>
              {t('logo_style_label')}
            </label>
            <div className={styles.styleGrid}>
              {styles_options.map((style) => (
                <button
                  key={style.value}
                  onClick={() => setSelectedStyle(style.value)}
                  className={`${styles.styleButton} ${selectedStyle === style.value ? styles.selected : ''}`}
                >
                  {style.label}
                </button>
              ))}
            </div>
          </div>

          <div className={styles.costInfo}>
            <p>{t('logo_cost')}</p>
          </div>

          <Button
            onClick={handleGenerate}
            disabled={isGenerating || !prompt.trim()}
            variant="primary"
            className={styles.generateButton}
          >
            {isGenerating ? t('logo_generating') : t('generate_logo')}
          </Button>

          {error && (
            <div className={styles.error}>
              {error}
            </div>
          )}

          {generatedImage && (
            <div className={styles.result}>
              <h3>{t('logo_generated')}</h3>
              <div className={styles.imageContainer}>
                <img src={generatedImage} alt="Generated Logo" className={styles.generatedImage} />
              </div>
              <div className={styles.resultActions}>
                <Button onClick={handleDownload} variant="success">
                  {t('download_logo')}
                </Button>
                <Button onClick={() => {
                  setGeneratedImage(null);
                  setPrompt('');
                }} variant="secondary">
                  {t('regenerate_logo')}
                </Button>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
} 