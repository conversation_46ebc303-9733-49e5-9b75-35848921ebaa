# Stripe Integration Setup

## Stripe Keys Explained

Stripe provides **two different keys** for different purposes:

### 🔐 **Secret Key** (`sk_test_...` / `sk_live_...`)
- **Server-side only** - Never expose to frontend
- Used for: Creating checkout sessions, processing payments, webhooks
- Added to: `.env.local` (server environment)

### 🌐 **Publishable Key** (`pk_test_...` / `pk_live_...`) 
- **Client-side safe** - Can be exposed to frontend
- Used for: Stripe Elements, client-side operations, loading Stripe.js
- Added to: `.env.local` (accessible to frontend via `NEXT_PUBLIC_`)

## Environment Variables Required

Add **both keys** to your `.env.local` file:

```bash
# Stripe Configuration - BOTH KEYS REQUIRED
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key                    # Server-side only
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key  # Client-side safe
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# App Configuration (for Stripe redirects)
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## Stripe Dashboard Setup

### 1. Create Stripe Account
- Go to [stripe.com](https://stripe.com) and create an account
- Go to Dashboard > Developers > API keys
- Copy **both** the Publishable key and Secret key

### 2. Create Webhook Endpoint
- Go to Dashboard > Developers > Webhooks
- Add endpoint: `https://yourdomain.com/api/stripe/webhook`
- Select these events:
  - `checkout.session.completed`
  - `payment_intent.succeeded` 
  - `payment_intent.payment_failed`
- Copy the webhook signing secret

### 3. Test the Integration
- Use Stripe test cards: `************** 4242`
- Monitor webhook events in Stripe Dashboard

## API Endpoints Created

### 1. Create Checkout Session
**POST** `/api/stripe/create-checkout-session`

```json
{
  "packageId": "uuid-of-token-package",
  "userId": "uuid-of-authenticated-user"
}
```

### 2. Get Token Packages
**GET** `/api/stripe/token-packages`

Returns available token packages for purchase.

### 3. Payment History
**GET** `/api/stripe/payment-history?userId=uuid`

Returns user's payment history.

### 4. Webhook Handler
**POST** `/api/stripe/webhook`

Handles Stripe webhook events (automatic).

## Frontend Integration 

### Using the Helper Functions

```typescript
import { createCheckoutSession, fetchTokenPackages } from '@/lib/stripe'

// Get available packages
const packages = await fetchTokenPackages()

// Purchase tokens
const handleBuyTokens = async (packageId: string) => {
  try {
    await createCheckoutSession(packageId, user.id)
    // User will be redirected to Stripe Checkout
  } catch (error) {
    console.error('Purchase failed:', error)
  }
}
```

### Manual Implementation

```typescript
// Buy tokens manually
const buyTokens = async (packageId: string) => {
  const response = await fetch('/api/stripe/create-checkout-session', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      packageId,
      userId: user.id
    })
  })
  
  const { url } = await response.json()
  window.location.href = url // Redirect to Stripe
}
```

## Key Usage Summary

| Key Type | Location | Usage |
|----------|----------|-------|
| **Secret Key** | Server APIs | Payment processing, webhooks |
| **Publishable Key** | Client-side | Loading Stripe.js, public operations |

## Database Tables

### token_packages
- Stores available token packages
- Admin can create/update packages
- Publicly readable

### payments  
- Tracks all payment transactions
- Links to users and token packages
- RLS enabled (users see only their payments)

## Payment Flow

1. User selects token package
2. Frontend calls `/api/stripe/create-checkout-session` (uses **secret key**)
3. User redirected to Stripe Checkout (uses **publishable key**)
4. Payment processed by Stripe
5. Webhook calls `/api/stripe/webhook` (uses **secret key**)
6. Tokens automatically added to user wallet
7. User redirected to success page

## Testing

Install Stripe CLI for local webhook testing:

```bash
npm install -g @stripe/stripe-cli
stripe login
stripe listen --forward-to localhost:3000/api/stripe/webhook
``` 