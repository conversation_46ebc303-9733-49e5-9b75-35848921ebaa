import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react';
import FreeGenerationsPage from '../page';
import { renderWithProviders } from '../../../../test-utils';

// Mock the fetch function
global.fetch = jest.fn();

const mockFetch = global.fetch as jest.Mock;

describe('FreeGenerationsPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock the initial fetch for the table
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ freeGenerations: [] }),
    } as Response);
  });

  it('renders the page and an empty table correctly', async () => {
    renderWithProviders(<FreeGenerationsPage />);
    
    expect(screen.getByRole('heading', { name: /Create Free Mortal Kombat Generation Link/i })).toBeInTheDocument();
    
    // Wait for the table to render with the empty message
    await waitFor(() => {
      expect(screen.getByText(/No free generations found./i)).toBeInTheDocument();
    });
  });

  it('calls the create link API and displays the link on success', async () => {
    const newLink = 'https://aivis.ro/mk-free/some-unique-id';
    // Mock the create link API call
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ freeLink: newLink }),
    } as Response);
    // Mock the subsequent fetch for the table refresh
    mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ freeGenerations: [{ id: 'some-unique-id', link: newLink, status: 'new', created_at: new Date().toISOString() }] }),
    } as Response);


    renderWithProviders(<FreeGenerationsPage />);

    const generateButton = screen.getByRole('button', { name: /Generate New Free Link/i });
    fireEvent.click(generateButton);

    // Check for loading state
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /Generating.../i })).toBeInTheDocument();
    });

    // Check that fetch was called for creation
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/admin/free-generations/create', {
        method: 'POST',
      });
    });

    // Check for success message and link
    await waitFor(() => {
      const linkInput = screen.getByRole('textbox');
      expect(linkInput).toHaveValue(newLink);
    });

    // Check that the table is updated
    await waitFor(() => {
        expect(screen.getByText(/some-unique-id/i)).toBeInTheDocument();
    });
  });

  it('displays an error message when the API call fails', async () => {
    // Mock the create link API call to fail
    mockFetch.mockResolvedValueOnce({
      ok: false,
      json: async () => ({ error: 'You are not authorized.' }),
    } as Response);

    renderWithProviders(<FreeGenerationsPage />);

    const generateButton = screen.getByRole('button', { name: /Generate New Free Link/i });
    fireEvent.click(generateButton);

    // Check for error message
    await waitFor(() => {
      expect(screen.getByText(/you are not authorized/i)).toBeInTheDocument();
    });
  });
});