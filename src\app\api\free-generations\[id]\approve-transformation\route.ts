import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: requestId } = await params;
    const body = await request.json();
    const { approved, reason } = body;

    // Get the generation request
    const { data: generationRequest, error: requestError } = await supabaseAdmin
      .from('web_generation_requests')
      .select('*')
      .eq('id', requestId)
      .is('user_id', null) // Ensure this is a free generation
      .single();

    if (requestError || !generationRequest) {
      return NextResponse.json(
        { error: 'Free generation request not found' },
        { status: 404 }
      );
    }

    // Verify the request is in the correct state
    if (generationRequest.status !== 'awaiting_approval') {
      return NextResponse.json(
        { error: 'Generation is not awaiting approval' },
        { status: 400 }
      );
    }

    if (approved) {
      // Update status to approved - ready for video generation
      const { error: updateError } = await supabaseAdmin
        .from('web_generation_requests')
        .update({
          status: 'approved',
          current_step: 3,
          approved_steps: [2], // Mark step 2 as approved
          awaiting_approval_for_step: null
        })
        .eq('id', requestId);

      if (updateError) {
        console.error('Failed to approve transformation:', updateError);
        return NextResponse.json(
          { error: 'Failed to approve transformation' },
          { status: 500 }
        );
      }

      // Get the updated request
      const { data: updatedRequest } = await supabaseAdmin
        .from('web_generation_requests')
        .select('*')
        .eq('id', requestId)
        .single();

      // Format response similar to regular web-generations
      const response = {
        request_id: updatedRequest.id,
        status: updatedRequest.status,
        current_step: updatedRequest.current_step,
        total_steps: updatedRequest.total_steps,
        transformation: {
          original_image_url: updatedRequest.input_data.original_image_url,
          transformed_image_url: updatedRequest.output_data.transformed_image_url,
          character_type: updatedRequest.input_data.settings?.character_type || updatedRequest.output_data.character,
          prompt_used: updatedRequest.output_data.character || ''
        }
      };

      return NextResponse.json(response);

    } else {
      // Update status to rejected
      const { error: updateError } = await supabaseAdmin
        .from('web_generation_requests')
        .update({
          status: 'rejected',
          current_step: 2,
          rejected_steps: [2], // Mark step 2 as rejected
          awaiting_approval_for_step: null,
          rejection_reason: reason || 'User rejected transformation'
        })
        .eq('id', requestId);

      if (updateError) {
        console.error('Failed to reject transformation:', updateError);
        return NextResponse.json(
          { error: 'Failed to reject transformation' },
          { status: 500 }
        );
      }

      // Get the updated request
      const { data: updatedRequest } = await supabaseAdmin
        .from('web_generation_requests')
        .select('*')
        .eq('id', requestId)
        .single();

      // Format response
      const response = {
        request_id: updatedRequest.id,
        status: updatedRequest.status,
        current_step: updatedRequest.current_step,
        total_steps: updatedRequest.total_steps,
        error_message: updatedRequest.rejection_reason
      };

      return NextResponse.json(response);
    }

  } catch (error) {
    console.error('Error approving/rejecting transformation:', error);
    return NextResponse.json(
      { error: 'Failed to process approval' },
      { status: 500 }
    );
  }
} 