export const logger = {
  error: (...args: any[]) => {
    if (process.env.NODE_ENV === 'test') {
      // Suppress error logs in test environment
      return;
    }
    // eslint-disable-next-line no-console
    console.error(...args);
  },
  log: (...args: any[]) => {
    if (process.env.NODE_ENV === 'test') {
      // Suppress logs in test environment
      return;
    }
    // eslint-disable-next-line no-console
    console.log(...args);
  },
  // Add warn/info/etc as needed
}; 