"use client";
import React, { useEffect, useState, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import styles from './page.module.css';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../store';
import { fetchUserProfile, logout } from '../store/userSlice';
import { MdOutlineTextSnippet, MdAutoAwesome, MdStars, MdSupportAgent } from 'react-icons/md';
import { MdChat, MdArrowForward } from 'react-icons/md';
import { FaTelegramPlane, FaWhatsapp } from 'react-icons/fa';
import { GiCutDiamond } from 'react-icons/gi';

// Mortal Kombat public generations gallery data (static for now, replace with dynamic fetch if needed)
const mkPublicGenerations = [
  {
    id: "a4c76409-c308-4101-900c-1522db74cda3",
    original_photo_url: "https://uhlmctpwxyubiyhakxqs.supabase.co/storage/v1/object/public/generated-images/mortal-kombat/08668752-8fbe-473c-8efb-e47eb4251665/1751392883341-ChatGPT_Image_Jun_5__2025__07_31_41_PM.png",
    generated_photo_url: "https://v3.fal.media/files/lion/XyY-jnd0h8wHkqqEOos-k_fbb4f4287fff499da878f40e11aebc3b.png",
    video_url: null
  },
  {
    id: "6026b4ab-33c9-449b-93db-661368a2b90e",
    original_photo_url: "https://uhlmctpwxyubiyhakxqs.supabase.co/storage/v1/object/public/generated-images/mortal-kombat/08668752-8fbe-473c-8efb-e47eb4251665/1751228415601-image.png",
    generated_photo_url: "https://v3.fal.media/files/tiger/oPF1hxL1rGUi3EAfqiPsB_982eff24fafe4490819f6df7c560bb96.png",
    video_url: "https://v3.fal.media/files/tiger/l0HvorW6yPSkJa1ECw4x1_output.mp4"
  },
  {
    id: "ddeb58b7-610b-45bb-a97b-bda73900b2c9",
    original_photo_url: "https://uhlmctpwxyubiyhakxqs.supabase.co/storage/v1/object/public/generated-images/mortal-kombat/08668752-8fbe-473c-8efb-e47eb4251665/1751117617293-img.jpg",
    generated_photo_url: "https://v3.fal.media/files/panda/04UCSTRHPIhwX-pOlNgNC_e51249facc264177b8f4673c1952b96f.png",
    video_url: "https://v3.fal.media/files/elephant/I3CnHTSQ_yeQ4USFOle88_output.mp4"
  },
  {
    id: "458f256e-9cf9-4c6c-9ca9-8b724f0714b1",
    original_photo_url: "https://uhlmctpwxyubiyhakxqs.supabase.co/storage/v1/object/public/generated-images/mortal-kombat/08668752-8fbe-473c-8efb-e47eb4251665/1749495564167-image.png",
    generated_photo_url: "https://v3.fal.media/files/kangaroo/ah3pdW3RLGMeyfUxnQ9Iv_be4373ec6f614361bfc619730db69a8a.png",
    video_url: "https://v3.fal.media/files/lion/cKi5xbln1UEeUbKh8idQp_output.mp4"
  },
  {
    id: "3daf195c-2d96-4b35-a124-cf5a20922c55",
    original_photo_url: "https://uhlmctpwxyubiyhakxqs.supabase.co/storage/v1/object/public/generated-images/mortal-kombat/08668752-8fbe-473c-8efb-e47eb4251665/1749480697262-Captur__de_ecran_2025-06-09_174634.png",
    generated_photo_url: "https://v3.fal.media/files/zebra/i9On0hm-fGH8eaEANVPLO_f640e4ed947148ec8420cd4ea871a31b.png",
    video_url: "https://v3.fal.media/files/kangaroo/AO_yV7Vj-VwZgO-tQe4_B_output.mp4"
  }
];

export default function Home() {
  const dispatch = useDispatch<AppDispatch>();
  const { user, loading, error } = useSelector((state: RootState) => state.user);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState<{ text: string; isError: boolean } | null>(null);
  const emailInputRef = useRef<HTMLInputElement>(null);
  const [localLoading, setLocalLoading] = useState(true);
  const hasAttemptedFetch = useRef(false);
  // Carousel state for MK gallery
  const [mkIndex, setMkIndex] = useState(0);
  const [fade, setFade] = useState(true);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalVideo, setModalVideo] = useState<string | null>(null);

  useEffect(() => {
    // Only fetch profile once when component mounts
    if (!hasAttemptedFetch.current) {
      hasAttemptedFetch.current = true;
      dispatch(fetchUserProfile());
    }
    
    // Update local loading state based on Redux state
    if (!loading) {
      setLocalLoading(false);
    }
  }, [dispatch, loading]);

  useEffect(() => {
    if (mkPublicGenerations.length < 2) return;
    const interval = setInterval(() => {
      setFade(false);
      setTimeout(() => {
        setMkIndex((prev) => (prev + 1) % mkPublicGenerations.length);
        setFade(true);
      }, 20); // faster fade out duration
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  // Prevent scrolling when mobile menu is open
  useEffect(() => {
    if (menuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [menuOpen]);

  const openModal = () => {
    setIsModalOpen(true);
    setSubmitMessage(null);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSubmitMessage(null);
  };

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!emailInputRef.current?.value) return;
    
    try {
      setSubmitting(true);
      setSubmitMessage(null);
      
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email: emailInputRef.current.value 
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'A apărut o eroare. Te rugăm să încerci din nou.');
      }
      
      setSubmitMessage({
        text: 'Mulțumim pentru abonare! Te vom notifica când platforma va fi disponibilă.',
        isError: false
      });
      
      // Reset the form
      e.currentTarget.reset();
      
      // Close modal after 3 seconds
      setTimeout(() => {
        closeModal();
      }, 3000);
      
    } catch (error: any) {
      setSubmitMessage({
        text: error.message || 'A apărut o eroare. Te rugăm să încerci din nou.',
        isError: true
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className={styles.container}>
      <header className={styles.header}>
        <div className={styles.logo}>
          <Image 
            src="/aivis-wide-small.png" 
            alt="Logo Aivis" 
            width={130} 
            height={40} 
          />
        </div>
        
        <button 
          className={`${styles.hamburger} ${menuOpen ? styles.active : ''}`} 
          onClick={toggleMenu}
          aria-label="Toggle navigation menu"
        >
          <span className={styles.hamburgerLine}></span>
          <span className={styles.hamburgerLine}></span>
          <span className={styles.hamburgerLine}></span>
        </button>
        
        <nav className={`${styles.nav} ${menuOpen ? styles.menuOpen : ''}`}>
          {localLoading ? null : user ? (
            <>
              <Link href="/dashboard" className={styles.navLink} onClick={() => setMenuOpen(false)}>
                Dashboard
              </Link>
              <button 
                onClick={() => {
                  setMenuOpen(false);
                  dispatch(logout());
                }} 
                className={styles.navButton}
              >
                Deconectare
              </button>
            </>
          ) : error ? (
            <>
              <span className={styles.errorText}>Eroare autentificare</span>
              <button 
                onClick={() => {
                  setMenuOpen(false);
                  dispatch(logout());
                }} 
                className={styles.navButton}
              >
                Deconectare
              </button>
            </>
          ) : (
            <>
              <Link href="/register-coming-soon" className={styles.navLink} onClick={() => setMenuOpen(false)}>
                Înregistrare
              </Link>
              <Link href="/login" className={styles.navLink} onClick={() => setMenuOpen(false)}>
                Autentificare
              </Link>
            </>
          )}
        </nav>
      </header>

      <main className={styles.main}>
        {/* Hero Section */}
        <section className={styles.hero}>
          <h1>Bine ai venit la Aivis</h1>
          <p>Platforma ta de creare a activelor vizuale cu inteligență artificială</p>
          <p className={styles.launchNotice}>Platforma noastră se află în dezvoltare. Abonează-te pentru a fi notificat când lansăm. Vei primi credite gratuite pentru a începe să folosești platforma, atunci cand va fi disponibila.</p>
          <div className={styles.ctaButtons}>
            <button onClick={openModal} className={styles.primaryButton}>
              Abonează-te
            </button>
            <Link href="/demo" className={styles.secondaryButton}>
              Vizualizează Demo
            </Link>
          </div>
        </section>

        {/* Modal */}
        {isModalOpen && (
          <div className={styles.modalOverlay} onClick={closeModal}>
            <div className={styles.modal} onClick={(e) => e.stopPropagation()}>
              <button className={styles.closeButton} onClick={closeModal}>×</button>
              <h2>Abonează-te pentru lansare</h2>
              <p>Primește notificări când platforma Aivis va fi disponibilă.</p>
              <form className={styles.subscribeForm} onSubmit={handleSubmit}>
                <div className={styles.inputGroup}>
                  <input 
                    type="email" 
                    placeholder="Adresa ta de email" 
                    required 
                    className={styles.emailInput}
                    ref={emailInputRef}
                    disabled={submitting}
                  />
                  <button 
                    type="submit" 
                    className={styles.subscribeButton}
                    disabled={submitting}
                  >
                    {submitting ? 'Se trimite...' : 'Abonează-te'}
                  </button>
                </div>
                {submitMessage && (
                  <p className={submitMessage.isError ? styles.errorMessage : styles.successMessage}>
                    {submitMessage.text}
                  </p>
                )}
                <p className={styles.formDisclaimer}>
                  Prin abonare, ești de acord să primești notificări despre lansare și actualizări. 
                  Nu facem spam și respectăm <Link href="/privacy">politica de confidențialitate</Link>.
                </p>
              </form>
            </div>
          </div>
        )}

        {/* Services Overview */}
        <section className={styles.services}>
          <h2>Serviciile Noastre</h2>
          
              {/* Mortal Kombat Photo/Video Generation Service */}
              <div className={styles.serviceSection}>
            <div className={styles.serviceImage}>
              {/* Carousel for before/after public generations */}
              {mkPublicGenerations.length > 0 ? (
                <div className={styles.mkGalleryContainer}>
                  <div className={styles.mkGalleryRow}>
                    <img
                      key={mkPublicGenerations[mkIndex].id + '-orig'}
                      src={mkPublicGenerations[mkIndex].original_photo_url}
                      alt="Original"
                      className={
                        styles.mkGalleryImage + ' ' +
                        styles.mkGalleryImageFade + ' ' +
                        (mkPublicGenerations[mkIndex].video_url ? styles.pointer : styles.default)
                      }
                      style={{ opacity: fade ? 1 : 0 }}
                      onClick={() => {
                        if (mkPublicGenerations[mkIndex].video_url) {
                          setModalVideo(mkPublicGenerations[mkIndex].video_url);
                          setModalOpen(true);
                        }
                      }}
                    />
                    <img
                      key={mkPublicGenerations[mkIndex].id + '-gen'}
                      src={mkPublicGenerations[mkIndex].generated_photo_url}
                      alt="Mortal Kombat"
                      className={
                        styles.mkGalleryImage + ' ' +
                        styles.mkGalleryImageFade + ' ' +
                        (mkPublicGenerations[mkIndex].video_url ? styles.pointer : styles.default)
                      }
                      style={{ opacity: fade ? 1 : 0 }}
                      onClick={() => {
                        if (mkPublicGenerations[mkIndex].video_url) {
                          setModalVideo(mkPublicGenerations[mkIndex].video_url);
                          setModalOpen(true);
                        }
                      }}
                    />
                    <span className={styles.mkGalleryArrow}>
                      <MdArrowForward size={48} />
                    </span>
                  </div>
                  <span className={styles.mkGalleryLabel}>Inainte / Dupa</span>
                </div>
              ) : (
                <span style={{ color: '#999' }}>No public examples yet</span>
              )}
            </div>
            <div className={styles.serviceContent}>
              <h3>Generare Foto/Video Mortal Kombat</h3>
              <p>Transformă-ți fotografia într-un personaj Mortal Kombat și creează videoclipuri spectaculoase de transformare cu ajutorul AI.</p>
              <ul>
                <li>Transformare foto în stil Mortal Kombat</li>
                <li>Generare video de transformare</li>
                <li>Mai multe personaje disponibile</li>
                <li>Rezultate rapide și de calitate</li>
              </ul>
              <Link href="/mortal-kombat-video-flow" className={styles.serviceLink}>
                Află Mai Multe
              </Link>
            </div>
          </div>

              {/* Video Creation Service */}
              <div className={`${styles.serviceSection} ${styles.serviceSectionReversed}`}>
            <div className={styles.serviceImage}>
              <Image 
                src="/svg/video-creation.svg" 
                alt="Creare Video"
                width={500}
                height={300}
                className={styles.serviceImg}
              />
            </div>
            <div className={styles.serviceContent}>
              <h3>Creare Video <span className={styles.comingSoonBadge}>În curând</span></h3>
              <p>Creează videoclipuri captivante din imaginile și ideile tale</p>
              <ul>
                <li>Conversie imagine-video</li>
                <li>Efecte dinamice</li>
                <li>Animații personalizate</li>
                <li>Multiple formate</li>
              </ul>
              <Link href="/services/video-creation" className={styles.serviceLink}>
                Află Mai Multe
              </Link>
            </div>
          </div>
          
          {/* Logo Creation Service */}
          <div className={styles.serviceSection}>
            <div className={styles.serviceImage}>
              <Image 
                src="/svg/logo-creation.svg" 
                alt="Creare Logo"
                width={500}
                height={300}
                className={styles.serviceImg}
              />
            </div>
            <div className={styles.serviceContent}>
              <h3>Creare Logo</h3>
              <p>Creează logo-uri profesioniste care reprezintă perfect identitatea brandului tău</p>
              <ul>
                <li>Generare logo cu IA</li>
                <li>Multiple opțiuni de stil</li>
                <li>Scheme de culori personalizate</li>
                <li>Export în rezoluție înaltă</li>
              </ul>
              <Link href="/services/logo-creation" className={styles.serviceLink}>
                Află Mai Multe
              </Link>
            </div>
          </div>

          {/* Image Generation Service */}
          <div className={`${styles.serviceSection} ${styles.serviceSectionReversed}`}>
            <div className={styles.serviceImage}>
              <Image 
                src="/svg/image-generation.svg" 
                alt="Generare Imagini"
                width={500}
                height={300}
                className={styles.serviceImg}
              />
            </div>
            <div className={styles.serviceContent}>
              <h3>Generare Imagini</h3>
              <p>Transformă-ți ideile în imagini uimitoare cu ajutorul IA</p>
              <ul>
                <li>Generare text-imagine</li>
                <li>Personalizare stil</li>
                <li>Multiple variații</li>
                <li>Drepturi de utilizare comercială</li>
              </ul>
              <Link href="/services/image-generation" className={styles.serviceLink}>
                Află Mai Multe
              </Link>
            </div>
          </div>

          {/* Image Editing Service */}
          <div className={styles.serviceSection}>
            <div className={styles.serviceImage}>
              <Image 
                src="/svg/image-editing.svg" 
                alt="Editare Imagini"
                width={500}
                height={300}
                className={styles.serviceImg}
              />
            </div>
            <div className={styles.serviceContent}>
              <h3>Editare Imagini</h3>
              <p>Îmbunătățește și modifică imaginile tale cu instrumente bazate pe IA</p>
              <ul>
                <li>Eliminare fundal</li>
                <li>Corecție culoare</li>
                <li>Eliminare obiecte</li>
                <li>Transfer stil</li>
              </ul>
              <Link href="/services/image-editing" className={styles.serviceLink}>
                Află Mai Multe
              </Link>
            </div>
          </div>

      

      
        </section>

        {/* How It Works */}
        <section className={styles.howItWorks}>
          <h2>Cum Funcționează</h2>
          <div className={styles.steps}>
            <div className={styles.step}>
              <div className={styles.stepNumber}>1</div>
              <span className={styles.stepIcon}><MdOutlineTextSnippet size={64} color="#0070f3" /></span>
              <h3>Descrie-ți Viziunea</h3>
              <p>Spune-ne ce vrei să creezi prin interfața noastră intuitivă de chat</p>
            </div>
            <div className={styles.step}>
              <div className={styles.stepNumber}>2</div>
              <span className={styles.stepIcon}><MdAutoAwesome size={64} color="#0070f3" /></span>
              <h3>Generare AI</h3>
              <p>AI-ul nostru procesează cererea ta și generează multiple opțiuni</p>
            </div>
            <div className={styles.step}>
              <div className={styles.stepNumber}>3</div>
              <span className={styles.stepIcon}><MdStars size={64} color="#0070f3" /></span>
              <h3>Sistem bazat pe puncte</h3>
              <p>Fiecare imagine sau video are un cost in puncte. Punctele se achizitionează inainte de a incepe generarea dar poti primi gratuit un numar de puncte la inceput.</p>
            </div>
            <div className={styles.step}>
              <div className={styles.stepNumber}>4</div>
              <span className={styles.stepIcon}><MdSupportAgent size={64} color="#0070f3" /></span>
              <h3>Asistenta umana</h3>
              <p>La nevoie, poti sa folosesti asistenta umana pentru a finaliza generarea</p>
            </div>
          </div>
        </section>

        {/* Features */}
        <section className={styles.features}>
          <h2>Caracteristici Platformă</h2>
          <div className={styles.featureGrid}>
            <div className={styles.feature}>
              <span className={styles.featureIcon}><MdChat size={64} color="#0070f3" /></span>
              <h3>Interfață Bazată pe Chat</h3>
              <p>Conversație naturală cu IA pentru a crea activele dorite</p>
            </div>
            <div className={styles.feature}>
              <span className={styles.featureIcon}><FaTelegramPlane size={64} color="#0070f3" /></span>
              <h3>Bot Telegram</h3>
              <p>Unele dintre activele create pot fi accesate si in Telegram</p>
            </div>
            <div className={styles.feature}>
              <span className={styles.featureIcon}><FaWhatsapp size={64} color="#0070f3" /></span>
              <h3>Bot WhatsApp</h3>
              <p>Unele dintre activele create pot fi accesate si in WhatsApp</p>
            </div>
            <div className={styles.feature}>
              <span className={styles.featureIcon}><GiCutDiamond size={64} color="#0070f3" /></span>
              <h3>Calitate Profesională</h3>
              <p>Rezultate în rezoluție înaltă gata pentru uz comercial</p>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className={styles.cta}>
          <h2>Gata să Creezi?</h2>
          <p>Platforma noastră se află în dezvoltare. Abonează-te pentru a fi notificat când lansăm.</p>
          
          <form className={styles.subscribeForm} onSubmit={async (e) => {
            e.preventDefault();
            
            const emailInput = e.currentTarget.querySelector('input[type="email"]') as HTMLInputElement;
            if (!emailInput?.value) return;
            
            try {
              const button = e.currentTarget.querySelector('button') as HTMLButtonElement;
              const statusElement = document.createElement('p');
              statusElement.className = styles.ctaStatusMessage;
              e.currentTarget.appendChild(statusElement);
              
              button.disabled = true;
              button.textContent = 'Se trimite...';
              
              const response = await fetch('/api/subscribe', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                  email: emailInput.value 
                }),
              });
              
              const data = await response.json();
              
              if (!response.ok) {
                throw new Error(data.error || 'A apărut o eroare. Te rugăm să încerci din nou.');
              }
              
              statusElement.textContent = 'Mulțumim pentru abonare! Te vom notifica când platforma va fi disponibilă.';
              statusElement.className = styles.successMessage;
              
              // Reset the form
              e.currentTarget.reset();
              
            } catch (error: any) {
              const statusElement = e.currentTarget.querySelector(`.${styles.ctaStatusMessage}`) || document.createElement('p');
              statusElement.textContent = error.message || 'A apărut o eroare. Te rugăm să încerci din nou.';
              statusElement.className = `${styles.errorMessage} ${styles.ctaStatusMessage}`;
              
              if (!e.currentTarget.contains(statusElement)) {
                e.currentTarget.appendChild(statusElement);
              }
            } finally {
              const button = e.currentTarget.querySelector('button') as HTMLButtonElement;
              if (button) {
                button.disabled = false;
                button.textContent = 'Abonează-te';
              }
            }
          }}>
            <div className={styles.inputGroup}>
              <input 
                type="email" 
                placeholder="Adresa ta de email" 
                required 
                className={styles.emailInput}
              />
              <button type="submit" className={styles.subscribeButton}>
                Abonează-te
              </button>
            </div>
            <p className={styles.formDisclaimer}>
              Prin abonare, ești de acord să primești notificări despre lansare și actualizări. 
              Nu facem spam și respectăm <Link href="/privacy">politica de confidențialitate</Link>.
            </p>
          </form>
        </section>
      </main>

      <footer className={styles.footer}>
        <div className={styles.footerContent}>
          <div className={styles.footerLogo}>
            <Image 
              src="/aivis-wide-small.png" 
              alt="Logo Aivis" 
              width={150} 
              height={42} 
            />
            <p>© 2024 Aivis. Toate drepturile rezervate.</p>
          </div>
          <div className={styles.footerLinks}>
            <div className={styles.footerLinkColumn}>
              <h4>Servicii</h4>
              <Link href="/services/logo-creation">Creare Logo</Link>
              <Link href="/services/image-generation">Generare Imagini</Link>
              <Link href="/services/image-editing">Editare Imagini</Link>
              <Link href="/services/video-creation">Creare Video</Link>
            </div>
            <div className={styles.footerLinkColumn}>
              <h4>Companie</h4>
              <Link href="/about">Despre Noi</Link>
              <Link href="/contact">Contact</Link>
              <Link href="/blog">Blog</Link>
            </div>
            <div className={styles.footerLinkColumn}>
              <h4>Legal</h4>
              <Link href="/terms">Termeni și Condiții</Link>
              <Link href="/privacy">Politica de Confidențialitate</Link>
            </div>
          </div>
        </div>
      </footer>

      {/* Modal for video */}
      {modalOpen && modalVideo && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          background: 'rgba(0,0,0,0.7)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}
          onClick={() => setModalOpen(false)}
        >
          <div style={{ position: 'relative', background: '#fff', borderRadius: 12, padding: 16 }} onClick={e => e.stopPropagation()}>
            <video src={modalVideo} controls autoPlay style={{ width: 480, height: 320, borderRadius: 8 }} />
            <button onClick={() => setModalOpen(false)} style={{ position: 'absolute', top: 8, right: 8, background: 'transparent', border: 'none', fontSize: 24, cursor: 'pointer' }}>&times;</button>
          </div>
        </div>
      )}
    </div>
  );
}
