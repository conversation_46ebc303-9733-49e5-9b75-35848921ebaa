"use client";

import React, { useState, useRef } from 'react';
import { Upload, Image as ImageIcon, Download, Gear, ArrowClockwise } from 'react-bootstrap-icons';
import styles from '../admin/admin.module.css';
import Header from '@/components/Header';
import { logger } from '@/utils/logger';

interface EditHistory {
  id: string;
  originalImage: string;
  editedImage: string;
  prompt: string;
  timestamp: Date;
  settings: EditSettings;
}

interface EditSettings {
  guidance_scale: number;
  safety_tolerance: string;
  output_format: 'jpeg' | 'png';
  aspect_ratio?: string;
  seed?: number;
}

const ImageEditor: React.FC = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null);
  const [prompt, setPrompt] = useState('');
  const [editedImage, setEditedImage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [editHistory, setEditHistory] = useState<EditHistory[]>([]);
  
  const [settings, setSettings] = useState<EditSettings>({
    guidance_scale: 3.5,
    safety_tolerance: "2",
    output_format: 'jpeg',
    aspect_ratio: undefined,
    seed: undefined
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  const handleFileSelect = (file: File) => {
    if (!file.type.startsWith('image/')) {
      setError('Please select a valid image file');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      setSelectedImage(result);
      setSelectedImageFile(file);
      setEditedImage(null);
      setError(null);
    };
    reader.readAsDataURL(file);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const uploadImageToFal = async (file: File): Promise<string> => {
    // Convert file to base64 for FAL upload
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result as string;
        // FAL expects a data URI format
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const handleEditImage = async () => {
    if (!selectedImageFile || !prompt.trim()) {
      setError('Please select an image and provide a prompt');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Upload image to get URL that FAL can access
      const imageDataUrl = await uploadImageToFal(selectedImageFile);
      
      const response = await fetch('/api/image-edit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt.trim(),
          image_url: imageDataUrl,
          ...settings
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to edit image');
      }

      if (result.success && result.data.images && result.data.images.length > 0) {
        const editedImageUrl = result.data.images[0].url;
        setEditedImage(editedImageUrl);
        
        // Add to history
        const historyEntry: EditHistory = {
          id: Date.now().toString(),
          originalImage: selectedImage!,
          editedImage: editedImageUrl,
          prompt: prompt.trim(),
          timestamp: new Date(),
          settings: { ...settings }
        };
        setEditHistory(prev => [historyEntry, ...prev]);
      } else {
        throw new Error('No edited image returned from API');
      }

    } catch (error: any) {
      logger.error('Error editing image:', error);
      setError(error.message || 'Failed to edit image');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = (imageUrl: string, filename: string) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const resetEditor = () => {
    setSelectedImage(null);
    setSelectedImageFile(null);
    setEditedImage(null);
    setPrompt('');
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <>
      <Header />
      <div className={styles.pageContainer}>
        <div className={styles.pageHeader}>
          <h2>Image Editor</h2>
          <p>Upload an image and use AI to edit it with text prompts</p>
        </div>

        <div className={styles.imageEditorContainer}>
          {/* Upload Section */}
          <div className={styles.uploadSection}>
            <div
              ref={dropZoneRef}
              className={`${styles.dropZone} ${selectedImage ? styles.hasImage : ''}`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onClick={() => fileInputRef.current?.click()}
            >
              {selectedImage ? (
                <div className={styles.selectedImageContainer}>
                  <img src={selectedImage} alt="Selected" className={styles.selectedImage} />
                  <div className={styles.imageOverlay}>
                    <Upload size={24} />
                    <span>Click or drag to replace</span>
                  </div>
                </div>
              ) : (
                <div className={styles.uploadPrompt}>
                  <ImageIcon size={48} />
                  <h3>Upload an Image</h3>
                  <p>Drag and drop an image here, or click to select</p>
                  <p className={styles.supportedFormats}>Supported: JPEG, PNG, WebP</p>
                </div>
              )}
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={(e) => e.target.files?.[0] && handleFileSelect(e.target.files[0])}
              style={{ display: 'none' }}
            />
          </div>

          {/* Edit Controls */}
          {selectedImage && (
            <div className={styles.editControls}>
              <div className={styles.promptSection}>
                <label htmlFor="prompt">Describe the changes you want to make:</label>
                <textarea
                  id="prompt"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="e.g., 'Add a rainbow in the sky', 'Change the color to blue', 'Remove the background'"
                  className={styles.promptInput}
                  rows={3}
                />
              </div>

              <div className={styles.controlButtons}>
                <button
                  onClick={() => setShowSettings(!showSettings)}
                  className={`${styles.settingsButton} ${showSettings ? styles.active : ''}`}
                >
                  <Gear size={16} />
                  Settings
                </button>
                
                <button
                  onClick={handleEditImage}
                  disabled={isLoading || !prompt.trim()}
                  className={styles.editButton}
                >
                  {isLoading ? (
                    <>
                      <ArrowClockwise size={16} className={styles.spinning} />
                      Processing...
                    </>
                  ) : (
                    'Edit Image'
                  )}
                </button>

                <button
                  onClick={resetEditor}
                  className={styles.resetButton}
                >
                  Reset
                </button>
              </div>

              {/* Settings Panel */}
              {showSettings && (
                <div className={styles.settingsPanel}>
                  <h4>Edit Settings</h4>
                  <div className={styles.settingsGrid}>
                    <div className={styles.settingItem}>
                      <label>Guidance Scale: {settings.guidance_scale}</label>
                      <input
                        type="range"
                        min="1"
                        max="10"
                        step="0.1"
                        value={settings.guidance_scale}
                        onChange={(e) => setSettings({...settings, guidance_scale: parseFloat(e.target.value)})}
                      />
                      <small>Higher values stick closer to the prompt</small>
                    </div>
                    
                    <div className={styles.settingItem}>
                      <label>Safety Tolerance</label>
                      <select
                        value={settings.safety_tolerance}
                        onChange={(e) => setSettings({...settings, safety_tolerance: e.target.value})}
                      >
                        <option value="1">1 - Most Strict</option>
                        <option value="2">2 - Strict</option>
                        <option value="3">3 - Moderate</option>
                        <option value="4">4 - Permissive</option>
                        <option value="5">5 - Most Permissive (NSFW Allowed)</option>
                        <option value="6">6 - Unrestricted</option>
                      </select>
                      <small>Higher values allow more mature/NSFW content</small>
                    </div>
                    
                    <div className={styles.settingItem}>
                      <label>Output Format</label>
                      <select
                        value={settings.output_format}
                        onChange={(e) => setSettings({...settings, output_format: e.target.value as 'jpeg' | 'png'})}
                      >
                        <option value="jpeg">JPEG</option>
                        <option value="png">PNG</option>
                      </select>
                    </div>
                    
                    <div className={styles.settingItem}>
                      <label>Seed (optional)</label>
                      <input
                        type="number"
                        value={settings.seed || ''}
                        onChange={(e) => setSettings({...settings, seed: e.target.value ? parseInt(e.target.value) : undefined})}
                        placeholder="Random if empty"
                      />
                      <small>Same seed produces same results</small>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className={styles.errorMessage}>
              <strong>Error:</strong> {error}
            </div>
          )}

          {/* Results Section */}
          {(selectedImage || editedImage) && (
            <div className={styles.resultsSection}>
              <h3>Results</h3>
              <div className={styles.imageComparison}>
                {selectedImage && (
                  <div className={styles.imagePanel}>
                    <h4>Original</h4>
                    <img src={selectedImage} alt="Original" className={styles.resultImage} />
                  </div>
                )}
                
                {editedImage && (
                  <div className={styles.imagePanel}>
                    <h4>Edited</h4>
                    <img src={editedImage} alt="Edited" className={styles.resultImage} />
                    <button
                      onClick={() => handleDownload(editedImage, `edited-image-${Date.now()}.${settings.output_format}`)}
                      className={styles.downloadButton}
                    >
                      <Download size={16} />
                      Download
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Edit History */}
          {editHistory.length > 0 && (
            <div className={styles.historySection}>
              <h3>Edit History</h3>
              <div className={styles.historyList}>
                {editHistory.map((entry) => (
                  <div key={entry.id} className={styles.historyItem}>
                    <div className={styles.historyImages}>
                      <img src={entry.originalImage} alt="Original" className={styles.historyThumbnail} />
                      <span>→</span>
                      <img src={entry.editedImage} alt="Edited" className={styles.historyThumbnail} />
                    </div>
                    <div className={styles.historyDetails}>
                      <p><strong>Prompt:</strong> {entry.prompt}</p>
                      <p><small>{entry.timestamp.toLocaleString()}</small></p>
                      <button
                        onClick={() => handleDownload(entry.editedImage, `edited-${entry.id}.${entry.settings.output_format}`)}
                        className={styles.downloadHistoryButton}
                      >
                        <Download size={12} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default ImageEditor; 