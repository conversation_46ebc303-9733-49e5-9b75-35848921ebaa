// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// -- This is a parent command --
Cypress.Commands.add('postToApi', (endpoint, requestBody, failOnStatusCode = false) => {
  return cy.request({
    method: 'POST',
    url: `/api/${endpoint}`,
    failOnStatusCode: failOnStatusCode,
    body: requestBody
  });
});

// -- Helper commands for each API endpoint --
Cypress.Commands.add('botReceiveMessage', (body, failOnStatusCode = false) => {
  // Use a fixture or custom body
  const requestBody = body || Cypress.env('mockBotData').receiveMessage;
  return cy.postToApi('bot-receive-message', requestBody, failOnStatusCode);
});

Cypress.Commands.add('botSendMessage', (body, failOnStatusCode = false) => {
  const requestBody = body || Cypress.env('mockBotData').sendMessage;
  return cy.postToApi('bot-send-message', requestBody, failOnStatusCode);
});

Cypress.Commands.add('botGenerateImage', (body, failOnStatusCode = false) => {
  const requestBody = body || Cypress.env('mockBotData').generateImage;
  return cy.postToApi('bot-generate-image', requestBody, failOnStatusCode);
});

Cypress.Commands.add('botUploadFile', (body, failOnStatusCode = false) => {
  const requestBody = body || Cypress.env('mockBotData').uploadFile;
  return cy.postToApi('bot-upload-file', requestBody, failOnStatusCode);
});

// Simplified cleanup command that uses direct request
Cypress.Commands.add('cleanupTestData', (options = {}) => {
  const defaultOptions = {
    tables: [], // Empty array means use default tables in the API
    conversationPrefix: 'test-', // Only clean conversations that start with this prefix
    olderThan: null // Clean all test data regardless of age
  };
  
  const cleanupOptions = { ...defaultOptions, ...options };
  const testApiKey = Cypress.env('TEST_API_KEY') || 'test-api-key';
  
  cy.log('Cleaning up test data...');
  
  // Make a direct request without chaining
  cy.request({
    method: 'POST',
    url: `/api/test-cleanup?key=${testApiKey}`,
    failOnStatusCode: false,
    body: cleanupOptions
  });
});

// -- Helper commands for getting data --
Cypress.Commands.add('getUserData', ({ userId, telegramId, whatsappId } = {}, failOnStatusCode = false) => {
  let url = '/api/user-data';
  
  if (userId) url += `?userId=${userId}`;
  else if (telegramId) url += `?telegram_id=${telegramId}`;
  else if (whatsappId) url += `?whatsapp_id=${whatsappId}`;
  
  const apiKey = Cypress.env('BOT_API_KEY') || 'n8ntest123';
  
  return cy.request({
    method: 'GET',
    url,
    failOnStatusCode,
    headers: { 'x-api-key': apiKey }
  });
});

// New command for complete file upload flow
Cypress.Commands.add('completeFileUploadFlow', ({ 
  conversationId, 
  externalIdentityId, 
  fileBase64, 
  fileUrl, 
  fileName = 'test-upload.jpg', 
  mimeType = 'image/jpeg', 
  textMessage = 'Here is a file upload',
  sender = 'user'
}, failOnStatusCode = false) => {
  
  // Default base64 image if none provided
  const defaultBase64 = 'iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAKElEQVQ4jWNgYGD4Twzu6FhFFGYYNXDUwGFpIAk2E4dHDRw1cDgaCAASFOffhEIO3gAAAABJRU5ErkJggg==';
  
  if (!conversationId || !externalIdentityId) {
    return cy.wrap({ 
      status: 400, 
      body: { 
        success: false, 
        error: 'Missing required fields: conversationId and externalIdentityId' 
      } 
    });
  }
  
  const uploadPayload = {
    conversationId,
    externalIdentityId,
    recordAsMessage: true,
    sender,
    textMessage,
    fileMetadata: {
      originalFilename: fileName,
      mimeType: mimeType,
      size: 1024
    }
  };
  
  // Either use file URL or base64 encoding
  if (fileUrl) {
    uploadPayload.fileUrl = fileUrl;
  } else {
    uploadPayload.fileBase64 = fileBase64 || defaultBase64;
  }
  
  return cy.botUploadFile(uploadPayload, failOnStatusCode);
});

// New command for complete image generation flow
Cypress.Commands.add('completeImageGenerationFlow', (messageText, imagePrompt, failOnStatusCode = false) => {
  // Default base64 image for testing
  const base64Image = 'iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAKElEQVQ4jWNgYGD4Twzu6FhFFGYYNXDUwGFpIAk2E4dHDRw1cDgaCAASFOffhEIO3gAAAABJRU5ErkJggg==';
  
  // Step 1: Bot receive message
  const messagePayload = {
    "messageId": "test-message-id-" + Date.now(),
    "firstName": "Test",
    "lastName": "User",
    "languageCode": "en",
    "chatId": "test-chat-id-" + Date.now(),
    "text": messageText || "Hello bot! Can you generate an image of a sunset?",
    "provider": "telegram",
    "providerUserId": "telegram-user-123456",
    "providerBotId": "telegram-bot-789012"
  };
  
  // Return a chain of promises to be resolved by the test
  return cy.botReceiveMessage(messagePayload, failOnStatusCode).then(receiveResponse => {
    expect(receiveResponse.status).to.eq(200);
    expect(receiveResponse.body).to.have.property('success', true);
    
    const conversationId = receiveResponse.body.data.conversationId;
    let walletId;
    
    // Use the provided wallet ID or fallback
    if (receiveResponse.body.data.walletId) {
      walletId = receiveResponse.body.data.walletId;
      return generateImage(conversationId, walletId);
    } else {
      // Use a known test wallet ID if none is returned
      walletId = "614324c6-11bc-4d32-8c0f-4ad28950ae30";
      return generateImage(conversationId, walletId);
    }
  });
  
  function generateImage(conversationId, walletId) {
    const imagePayload = {
      "conversationId": conversationId,
      "walletId": walletId,
      "tokenCost": 10,
      "operation": "generate",
      "promptText": imagePrompt || "A beautiful sunset over the ocean",
      "parameters": {
        "style": "realistic",
        "width": 1024,
        "height": 1024
      },
      "imageBase64": base64Image,
      "imageMimeType": "image/png"
    };
    
    return cy.botGenerateImage(imagePayload, failOnStatusCode).then(generateResponse => {
      if (generateResponse.status === 200 && generateResponse.body.data?.resultUrl) {
        // Step 3: Send a message with the generated image
        return sendMessage(conversationId, generateResponse.body.data.resultUrl, imagePrompt);
      }
      
      // Return the generate response even if it failed
      return generateResponse;
    });
  }
  
  function sendMessage(conversationId, imageUrl, promptText) {
    const messageContent = promptText 
      ? `Here's your image of ${promptText.toLowerCase()}`
      : "Here's your generated image!";
      
    const sendPayload = {
      "conversationId": conversationId,
      "text": messageContent,
      "imageUrl": imageUrl
    };
    
    return cy.botSendMessage(sendPayload, failOnStatusCode);
  }
}); 