"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from 'next/navigation';
import { supabase } from "@/lib/supabaseClient";
import { createCheckoutSession, fetchTokenPackages } from '@/lib/stripe';
import styles from "../page.module.css";
import Header from '@/components/Header';
import { logger } from '@/utils/logger';

interface TokenPackage {
  id: string;
  name: string;
  description: string;
  tokenAmount: number;
  priceCents: number;
  priceFormatted: string;
  currency: string;
  tokensPerDollar: number;
}

interface PaymentHistory {
  id: string;
  packageName: string;
  packageDescription: string;
  expectedTokens: number;
  tokensGranted: number;
  amountFormatted: string;
  status: string;
  paymentMethod: string;
  createdAt: string;
}

export default function TokensPage() {
  const [user, setUser] = useState<any>(null);
  const [profile, setProfile] = useState<any>(null);
  const [tokenPackages, setTokenPackages] = useState<TokenPackage[]>([]);
  const [paymentHistory, setPaymentHistory] = useState<PaymentHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState<string | null>(null);
  const [error, setError] = useState<string>("");
  const router = useRouter();

  useEffect(() => {
    const getUser = async () => {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error || !user) {
        router.push('/login');
        return;
      }
      
      setUser(user);
      await fetchUserProfile(user.id);
      await loadTokenPackages();
      await loadPaymentHistory(user.id);
      setLoading(false);
    };

    getUser();
  }, [router]);

  const fetchUserProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        logger.error('Error fetching profile:', error);
        return;
      }

      setProfile(data);
    } catch (err) {
      logger.error('Error:', err);
    }
  };

  const loadTokenPackages = async () => {
    try {
      const packages = await fetchTokenPackages();
      setTokenPackages(packages);
    } catch (err) {
      logger.error('Error loading token packages:', err);
      setError('Nu s-au putut încărca pachetele de credite');
    }
  };

  const loadPaymentHistory = async (userId: string) => {
    try {
      const response = await fetch(`/api/stripe/payment-history?userId=${userId}`);
      if (response.ok) {
        const data = await response.json();
        setPaymentHistory(data.payments);
      }
    } catch (err) {
      logger.error('Error loading payment history:', err);
    }
  };

  const handlePurchase = async (packageId: string) => {
    if (!user) return;
    
    setPurchasing(packageId);
    setError('');
    
    try {
      await createCheckoutSession(packageId, user.id);
      // User will be redirected to Stripe Checkout
    } catch (err: any) {
      logger.error('Purchase error:', err);
      setError(`Cumpărarea a eșuat: ${err.message}`);
      setPurchasing(null);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ro-RO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit'
    });
  };

  const getStatusInRomanian = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'completat';
      case 'pending':
        return 'în așteptare';
      case 'failed':
        return 'eșuat';
      case 'processing':
        return 'în procesare';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <Header />
        <div className={styles.loading}>Se încarcă...</div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <Header />
      
      <main className={styles.mainContainer}>
        <div className={styles.content}>
          <div className={styles.header}>
            <h1>Creditele Tale</h1>
            <div className={styles.tokenBalance}>
              Sold curent: <strong>{profile?.tokens || 0} credite</strong>
            </div>
          </div>

          {error && (
            <div className={styles.error}>
              {error}
            </div>
          )}

          {/* Token Packages */}
          <section className={styles.section}>
            <h2>Cumpără Mai Multe Credite</h2>
            <div className={styles.tokenPackages}>
              {tokenPackages.map((pkg) => (
                <div key={pkg.id} className={styles.tokenPackage}>
                  <div className={styles.packageHeader}>
                    <h3>{pkg.name}</h3>
                    <div className={styles.packagePrice}>{pkg.priceFormatted}</div>
                  </div>
                  
                  <div className={styles.packageDetails}>
                    <p className={styles.tokenAmount}>{pkg.tokenAmount} credite</p>
                    <p className={styles.packageDescription}>{pkg.description}</p>
                    <p className={styles.tokenValue}>
                      {pkg.tokensPerDollar} credite per dolar
                    </p>
                  </div>
                  
                  <button
                    className={`${styles.purchaseButton} ${purchasing === pkg.id ? styles.purchasing : ''}`}
                    onClick={() => handlePurchase(pkg.id)}
                    disabled={purchasing === pkg.id}
                  >
                    {purchasing === pkg.id ? 'Se procesează...' : 'Cumpără Acum'}
                  </button>
                </div>
              ))}
            </div>
          </section>

          {/* Payment History */}
          {paymentHistory.length > 0 && (
            <section className={styles.section}>
              <h2>Istoricul Plăților</h2>
              <div className={styles.paymentHistory}>
                {paymentHistory.map((payment) => (
                  <div key={payment.id} className={styles.paymentItem}>
                    <div className={styles.paymentDetails}>
                      <div className={styles.paymentPackage}>
                        <strong>{payment.packageName}</strong>
                        <span className={styles.paymentAmount}>{payment.amountFormatted}</span>
                      </div>
                      
                      <div className={styles.paymentMeta}>
                        <span>
                          {payment.tokensGranted || payment.expectedTokens} credite
                        </span>
                        <span className={`${styles.paymentStatus} ${styles[payment.status]}`}>
                          {getStatusInRomanian(payment.status)}
                        </span>
                        <span className={styles.paymentDate}>
                          {formatDate(payment.createdAt)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>
      </main>
    </div>
  );
} 