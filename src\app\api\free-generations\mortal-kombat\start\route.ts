import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';
import { randomUUID } from 'crypto';
import { fal } from '@fal-ai/client';
import { logger } from '@/utils/logger';

// Add Sharp for image processing
import sharp from 'sharp';

// Configure the FAL client with the API key
fal.config({
  credentials: process.env.FAL_API_KEY || ''
});

// Character transformation prompts - same as regular MK flow
const CHARACTER_PROMPTS = {
  scorpion: "Dress this person as <PERSON><PERSON><PERSON> from Mortal Kombat. Keep their face, body, and background unchanged but add: yellow ninja outfit with black trim, <PERSON><PERSON><PERSON>'s signature skull mask covering lower face, kunai weapon with chain rope. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle fire/ember particle effects around the person without changing the background. Photorealistic, professional costume photography.",
  'sub-zero': "Dress this person as Sub-Zero from Mortal Kombat. Keep their face, body, and background unchanged but add: blue ninja outfit with ice-blue accents, <PERSON><PERSON><PERSON>'s signature ice mask covering lower face, frost effects around hands only. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle ice particle effects around the person without changing the background. Photorealistic, professional costume photography.",
  raiden: "Dress this person as Raiden from Mortal Kombat. Keep their face, body, and background unchanged but add: white and blue traditional outfit with lightning patterns, Raiden's conical straw hat, subtle blue glow in eyes. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle electrical spark effects around the person without changing the background. Photorealistic, professional costume photography.",
  'liu-kang': "Dress this person as Liu Kang from Mortal Kombat. Keep their face, body, and background unchanged but add: red martial arts outfit with black trim and dragon motifs, red bandana/headband. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle fire particle effects around fists without changing the background. Photorealistic, professional costume photography.",
  kitana: "Dress this person as Kitana from Mortal Kombat. Keep their face, body, and background unchanged but add: royal blue and black outfit with elegant design, decorative mask covering lower face, steel fans as weapons. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle magical sparkle effects around the person without changing the background. Photorealistic, professional costume photography.",
  'johnny-cage': "Dress this person as Johnny Cage from Mortal Kombat. Keep their face, body, and background unchanged but add: designer sunglasses, black military pants, open vest showing chest, confident Hollywood action star look. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle green energy glow around hands without changing the background. Photorealistic, professional costume photography.",
  mileena: "Dress this person as Mileena from Mortal Kombat. Keep their face, body, and background unchanged but add: purple ninja outfit with revealing design, Mileena's signature sai weapons, pink/purple mask covering lower face. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle dark energy effects around the person without changing the background. Photorealistic, professional costume photography.",
  'kung-lao': "Dress this person as Kung Lao from Mortal Kombat. Keep their face, body, and background unchanged but add: traditional Shaolin monk robes in blue and white, Kung Lao's signature razor-rimmed hat. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle wind effects around the hat without changing the background. Photorealistic, professional costume photography.",
  sindel: "Dress this person as Sindel from Mortal Kombat. Keep their face, body, and background unchanged but add: regal purple and black outfit with silver accents, long flowing white hair, Sindel's signature spiked tiara, and mystical aura. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle purple energy waves around the person without changing the background. Photorealistic, professional costume photography.",
  fujin: "Dress this person as Fujin from Mortal Kombat. Keep their face, body, and background unchanged but add: wind god attire with white and blue robes, Fujin's signature long ponytail, silver armor, and a staff. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle wind swirl effects around the person without changing the background. Photorealistic, professional costume photography."
};

// Get selected image generator from database
async function getSelectedImageGenerator(supabase: any): Promise<string> {
  const { data, error } = await supabase
    .from('config_options')
    .select('value')
    .eq('key', 'mk_image_generator')
    .single();

  if (error || !data || !data.value || !data.value.selected) {
    logger.log('No image generator config found, defaulting to gpt-image-1');
    return 'gpt-image-1';
  }

  return data.value.selected;
}

// Transformation function for GPT-Image-1
async function transformWithGPTImage1(imageUrl: string, prompt: string) {
  const result = await fal.subscribe("fal-ai/gpt-image-1/edit-image/byok", {
    input: {
      image_urls: [imageUrl],
      prompt: prompt,
      image_size: "auto",
      num_images: 1,
      quality: "auto",
      openai_api_key: process.env.OPENAI_API_KEY
    },
    logs: true,
    onQueueUpdate: (update) => {
      if (update.status === "IN_PROGRESS") {
        logger.log("GPT-Image-1 Processing:", update.logs?.map((log) => log.message).join('\n'));
      }
    },
  });

  return {
    transformedImageUrl: result.data.images[0].url,
    requestId: result.requestId,
    originalResponse: result.data,
    model: 'gpt-image-1/edit-image/byok'
  };
}

// Transformation function for SeedEdit
async function transformWithSeedEdit(imageUrl: string, prompt: string) {
  const result = await fal.subscribe("fal-ai/bytedance/seededit/v3/edit-image", {
    input: {
      prompt: prompt,
      image_url: imageUrl,
      guidance_scale: 0.5
    },
    logs: true,
    onQueueUpdate: (update) => {
      if (update.status === "IN_PROGRESS") {
        logger.log("SeedEdit Processing:", update.logs?.map((log) => log.message).join('\n'));
      }
    },
  });

  return {
    transformedImageUrl: result.data.image.url,
    requestId: result.requestId,
    originalResponse: result.data,
    model: 'fal-ai/bytedance/seededit/v3/edit-image'
  };
}

// Transformation function for FLUX.1 Kontext [pro]
async function transformWithFluxKontext(imageUrl: string, prompt: string) {
  const result = await fal.subscribe("fal-ai/flux-pro/kontext", {
    input: {
      prompt: prompt,
      image_url: imageUrl,
      guidance_scale: 3.5,
      num_images: 1,
      safety_tolerance: "2",
      output_format: "jpeg"
    },
    logs: true,
    onQueueUpdate: (update) => {
      if (update.status === "IN_PROGRESS") {
        logger.log("FLUX Kontext Processing:", update.logs?.map((log) => log.message).join('\n'));
      }
    },
  });

  return {
    transformedImageUrl: result.data.images[0].url,
    requestId: result.requestId,
    originalResponse: result.data,
    model: 'fal-ai/flux-pro/kontext'
  };
}

// Utility function to map selectedImageGenerator to fal_model
function getFalModelFromGenerator(selectedImageGenerator: string): string {
  switch (selectedImageGenerator) {
    case 'gpt-image-1':
      return 'gpt-image-1/edit-image/byok';
    case 'fal-seededit-v3':
      return 'fal-ai/bytedance/seededit/v3/edit-image';
    case 'fal-flux-pro-kontext':
      return 'fal-ai/flux-pro/kontext';
    default:
      throw new Error(`Unknown image generator: ${selectedImageGenerator}`);
  }
}

// Utility function to normalize character names
function normalizeCharacterName(characterInput: string): string {
  if (!characterInput) return '';
  
  // Handle full character descriptions like "Scorpion - The vengeful specter with fire powers"
  const characterMap: { [key: string]: string } = {
    'Scorpion - The vengeful specter with fire powers': 'scorpion',
    'Sub-Zero - The ice-cold ninja warrior': 'sub-zero',
    'Raiden - The thunder god protector': 'raiden',
    'Liu Kang - The dragon fire martial artist': 'liu-kang',
    'Kitana - The royal assassin with steel fans': 'kitana',
    'Johnny Cage - The Hollywood action star': 'johnny-cage',
    'Mileena - The deadly pink assassin': 'mileena',
    'Kung Lao - The hat-throwing monk': 'kung-lao',
    'Sindel - The screaming queen': 'sindel',
    'Fujin - The wind god': 'fujin'
  };

  // First check if it's a full description
  if (characterMap[characterInput]) {
    return characterMap[characterInput];
  }

  // Otherwise, normalize to lowercase and handle common variations
  const normalized = characterInput.toLowerCase().trim();
  
  // Handle common variations
  if (normalized.includes('scorpion')) return 'scorpion';
  if (normalized.includes('sub-zero') || normalized.includes('subzero')) return 'sub-zero';
  if (normalized.includes('raiden')) return 'raiden';
  if (normalized.includes('liu kang') || normalized.includes('liu-kang')) return 'liu-kang';
  if (normalized.includes('kitana')) return 'kitana';
  if (normalized.includes('johnny cage') || normalized.includes('johnny-cage')) return 'johnny-cage';
  if (normalized.includes('mileena')) return 'mileena';
  if (normalized.includes('kung lao') || normalized.includes('kung-lao')) return 'kung-lao';
  if (normalized.includes('sindel')) return 'sindel';
  if (normalized.includes('fujin')) return 'fujin';

  return normalized;
}

async function validateVoucher(supabase: any, freeGenerationId: string) {
  logger.log('Validating voucher ID:', freeGenerationId);
  
  const { data: voucher, error } = await supabase
    .from('free_generations')
    .select('status')
    .eq('id', freeGenerationId)
    .single();

  logger.log('Voucher query result:', { voucher, error });

  if (error || !voucher) {
    logger.log('Voucher validation failed: Invalid or expired link');
    return { isValid: false, message: 'Invalid or expired link.' };
  }

  if (voucher.status !== 'new') {
    logger.log('Voucher validation failed: Status is', voucher.status, 'but expected "new"');
    return { isValid: false, message: 'This link has already been used.' };
  }

  logger.log('Voucher validation successful');
  return { isValid: true };
}

// Add image processing utility function
async function processImageForFAL(imageFile: File): Promise<{ buffer: Buffer; contentType: string; fileName: string }> {
  const MAX_DIMENSION = 3840; // Slightly under 4000 to be safe
  const QUALITY = 85; // JPEG quality when converting

  try {
    // Convert File to Buffer
    const arrayBuffer = await imageFile.arrayBuffer();
    const inputBuffer = Buffer.from(arrayBuffer);
    
    // Get image metadata
    const metadata = await sharp(inputBuffer).metadata();
    const { width = 0, height = 0, format } = metadata;
    
    logger.log(`Original image: ${width}x${height}, format: ${format}`);
    
    // Check if resizing is needed
    const needsResize = width > MAX_DIMENSION || height > MAX_DIMENSION;
    
    if (!needsResize) {
      // Image is within limits, return as-is but ensure it's in a supported format
      let processedBuffer = inputBuffer;
      let outputFormat = format;
      
      // Convert HEIC, WEBP, or other formats to JPEG for better compatibility
      if (format && !['jpeg', 'jpg', 'png'].includes(format.toLowerCase())) {
        processedBuffer = await sharp(inputBuffer)
          .rotate() // Auto-rotate based on EXIF orientation
          .jpeg({ quality: QUALITY })
          .toBuffer();
        outputFormat = 'jpeg';
        logger.log(`Converted ${format} to JPEG for compatibility`);
      } else {
        // Even for supported formats, apply EXIF rotation if needed
        const needsRotation = metadata.orientation && metadata.orientation !== 1;
        if (needsRotation) {
          processedBuffer = await sharp(inputBuffer)
            .rotate() // Auto-rotate based on EXIF orientation
            .toBuffer();
          logger.log(`Applied EXIF orientation correction`);
        }
      }
      
      return {
        buffer: processedBuffer,
        contentType: outputFormat === 'png' ? 'image/png' : 'image/jpeg',
        fileName: imageFile.name.replace(/\.[^/.]+$/, '') + (outputFormat === 'png' ? '.png' : '.jpg')
      };
    }
    
    // Calculate scale factor to keep largest dimension under MAX_DIMENSION
    const maxCurrentDimension = Math.max(width, height);
    const scaleFactor = Math.min(MAX_DIMENSION / maxCurrentDimension, 1.0); // Never scale up
    
    const newWidth = Math.round(width * scaleFactor);
    const newHeight = Math.round(height * scaleFactor);
    
    logger.log(`Resizing image from ${width}x${height} to ${newWidth}x${newHeight}`);
    
    // Resize image using proportional scaling (no distortion)
    const resizedBuffer = await sharp(inputBuffer)
      .rotate() // Auto-rotate based on EXIF orientation
      .resize(newWidth, newHeight, {
        fit: 'inside', // Scale to fit inside dimensions, maintaining aspect ratio
        kernel: sharp.kernel.lanczos3,
        withoutEnlargement: true
      })
      .jpeg({ 
        quality: QUALITY,
        progressive: true
      })
      .toBuffer();
    
    const originalSizeMB = (inputBuffer.length / 1024 / 1024).toFixed(2);
    const newSizeMB = (resizedBuffer.length / 1024 / 1024).toFixed(2);
    logger.log(`Image size reduced from ${originalSizeMB}MB to ${newSizeMB}MB`);
    
    return {
      buffer: resizedBuffer,
      contentType: 'image/jpeg',
      fileName: imageFile.name.replace(/\.[^/.]+$/, '') + '_resized.jpg'
    };
    
  } catch (error) {
    logger.error('Image processing error:', error);
    // Fallback: return original file as buffer
    const arrayBuffer = await imageFile.arrayBuffer();
    return {
      buffer: Buffer.from(arrayBuffer),
      contentType: imageFile.type,
      fileName: imageFile.name
    };
  }
}

export async function POST(req: Request) {
  // Create a Supabase client specifically for server-side use with service role key
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    process.env.SUPABASE_SERVICE_ROLE_KEY || ''
  );
  const formData = await req.formData();
  const image = formData.get('image') as File;
  const settingsString = formData.get('settings') as string;
  const freeGenerationId = formData.get('freeGenerationId') as string;

  logger.log('Raw settings string:', settingsString);
  
  // Parse settings safely, handling undefined/null case
  let settings = null;
  try {
    settings = settingsString ? JSON.parse(settingsString) : null;
  } catch (error) {
    logger.error('Error parsing settings:', error);
    return NextResponse.json({ error: 'Invalid settings format.' }, { status: 400 });
  }
  logger.log('Parsed settings:', settings);

  if (!image || !settings || !freeGenerationId) {
    return NextResponse.json({ error: 'Missing required fields.' }, { status: 400 });
  }

  // 1. Validate the voucher
  const { isValid, message } = await validateVoucher(supabase, freeGenerationId);
  if (!isValid) {
    return NextResponse.json({ error: message }, { status: 403 });
  }

  try {
    // 2. Check image dimensions first
    const imageArrayBuffer = await image.arrayBuffer();
    const imageBuffer = Buffer.from(imageArrayBuffer);
    const imageMetadata = await sharp(imageBuffer).metadata();
    const { width = 0, height = 0 } = imageMetadata;
    
    logger.log(`Free gen - Image dimensions: ${width}x${height}`);
    
    // If image is larger than 4000px, return resized preview for user confirmation
    const MAX_DIMENSION = 4000;
    const needsResizing = width > MAX_DIMENSION || height > MAX_DIMENSION;
    
    if (needsResizing) {
      try {
        logger.log(`Free gen - Image needs resizing from ${width}x${height}`);
        // Calculate scale factor to keep largest dimension under 3840px
        const maxDimension = Math.max(width, height);
        const scaleFactor = Math.min(3840 / maxDimension, 1.0); // Never scale up
        const newWidth = Math.round(width * scaleFactor);
        const newHeight = Math.round(height * scaleFactor);
        logger.log(`Free gen - Will scale by ${(scaleFactor * 100).toFixed(1)}% to: ${newWidth}x${newHeight}`);
        // Create resized preview using proportional scaling (no distortion)
        let resizedPreviewBuffer;
        try {
          resizedPreviewBuffer = await sharp(imageBuffer)
            .rotate() // Auto-rotate based on EXIF orientation
            .resize(newWidth, newHeight, {
              fit: 'inside', // Scale to fit inside dimensions, maintaining aspect ratio
              kernel: sharp.kernel.lanczos3,
              withoutEnlargement: true
            })
            .jpeg({ quality: 85, progressive: true })
            .toBuffer();
        } catch (sharpError) {
          logger.error('Error in sharp().toBuffer():', sharpError);
          return NextResponse.json(
            { error: 'Failed to process image buffer' },
            { status: 500 }
          );
        }
        // Upload original image for later use
        const originalFileName = `mortal-kombat/free/${randomUUID()}-original-${image.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
        const { data: originalUpload, error: originalUploadError } = await supabase.storage
          .from('generated-images')
          .upload(originalFileName, imageBuffer, {
            contentType: image.type,
            upsert: false
          });
        if (originalUploadError) {
          logger.error('Free gen - Original upload error:', originalUploadError);
          return NextResponse.json(
            { error: 'Failed to upload original image' },
            { status: 500 }
          );
        }
        // Upload resized preview
        const previewFileName = `mortal-kombat/free/${randomUUID()}-preview-${image.name.replace(/[^a-zA-Z0-9.-]/g, '_')}.jpg`;
        const { data: previewUpload, error: previewUploadError } = await supabase.storage
          .from('generated-images')
          .upload(previewFileName, resizedPreviewBuffer, {
            contentType: 'image/jpeg',
            upsert: false
          });
        if (previewUploadError) {
          logger.error('Free gen - Preview upload error:', previewUploadError);
          return NextResponse.json(
            { error: 'Failed to upload preview image' },
            { status: 500 }
          );
        }
        // Get public URLs only if uploads succeeded
        const { data: { publicUrl: originalUrl } } = supabase.storage
          .from('generated-images')
          .getPublicUrl(originalFileName);
        let previewUrl;
        if (!previewUploadError) {
          const { data: { publicUrl } } = supabase.storage
            .from('generated-images')
            .getPublicUrl(previewFileName);
          previewUrl = publicUrl;
        }
        // Return image resize confirmation needed
        return NextResponse.json({
          requiresResizeConfirmation: true,
          originalImage: {
            url: originalUrl,
            width: width,
            height: height,
            size: image.size
          },
          resizedPreview: {
            url: previewUrl,
            width: newWidth,
            height: newHeight,
            size: resizedPreviewBuffer.length
          },
          settings: settings,
          freeGenerationId: freeGenerationId,
          message: 'Image is larger than 4000 pixels and needs to be resized. Please review the preview.'
        });
      } catch (resizeError) {
        logger.error('Error during resize confirmation flow:', resizeError);
        if (resizeError instanceof Error && resizeError.stack) {
          logger.error('Error stack:', resizeError.stack);
        }
        logger.error('Debug info: imageMetadata:', typeof imageMetadata !== 'undefined' ? imageMetadata : 'undefined');
        return NextResponse.json(
          { error: 'Failed to upload preview image' },
          { status: 500 }
        );
      }
    }

    // Process and upload image to Supabase Storage for smaller images
    const { buffer, contentType, fileName: processedFileName } = await processImageForFAL(image);
    const uploadFileName = `mortal-kombat/free/${randomUUID()}-${processedFileName.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('generated-images')
      .upload(uploadFileName, buffer, {
        contentType: contentType,
        upsert: false
      });

    if (uploadError) {
      logger.error('Image upload error:', uploadError);
      return NextResponse.json({ error: 'Failed to upload image.' }, { status: 500 });
    }

    // Get public URL for the uploaded image
    const { data: { publicUrl: imageUrl } } = supabase.storage
      .from('generated-images')
      .getPublicUrl(uploadFileName);

    // 3. Get the selected image generator
    const selectedImageGenerator = await getSelectedImageGenerator(supabase);

    // 4. Create the Web Generation Request (for an anonymous user)
    const { data: webRequest, error: requestError } = await supabase
      .from('web_generation_requests')
      .insert({
        flow_type: 'mortal_kombat',
        status: 'pending',
        current_step: 1,
        total_steps: 4,
        input_data: {
          original_image_url: imageUrl,
          settings,
          selected_image_generator: selectedImageGenerator,
        },
        // user_id and wallet_id are NULL
      })
      .select()
      .single();

    if (requestError) {
      logger.error('Error creating web generation request:', requestError);
      return NextResponse.json({ error: 'Failed to initialize generation.' }, { status: 500 });
    }

    // 5. Mark the voucher as 'claimed' and link it to the request
    const { error: updateVoucherError } = await supabase
      .from('free_generations')
      .update({ status: 'claimed', web_generation_request_id: webRequest.id })
      .eq('id', freeGenerationId);

    if (updateVoucherError) {
        // If this fails, we should ideally roll back the generation request.
        // For now, we'll log the error and proceed.
      logger.error('Failed to update voucher status:', updateVoucherError);
    }

    // 6. Start the actual image transformation process
    try {
      // Update status to processing
      await supabase
        .from('web_generation_requests')
        .update({ status: 'processing', current_step: 2 })
        .eq('id', webRequest.id);

      // Get the character and prompt
      const rawCharacter = settings.character_type || settings.character;
      logger.log('Raw character from settings:', rawCharacter);
      logger.log('Settings object keys:', Object.keys(settings));
      logger.log('Full settings object:', JSON.stringify(settings, null, 2));
      
      if (!rawCharacter) {
        throw new Error(`Character not provided in settings. Available keys: ${Object.keys(settings).join(', ')}`);
      }
      
      const character = normalizeCharacterName(rawCharacter);
      logger.log('Normalized character:', character);
      logger.log('Available characters:', Object.keys(CHARACTER_PROMPTS));
      
      const prompt = CHARACTER_PROMPTS[character as keyof typeof CHARACTER_PROMPTS];
      
      if (!prompt) {
        throw new Error(`Unknown character: ${character} (from raw: ${rawCharacter}). Available characters: ${Object.keys(CHARACTER_PROMPTS).join(', ')}`);
      }

      logger.log(`Starting ${selectedImageGenerator} transformation for character: ${character}`);

      // Call the appropriate transformation function based on selected generator
      let transformationResult;
      switch (selectedImageGenerator) {
        case 'gpt-image-1':
          transformationResult = await transformWithGPTImage1(imageUrl, prompt);
          break;
        case 'fal-seededit-v3':
          transformationResult = await transformWithSeedEdit(imageUrl, prompt);
          break;
        case 'fal-flux-pro-kontext':
          transformationResult = await transformWithFluxKontext(imageUrl, prompt);
          break;
        default:
          throw new Error(`Unknown image generator: ${selectedImageGenerator}`);
      }

      // Update the request with the transformation result - require approval like paid flow
      const { error: updateError } = await supabase
        .from('web_generation_requests')
        .update({
          status: 'awaiting_approval',
          awaiting_approval_for_step: 2,
          current_step: 2,
          output_data: {
            transformed_image_url: transformationResult.transformedImageUrl,
            fal_request_id: transformationResult.requestId,
            model_used: transformationResult.model,
            character: character,
            original_response: transformationResult.originalResponse
          }
        })
        .eq('id', webRequest.id);

      if (updateError) {
        logger.error('Failed to update request with transformation result:', updateError);
        throw updateError;
      }

      logger.log(`Transformation completed successfully for request ${webRequest.id}`);

      // Return the updated request
      const { data: finalRequest } = await supabase
        .from('web_generation_requests')
        .select('*')
        .eq('id', webRequest.id)
        .single();

      return NextResponse.json(finalRequest);

    } catch (transformError) {
      logger.error('Error during transformation:', transformError);
      
      // Update the request with error status
      await supabase
        .from('web_generation_requests')
        .update({
          status: 'failed',
          error_message: transformError instanceof Error ? transformError.message : 'Unknown transformation error'
        })
        .eq('id', webRequest.id);

      return NextResponse.json({ error: 'Transformation failed.' }, { status: 500 });
    }

  } catch (error) {
    logger.error('Unexpected error in free generation start:', error);
    return NextResponse.json({ error: 'An unexpected error occurred.' }, { status: 500 });
  }
} 