import React, { useEffect, useState } from 'react';
import { logger } from '../utils/logger';

interface SignedUrlVideoProps {
  filePath: string;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
  controls?: boolean;
}

const SignedUrlVideo: React.FC<SignedUrlVideoProps> = ({ 
  filePath, 
  className, 
  style, 
  onClick,
  controls = true 
}) => {
  const [signedUrl, setSignedUrl] = useState<string | null>(null);

  useEffect(() => {
    async function fetchSignedUrl() {
      try {
        const res = await fetch("/api/fetch-signed-url", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ filePath }),
        });
        const data = await res.json();
        if (data.signedUrl) {
          setSignedUrl(data.signedUrl);
        } else {
          logger.error("Error fetching signed URL:", data.error);
        }
      } catch (error) {
        logger.error("Error fetching signed URL", error);
      }
    }
    fetchSignedUrl();
  }, [filePath]);

  if (!signedUrl) {
    return <div>Loading video...</div>;
  }
  
  return (
    <video 
      src={signedUrl} 
      className={className} 
      style={style} 
      onClick={onClick}
      controls={controls}
    />
  );
};

export default SignedUrlVideo; 