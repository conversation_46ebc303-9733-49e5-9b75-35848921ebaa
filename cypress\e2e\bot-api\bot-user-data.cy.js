describe('Bot User Data API', () => {
  const apiEndpoint = '/api/user-data';
  const apiKey = 'n8ntest123'; // This should match the BOT_API_KEY in your .env

  it('should return user data for a valid telegram_id', () => {
    // Using the test telegram ID we confirmed works
    const telegramId = 'telegram-test-id-123';
    
    cy.request({
      method: 'GET',
      url: `${apiEndpoint}?telegram_id=${telegramId}`,
      failOnStatusCode: false,
      headers: {
        'x-api-key': apiKey
      }
    }).then((response) => {
      cy.log(`Response status: ${response.status}`);
      cy.log(`Response body: ${JSON.stringify(response.body)}`);
      
      // Validate the successful response
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('exists', true);
      expect(response.body).to.have.property('external_identity');
      expect(response.body.external_identity).to.have.property('telegram_id', telegramId);
      expect(response.body).to.have.property('wallet');
      expect(response.body).to.have.property('stats');
    });
  });

  it('should return 404 for non-existent telegram_id', () => {
    // Using a telegram ID that doesn't exist
    const nonExistentTelegramId = 'non-existent-telegram-id-' + Date.now();
    
    cy.request({
      method: 'GET',
      url: `${apiEndpoint}?telegram_id=${nonExistentTelegramId}`,
      failOnStatusCode: false,
      headers: {
        'x-api-key': apiKey
      }
    }).then((response) => {
      cy.log(`Response status: ${response.status}`);
      cy.log(`Response body: ${JSON.stringify(response.body)}`);
      
      // Validate the not found response
      expect(response.status).to.eq(404);
      expect(response.body).to.have.property('exists', false);
      expect(response.body).to.have.property('message', 'User not found');
    });
  });

  it('should return 401 without API key', () => {
    const telegramId = 'telegram-test-id-123';
    
    cy.request({
      method: 'GET',
      url: `${apiEndpoint}?telegram_id=${telegramId}`,
      failOnStatusCode: false
      // No API key header
    }).then((response) => {
      cy.log(`Response status: ${response.status}`);
      cy.log(`Response body: ${JSON.stringify(response.body)}`);
      
      // Validate unauthorized response
      expect(response.status).to.eq(401);
      expect(response.body).to.have.property('error').that.includes('Unauthorized');
    });
  });

  it('should return 400 when no query parameter is provided', () => {
    cy.request({
      method: 'GET',
      url: apiEndpoint, // No query parameter
      failOnStatusCode: false,
      headers: {
        'x-api-key': apiKey
      }
    }).then((response) => {
      cy.log(`Response status: ${response.status}`);
      cy.log(`Response body: ${JSON.stringify(response.body)}`);
      
      // Validate bad request response
      expect(response.status).to.eq(400);
      expect(response.body).to.have.property('error').that.includes('telegram_id or whatsapp_id must be provided');
    });
  });

  it('should handle whatsapp_id parameter', () => {
    // Using a test whatsapp ID - this might return a 404 if it doesn't exist
    const whatsappId = 'whatsapp-test-id-123';
    
    cy.request({
      method: 'GET',
      url: `${apiEndpoint}?whatsapp_id=${whatsappId}`,
      failOnStatusCode: false,
      headers: {
        'x-api-key': apiKey
      }
    }).then((response) => {
      cy.log(`Response status: ${response.status}`);
      cy.log(`Response body: ${JSON.stringify(response.body)}`);
      
      // Just verify the endpoint handles the parameter (may return 404 if ID doesn't exist)
      expect(response.status).to.be.oneOf([200, 404]);
      
      if (response.status === 200) {
        expect(response.body).to.have.property('exists', true);
        expect(response.body.external_identity).to.have.property('whatsapp_id', whatsappId);
      } else {
        expect(response.body).to.have.property('exists', false);
      }
    });
  });
}); 