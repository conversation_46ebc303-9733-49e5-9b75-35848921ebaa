{"name": "podvideos", "version": "0.1.0", "private": true, "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "cypress:open": "cypress open", "cypress:run": "cypress run", "test:e2e": "start-server-and-test dev http://localhost:3000 cypress:run", "test:open": "start-server-and-test dev http://localhost:3000 cypress:open", "cleanup:db": "cypress run --spec cypress/e2e/bot-api/cleanup-test-data.cy.js"}, "dependencies": {"@fal-ai/client": "^1.5.0", "@mailchimp/mailchimp_marketing": "^3.0.80", "@reduxjs/toolkit": "^2.5.1", "@stripe/stripe-js": "^7.3.1", "@supabase/supabase-js": "^2.48.1", "@vercel/analytics": "^1.5.0", "base64-arraybuffer": "^1.0.2", "bootstrap": "^5.3.3", "date-fns": "^4.1.0", "dotenv": "^17.0.1", "next": "^15.1.6", "react": "^19.0.0", "react-bootstrap": "^2.10.9", "react-bootstrap-icons": "^1.11.5", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "sharp": "^0.34.2", "stripe": "^16.12.0"}, "devDependencies": {"@cypress/code-coverage": "^3.12.13", "@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "cypress": "^14.3.3", "eslint": "^9", "eslint-config-next": "15.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "jest-junit": "^16.0.0", "start-server-and-test": "^2.0.11", "ts-jest": "^29.3.4", "typescript": "^5", "undici": "^7.11.0"}}