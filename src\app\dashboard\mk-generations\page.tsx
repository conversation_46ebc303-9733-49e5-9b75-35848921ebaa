"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '../../../lib/supabaseClient';
import MortalKombatGeneration from '../MortalKombatGeneration';
import { useTranslation } from '@/hooks/useTranslation';
import Header from '@/components/Header';

export default function MkGenerationsPage() {
  const router = useRouter();
  const [generations, setGenerations] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const lang: 'ro' = 'ro';
  const { t } = useTranslation(lang);

  useEffect(() => {
    async function fetchGenerations() {
      setLoading(true);
      const { data, error } = await supabase
        .from('web_generation_requests')
        .select('*')
        .eq('flow_type', 'mortal_kombat')
        .order('created_at', { ascending: false });
      if (!error && data) setGenerations(data);
      setLoading(false);
    }
    fetchGenerations();
  }, []);

  return (
    <>
      <Header />
      <main style={{ maxWidth: 800, margin: '0 auto', padding: '32px 16px' }}>
        <h1 style={{ fontSize: 28, fontWeight: 700, marginBottom: 32 }}>{t('mortal_kombat_generations_section_title')}</h1>
        {loading ? (
          <div style={{ color: '#888', fontSize: 18 }}>Se încarcă...</div>
        ) : generations.length === 0 ? (
          <div style={{ color: '#888', fontSize: 18 }}>{t('mortal_kombat_generations_none')}</div>
        ) : (
          <div style={{ display: 'flex', flexDirection: 'column', gap: 20 }}>
            {generations.map(gen => (
              <MortalKombatGeneration
                key={gen.id}
                gen={gen}
                t={t}
                onViewDetails={(id) => router.push(`/mortal-kombat-video?id=${id}`)}
              />
            ))}
          </div>
        )}
      </main>
    </>
  );
} 