.videoGenCard {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  min-height: 80px;
}

.videoGenImage {
  width: 96px;
  height: 96px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #eee;
  margin-right: 16px;
}

.videoGenLeft {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.videoGenInfoBlock {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.videoGenPrompt {
  font-size: 18px;
  font-weight: 600;
  color: #222;
  margin-bottom: 4px;
}

.videoGenInfo {
  font-size: 16px;
  font-weight: 400;
  color: #666;
}

.videoGenButton {
  min-width: 120px;
}

@media (max-width: 600px) {
  .videoGenCard {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 16px;
  }
  
  .videoGenLeft {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .videoGenImage {
    margin: 0 0 8px 0;
    width: 120px;
    height: 120px;
  }
  
  .videoGenButton {
    margin-top: 12px;
    width: 100%;
    min-width: unset;
  }
}
