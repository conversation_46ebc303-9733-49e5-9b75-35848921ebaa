import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    // Fetch all web_images with user info
    const { data: images, error } = await supabaseAdmin
      .from('web_images')
      .select(`
        id,
        user_id,
        prompt_text,
        result_image_url,
        created_at,
        token_cost
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching web_images:', error);
      return NextResponse.json({ error: 'Failed to fetch images' }, { status: 500 });
    }

    // Fetch user profiles for email/role
    const userIds = [...new Set(images?.map(i => i.user_id) || [])].filter(Boolean);
    let profiles: any[] = [];
    if (userIds.length > 0) {
      const { data: profileData, error: profilesError } = await supabaseAdmin
        .from('profiles')
        .select('user_id, email, role')
        .in('user_id', userIds);
      if (profilesError) {
        console.error('Error fetching user profiles:', profilesError);
      } else {
        profiles = profileData;
      }
    }
    const profilesMap = new Map();
    profiles.forEach((profile: any) => {
      profilesMap.set(profile.user_id, profile);
    });

    const processedImages = images?.map((row: any) => {
      const userProfile = profilesMap.get(row.user_id);
      return {
        id: row.id,
        user_id: row.user_id,
        user_email: userProfile?.email || 'Unknown',
        user_role: userProfile?.role || 'user',
        prompt_text: row.prompt_text,
        status: 'completed',
        result_url: row.result_image_url,
        created_at: row.created_at,
        token_cost: row.token_cost || 0,
      };
    }) || [];

    return NextResponse.json({
      images: processedImages,
      total_count: processedImages.length,
      completed_count: processedImages.length,
      pending_count: 0,
      error_count: 0
    });
  } catch (error) {
    console.error('Error in admin web_images API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 