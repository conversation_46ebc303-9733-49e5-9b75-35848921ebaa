import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Use service role client for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    // Get user ID from the Authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('user_id', user.id)
      .single();

    if (profileError || profile?.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Fetch all web generation requests with user details
    const { data: requests, error } = await supabaseAdmin
      .from('web_generation_requests')
      .select(`
        *,
        web_generation_steps(step_number, status, created_at, completed_at, token_cost),
        web_videos(id, video_url)
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching admin video generations:', error);
      return NextResponse.json({ error: 'Failed to fetch requests' }, { status: 500 });
    }

    console.log(`Admin fetched ${requests?.length || 0} total video generations`);

    // Fetch user profiles (now includes email column)
    const userIds = [...new Set(requests?.map(r => r.user_id) || [])].filter(Boolean);
    const { data: profiles, error: profilesError } = await supabaseAdmin
      .from('profiles')
      .select('user_id, email, role')
      .in('user_id', userIds);

    if (profilesError) {
      console.error('Error fetching user profiles:', profilesError);
    }

    // Create a lookup map for profiles
    const profilesMap = new Map();
    profiles?.forEach(profile => {
      profilesMap.set(profile.user_id, profile);
    });

    // Process the data to get statistics for each request
    const processedRequests = requests?.map(request => {
      const steps = request.web_generation_steps || [];
      const completedSteps = steps.filter((step: any) => step.status === 'completed');
      const latestStep = completedSteps.length > 0 ? 
        Math.max(...completedSteps.map((step: any) => step.step_number)) : 0;
      
      const totalTokenCost = steps.reduce((sum: number, step: any) => sum + (step.token_cost || 0), 0);
      const userProfile = profilesMap.get(request.user_id);
      const userEmail = userProfile?.email;
      const userRole = userProfile?.role;
      
      return {
        id: request.id,
        user_id: request.user_id,
        user_email: userEmail || 'Unknown',
        user_role: userRole || 'user',
        flow_type: request.flow_type,
        status: request.status,
        character: request.input_data?.character_type || request.character,
        total_cost: parseFloat(request.actual_cost) || 0,
        estimated_cost: parseFloat(request.estimated_cost) || 0,
        total_tokens: totalTokenCost,
        created_at: request.created_at,
        updated_at: request.updated_at,
        current_step: request.current_step,
        total_steps: request.total_steps,
        latest_completed_step: latestStep,
        has_video: request.web_videos && request.web_videos.length > 0,
        video_url: request.web_videos?.[0]?.video_url,
        steps_count: steps.length,
        completed_steps_count: completedSteps.length
      };
    }) || [];

    return NextResponse.json({ 
      requests: processedRequests,
      total_count: processedRequests.length,
      completed_count: processedRequests.filter(r => r.status === 'completed').length,
      pending_count: processedRequests.filter(r => r.status === 'pending').length,
      error_count: processedRequests.filter(r => r.status === 'error').length
    });

  } catch (error) {
    console.error('Error in admin video generations API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 