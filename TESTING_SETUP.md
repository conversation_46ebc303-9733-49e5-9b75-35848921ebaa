# Testing Setup Guide

This document explains the unit testing setup for the Image Editor and other components.

## Testing Stack

- **Jest**: JavaScript testing framework
- **React Testing Library**: Simple and complete testing utilities for React
- **@testing-library/user-event**: Fire events the same way the user does
- **@testing-library/jest-dom**: Custom Jest matchers for DOM elements

## File Structure

```
src/
├── app/
│   ├── admin/
│   │   └── image-editor/
│   │       ├── __tests__/
│   │       │   └── page.test.tsx        # Image Editor component tests
│   │       └── page.tsx
│   └── api/
│       └── image-edit/
│           ├── __tests__/
│           │   └── route.test.ts        # API route tests
│           └── route.ts
├── test-utils/
│   └── index.tsx                        # Testing utilities and helpers
├── jest.config.js                       # Jest configuration
└── jest.setup.js                        # Global test setup
```

## Running Tests

### Basic Commands

```bash
# Run all tests once
npm test

# Run tests in watch mode (reruns on file changes)
npm run test:watch

# Run tests with coverage report
npm run test:coverage
```

### Advanced Commands

```bash
# Run specific test file
npm test -- page.test.tsx

# Run tests matching a pattern
npm test -- --testNamePattern="handles file upload"

# Run tests in verbose mode
npm test -- --verbose

# Update snapshots
npm test -- --updateSnapshot
```

## Test Configuration

### Jest Configuration (`jest.config.js`)

- Uses `next/jest` for Next.js compatibility
- Configured for `jsdom` environment (simulates browser)
- Module path mapping for `@/` imports
- Coverage thresholds set to 80%
- Excludes Cypress and build files

### Global Setup (`jest.setup.js`)

- Imports `@testing-library/jest-dom` for custom matchers
- Mocks Next.js components (`Image`, `Link`, `router`)
- Mocks Redux store with test data
- Mocks `fetch` API globally
- Mocks `FileReader` for file upload tests
- Mocks browser APIs (`URL`, `window.location`)

## Writing Tests

### Component Tests

```tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import Component from './Component'

describe('Component', () => {
  it('renders correctly', () => {
    render(<Component />)
    expect(screen.getByText('Hello World')).toBeInTheDocument()
  })

  it('handles user interactions', async () => {
    const user = userEvent.setup()
    render(<Component />)
    
    const button = screen.getByRole('button')
    await user.click(button)
    
    expect(screen.getByText('Clicked')).toBeInTheDocument()
  })
})
```

### API Route Tests

```ts
import { NextRequest } from 'next/server'
import { POST } from './route'

describe('/api/endpoint', () => {
  it('handles valid requests', async () => {
    const request = new NextRequest('http://localhost:3000/api/endpoint', {
      method: 'POST',
      body: JSON.stringify({ data: 'test' }),
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.success).toBe(true)
  })
})
```

## Testing Best Practices

### 1. **Test Structure (AAA Pattern)**
```tsx
it('should do something', async () => {
  // Arrange
  const user = userEvent.setup()
  render(<Component />)

  // Act
  await user.click(screen.getByRole('button'))

  // Assert
  expect(screen.getByText('Result')).toBeInTheDocument()
})
```

### 2. **Use Semantic Queries**
```tsx
// ✅ Good - accessible queries
screen.getByRole('button', { name: /submit/i })
screen.getByLabelText(/email address/i)
screen.getByText(/welcome/i)

// ❌ Avoid - implementation details
document.querySelector('.submit-button')
screen.getByTestId('submit-btn')
```

### 3. **Wait for Async Operations**
```tsx
// ✅ Good - wait for elements to appear
await waitFor(() => {
  expect(screen.getByText('Loading complete')).toBeInTheDocument()
})

// ✅ Good - wait for API calls
await waitFor(() => {
  expect(fetch).toHaveBeenCalledWith('/api/data')
})
```

### 4. **Mock External Dependencies**
```tsx
// Mock API calls
global.fetch = jest.fn().mockResolvedValue({
  ok: true,
  json: async () => ({ data: 'test' }),
})

// Mock complex components
jest.mock('./ComplexComponent', () => {
  return function MockComplexComponent() {
    return <div>Mocked Component</div>
  }
})
```

## Image Editor Tests

The Image Editor component tests cover:

### Core Functionality
- ✅ Renders interface correctly
- ✅ Handles file upload (click and drag & drop)
- ✅ Validates file types
- ✅ Shows/hides edit controls
- ✅ Validates required inputs
- ✅ Settings panel toggle

### API Integration
- ✅ Makes correct API calls
- ✅ Handles successful responses
- ✅ Handles API errors
- ✅ Shows loading states

### User Interactions
- ✅ File selection
- ✅ Text input
- ✅ Settings configuration
- ✅ Reset functionality
- ✅ Download actions

## API Route Tests

The API route tests cover:

### Validation
- ✅ Required parameter validation
- ✅ Environment configuration checks
- ✅ Input sanitization

### Integration
- ✅ FAL API integration
- ✅ Error handling
- ✅ Response formatting
- ✅ Optional parameter handling

## Coverage Requirements

Current coverage thresholds:
- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 80%
- **Statements**: 80%

To view coverage report:
```bash
npm run test:coverage
```

## Continuous Integration

Tests run automatically on:
- Pull requests
- Main branch pushes
- Release builds

## Troubleshooting

### Common Issues

#### Tests timeout
- Increase timeout: `jest.setTimeout(10000)`
- Check for missing `await` statements

#### Module not found
- Check `moduleNameMapping` in `jest.config.js`
- Verify import paths

#### DOM queries fail
- Use `screen.debug()` to see rendered output
- Check element accessibility

#### Mocks not working
- Ensure mocks are in `jest.setup.js`
- Clear mocks between tests with `jest.clearAllMocks()`

### Debugging Tests

```bash
# Run specific test with debugging
npm test -- --no-coverage --verbose page.test.tsx

# Add debugging output in tests
console.log(screen.debug()) // Shows current DOM
console.log(screen.logTestingPlaygroundURL()) // Interactive debugging
```

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
- [Next.js Testing](https://nextjs.org/docs/testing) 