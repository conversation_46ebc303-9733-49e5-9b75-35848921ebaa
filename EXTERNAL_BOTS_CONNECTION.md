A concise overview of the tables and core flows for handling Telegram and WhatsApp users with token-based actions.

Overview

We support two messaging bots—Telegram and WhatsApp—that allow users to generate or edit images using token-based actions. Each external user (by their unique provider ID) can receive a one‑time token airdrop and consume tokens without creating a full web account, while still recording their activity. Later, they can link their external identity to a web user account to consolidate their tokens and history.

1. Tables

external_identities

Stores a unique internal ID and, when available, links to a full web user account.

Holds optional external IDs for Telegram and WhatsApp.

Tracks whether the one-time token airdrop has been granted for each provider.

Records when the identity was first created.

wallets

Represents a token balance belonging to either a web user or an external identity.

Enforces that exactly one owner exists per wallet (either a user or an external identity).

Maintains the current balance and the timestamp of the last update.

2. Key Flows

First-time External User

Insert or locate the external_identities row for the given provider ID.

If the airdrop for that provider hasn’t been granted yet:

Create a new wallet with the initial token allocation (e.g., 100 tokens).

Log a token transaction for the airdrop event.

Mark the provider’s airdrop flag as granted.

Token Consumption

Use a single, atomic procedure to:

Lock the wallet record and verify sufficient balance.

Deduct the token cost.

Insert a corresponding transaction record.

Linking to Web Account

Update the external_identities entry to reference the newly registered web user.

Migrate the wallet to be owned by the web user instead of the external identity.

This approach scales easily: to add another messaging channel, add new columns for that provider’s ID and its airdrop flag, while reusing the existing wallet and transaction logic.

