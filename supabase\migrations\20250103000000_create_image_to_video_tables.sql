-- Create image_to_video_requests table
CREATE TABLE image_to_video_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  wallet_id UUID REFERENCES wallets(id) ON DELETE CASCADE,
  
  -- Request details
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  image_url TEXT NOT NULL,
  prompt TEXT NOT NULL,
  duration INTEGER DEFAULT 5 CHECK (duration IN (5, 8, 10)),
  resolution VARCHAR(10) DEFAULT '1080p' CHECK (resolution IN ('720p', '1080p')),
  
  -- Generated content
  video_url TEXT,
  fal_request_id TEXT,
  
  -- Cost tracking
  estimated_cost DECIMAL(10, 4) DEFAULT 0,
  actual_cost DECIMAL(10, 4) DEFAULT 0,
  token_cost INTEGER DEFAULT 0,
  
  -- Error handling
  error_message TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Create generated_videos table for tracking all video outputs
CREATE TABLE generated_videos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  wallet_id UUID REFERENCES wallets(id) ON DELETE CASCADE,
  request_id UUID REFERENCES image_to_video_requests(id) ON DELETE CASCADE,
  
  -- Video details
  video_url TEXT NOT NULL,
  duration INTEGER NOT NULL,
  resolution VARCHAR(10) NOT NULL,
  prompt_text TEXT NOT NULL,
  source_image_url TEXT NOT NULL,
  
  -- FAL API details
  fal_request_id TEXT,
  fal_model VARCHAR(100) NOT NULL DEFAULT 'fal-ai/bytedance/seedance/v1/pro/image-to-video',
  
  -- Cost tracking
  token_cost INTEGER DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_image_to_video_requests_user_id ON image_to_video_requests(user_id);
CREATE INDEX idx_image_to_video_requests_status ON image_to_video_requests(status);
CREATE INDEX idx_image_to_video_requests_created_at ON image_to_video_requests(created_at);

CREATE INDEX idx_generated_videos_user_id ON generated_videos(user_id);
CREATE INDEX idx_generated_videos_request_id ON generated_videos(request_id);
CREATE INDEX idx_generated_videos_created_at ON generated_videos(created_at);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_image_to_video_requests_updated_at
  BEFORE UPDATE ON image_to_video_requests
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_generated_videos_updated_at
  BEFORE UPDATE ON generated_videos
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE image_to_video_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE generated_videos ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own image to video requests"
  ON image_to_video_requests
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own image to video requests"
  ON image_to_video_requests
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own image to video requests"
  ON image_to_video_requests
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own generated videos"
  ON generated_videos
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own generated videos"
  ON generated_videos
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Admin policies (assuming there's an admin role)
CREATE POLICY "Admins can view all image to video requests"
  ON image_to_video_requests
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.user_metadata->>'role' = 'admin'
    )
  );

CREATE POLICY "Admins can view all generated videos"
  ON generated_videos
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.user_metadata->>'role' = 'admin'
    )
  );

-- Service role can do everything (for API operations)
CREATE POLICY "Service role can manage image to video requests"
  ON image_to_video_requests
  FOR ALL
  USING (current_setting('role') = 'service_role');

CREATE POLICY "Service role can manage generated videos"
  ON generated_videos
  FOR ALL
  USING (current_setting('role') = 'service_role');