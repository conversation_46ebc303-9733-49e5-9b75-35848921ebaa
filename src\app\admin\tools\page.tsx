"use client";

import React, { useEffect, useState } from "react";
import { supabase } from "@/lib/supabaseClient";
import styles from "../admin.module.css";

interface Tool {
  id: string;
  name: string;
  status: string;
  route: string;
}

export default function ToolManagementPage() {
  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState<string | null>(null);

  useEffect(() => {
    fetchTools();
  }, []);

  const fetchTools = async () => {
    setLoading(true);
    setError(null);
    const { data, error } = await supabase
      .from("tools")
      .select("id, name, status, route")
      .order("order_index", { ascending: true });
    if (error) setError(error.message);
    setTools(data || []);
    setLoading(false);
  };

  const toggleToolStatus = async (tool: Tool) => {
    setUpdating(tool.id);
    const newStatus = tool.status === "active" ? "inactive" : "active";
    const { error } = await supabase
      .from("tools")
      .update({ status: newStatus })
      .eq("id", tool.id);
    if (error) setError(error.message);
    await fetchTools();
    setUpdating(null);
  };

  return (
    <div>
      <h2>Telegram generations</h2>
      {loading ? (
        <div>Loading tools...</div>
      ) : error ? (
        <div style={{ color: "red" }}>{error}</div>
      ) : (
        <table className={styles.adminTable} style={{ minWidth: 400 }}>
          <thead>
            <tr>
              <th>Name</th>
              <th>Status</th>
              <th>Route</th>
              <th>Toggle</th>
            </tr>
          </thead>
          <tbody>
            {tools.map((tool) => (
              <tr key={tool.id}>
                <td>{tool.name}</td>
                <td>{tool.status === "active" ? "ACTIV" : "INACTIV"}</td>
                <td>{tool.route}</td>
                <td>
                  <button
                    onClick={() => toggleToolStatus(tool)}
                    disabled={updating === tool.id}
                    style={{
                      background: tool.status === "active" ? "#e74c3c" : "#27ae60",
                      color: "#fff",
                      border: "none",
                      borderRadius: 4,
                      padding: "6px 16px",
                      cursor: updating === tool.id ? "not-allowed" : "pointer"
                    }}
                  >
                    {updating === tool.id
                      ? "Updating..."
                      : tool.status === "active"
                      ? "Deactivate"
                      : "Activate"}
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
} 