.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: calc(100vh - 80px);
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.header h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.header p {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

.toolsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.toolCard {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.toolCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.toolHeader {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.toolIcon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 12px;
  flex-shrink: 0;
}

.toolInfo {
  flex: 1;
}

.toolInfo h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  color: #333;
}

.toolMeta {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.category {
  background: #e9ecef;
  color: #495057;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status.active {
  background: #d4edda;
  color: #155724;
}

.toolDescription {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.toolFeatures {
  margin-bottom: 2rem;
}

.toolFeatures h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: #333;
}

.toolFeatures ul {
  margin: 0;
  padding-left: 1.2rem;
  color: #666;
}

.toolFeatures li {
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.toolActions {
  border-top: 1px solid #e9ecef;
  padding-top: 1.5rem;
}

.launchButton {
  width: 100%;
  padding: 0.75rem;
  font-weight: 500;
  background: #007bff;
  border: none;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.launchButton:hover {
  background: #0056b3;
}

.comingSoon {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid #e9ecef;
}

.comingSoon h2 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #666;
  font-size: 1.5rem;
}

.futureTool {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #e9ecef;
}

.futureTool:last-child {
  margin-bottom: 0;
}

.futureTool .toolIcon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  background: #e9ecef;
}

.futureTool h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  color: #666;
}

.futureTool p {
  margin: 0;
  font-size: 0.9rem;
  color: #999;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .toolsGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .toolCard {
    padding: 1.5rem;
  }
  
  .toolHeader {
    flex-direction: column;
    text-align: center;
  }
  
  .toolIcon {
    margin: 0 auto;
  }
} 