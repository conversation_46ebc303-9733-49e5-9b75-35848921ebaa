import React from 'react';
import { fireEvent, waitFor, screen } from '@testing-library/react';
import Login from '../page';
import { renderWithProviders } from '../../../test-utils';

// Mock Supabase client
jest.mock('../../../lib/supabaseClient', () => ({
  supabase: {
    auth: {
      signInWithPassword: jest.fn(),
      signInWithOAuth: jest.fn(),
    },
  },
}));

// Import the mocked module to get typed access to the mocks
import { supabase } from '../../../lib/supabaseClient';

// Type assertion for better TypeScript support
const mockSignInWithPassword = supabase.auth.signInWithPassword as jest.MockedFunction<typeof supabase.auth.signInWithPassword>;
const mockSignInWithOAuth = supabase.auth.signInWithOAuth as jest.MockedFunction<typeof supabase.auth.signInWithOAuth>;

// Mock push function for router testing
const mockRouterPush = jest.fn();

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockRouterPush,
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  }),
  usePathname: () => '/login',
  useSearchParams: () => new URLSearchParams(),
}));

describe('Login Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders login form correctly', () => {
    renderWithProviders(<Login />);
    
    expect(screen.getByRole('heading', { name: /autentificare/i })).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/parolă/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /autentificare/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /continuă cu google/i })).toBeInTheDocument();
    expect(screen.getByText(/nu ai cont\?/i)).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /înregistrează-te aici/i })).toBeInTheDocument();
  });

  it('submits form with valid credentials successfully', async () => {
    const mockUserData = {
      user: { id: '123', email: '<EMAIL>' },
      session: { access_token: 'token123' },
    };

    mockSignInWithPassword.mockResolvedValueOnce({
      data: mockUserData,
      error: null,
    });

    renderWithProviders(<Login />);
    
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/parolă/i);
    const submitButton = screen.getByRole('button', { name: /autentificare/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    // Check loading state
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /se autentifică\.\.\./i })).toBeInTheDocument();
    });

    // Wait for successful login
    await waitFor(() => {
      expect(mockSignInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });

    await waitFor(() => {
      expect(screen.getByText(/login successful!/i)).toBeInTheDocument();
      expect(mockRouterPush).toHaveBeenCalledWith('/dashboard');
    });
  });

  it('displays error message when login fails', async () => {
    const mockError = { message: 'Invalid login credentials' };

    mockSignInWithPassword.mockResolvedValueOnce({
      data: null,
      error: mockError,
    });

    renderWithProviders(<Login />);
    
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/parolă/i);
    const submitButton = screen.getByRole('button', { name: /autentificare/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/error: invalid login credentials/i)).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /ai uitat parola\?/i })).toBeInTheDocument();
    });

    expect(mockRouterPush).not.toHaveBeenCalled();
  });

  it('clears error message when user starts typing', async () => {
    const mockError = { message: 'Invalid login credentials' };

    mockSignInWithPassword.mockResolvedValueOnce({
      data: null,
      error: mockError,
    });

    renderWithProviders(<Login />);
    
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/parolă/i);
    const submitButton = screen.getByRole('button', { name: /autentificare/i });

    // First, trigger an error
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/error: invalid login credentials/i)).toBeInTheDocument();
    });

    // Now type in the email field to clear the error
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    await waitFor(() => {
      expect(screen.queryByText(/error: invalid login credentials/i)).not.toBeInTheDocument();
    });
  });

  it('handles Google OAuth login successfully', async () => {
    mockSignInWithOAuth.mockResolvedValueOnce({
      data: { url: 'https://google.oauth.url' },
      error: null,
    });

    renderWithProviders(<Login />);
    
    const googleButton = screen.getByRole('button', { name: /continuă cu google/i });
    fireEvent.click(googleButton);

    await waitFor(() => {
      expect(mockSignInWithOAuth).toHaveBeenCalledWith({
        provider: 'google',
        options: {
          redirectTo: expect.stringContaining('/auth/callback'),
        },
      });
    });
  });

  it('handles Google OAuth login error', async () => {
    const mockError = { message: 'OAuth provider error' };

    mockSignInWithOAuth.mockResolvedValueOnce({
      data: null,
      error: mockError,
    });

    renderWithProviders(<Login />);
    
    const googleButton = screen.getByRole('button', { name: /continuă cu google/i });
    fireEvent.click(googleButton);

    await waitFor(() => {
      expect(screen.getByText(/error: oauth provider error/i)).toBeInTheDocument();
    });
  });

  it('disables form and buttons during submission', async () => {
    // Mock a slow response to test loading state
    mockSignInWithPassword.mockImplementationOnce(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({ data: null, error: { message: 'Test error' } }), 100)
      )
    );

    renderWithProviders(<Login />);
    
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/parolă/i);
    const submitButton = screen.getByRole('button', { name: /autentificare/i });
    const googleButton = screen.getByRole('button', { name: /continuă cu google/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    // Check that buttons are disabled during submission
    expect(screen.getByRole('button', { name: /se autentifică\.\.\./i })).toBeDisabled();
    expect(googleButton).toBeDisabled();

    // Wait for submission to complete
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /autentificare/i })).not.toBeDisabled();
    }, { timeout: 200 });
  });

  it('validates required form fields', () => {
    renderWithProviders(<Login />);
    
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/parolă/i);

    expect(emailInput).toBeRequired();
    expect(passwordInput).toBeRequired();
    expect(emailInput).toHaveAttribute('type', 'email');
    expect(passwordInput).toHaveAttribute('type', 'password');
  });

  it('contains correct placeholders and labels', () => {
    renderWithProviders(<Login />);
    
    expect(screen.getByPlaceholderText(/emailul tău/i)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/parola ta/i)).toBeInTheDocument();
  });

  it('navigates to register page when clicking register link', () => {
    renderWithProviders(<Login />);
    
    const registerLink = screen.getByRole('link', { name: /înregistrează-te aici/i });
    expect(registerLink).toHaveAttribute('href', '/register');
  });

  it('shows forgot password link when login error occurs', async () => {
    const mockError = { message: 'Invalid login credentials' };

    mockSignInWithPassword.mockResolvedValueOnce({
      data: null,
      error: mockError,
    });

    renderWithProviders(<Login />);
    
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/parolă/i);
    const submitButton = screen.getByRole('button', { name: /autentificare/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      const forgotPasswordLink = screen.getByRole('link', { name: /ai uitat parola\?/i });
      expect(forgotPasswordLink).toBeInTheDocument();
      expect(forgotPasswordLink).toHaveAttribute('href', '/forgotpassword');
    });
  });
});