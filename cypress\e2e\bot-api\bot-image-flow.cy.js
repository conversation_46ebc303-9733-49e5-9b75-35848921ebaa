describe('Bot Image Generation Flow', () => {
  const base64Image = 'iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAKElEQVQ4jWNgYGD4Twzu6FhFFGYYNXDUwGFpIAk2E4dHDRw1cDgaCAASFOffhEIO3gAAAABJRU5ErkJggg==';
  let conversationId;
  let walletId;
  let generatedImageUrl;
  
  // Test part 1: Bot receive message
  it('1. should handle bot receive message with image request', () => {
    // Create a message payload requesting an image
    const messagePayload = {
      "messageId": "test-message-id-" + Date.now(),
      "firstName": "Test",
      "lastName": "User",
      "languageCode": "en",
      "chatId": "test-chat-id-456",
      "text": "Hello bot! Can you generate an image of a sunset?",
      "provider": "telegram",
      "providerUserId": "telegram-user-123456",
      "providerBotId": "telegram-bot-789012"
    };
    
    cy.request({
      method: 'POST',
      url: '/api/bot-receive-message',
      failOnStatusCode: false,
      body: messagePayload
    }).then((receiveResponse) => {
      cy.log(`Bot Receive Message Response: ${JSON.stringify(receiveResponse.body)}`);
      
      // Validate the response
      expect(receiveResponse.status).to.eq(200);
      expect(receiveResponse.body).to.have.property('success', true);
      expect(receiveResponse.body.data).to.have.property('conversationId');
      expect(receiveResponse.body.data).to.have.property('messageId');
      
      // Store the conversationId for the next test
      conversationId = receiveResponse.body.data.conversationId;
      cy.log(`Conversation ID for next test: ${conversationId}`);
      
      // If available, store the walletId
      if (receiveResponse.body.data.walletId) {
        walletId = receiveResponse.body.data.walletId;
        cy.log(`Wallet ID from response: ${walletId}`);
      } else {
        // Use a fallback wallet ID for testing
        walletId = "614324c6-11bc-4d32-8c0f-4ad28950ae30";
        cy.log(`Using fallback wallet ID: ${walletId}`);
      }
    });
  });
  
  // Test part 2: Bot generate image
  it('2. should handle bot generate image with data from previous step', () => {
    // Skip this test if the previous one didn't set the conversation ID
    if (!conversationId) {
      cy.log('Skipping test because no conversation ID was set');
      return;
    }
    
    cy.log(`Using conversation ID from previous test: ${conversationId}`);
    cy.log(`Using wallet ID: ${walletId}`);
    
    const imagePayload = {
      "conversationId": conversationId,
      "walletId": walletId,
      "tokenCost": 10,
      "operation": "generate",
      "promptText": "A beautiful sunset over the ocean",
      "parameters": {
        "style": "realistic",
        "width": 1024,
        "height": 1024
      },
      "imageBase64": base64Image,
      "imageMimeType": "image/png"
    };
    
    cy.request({
      method: 'POST',
      url: '/api/bot-generate-image',
      failOnStatusCode: false,
      body: imagePayload
    }).then((generateResponse) => {
      cy.log(`Bot Generate Image Response: ${JSON.stringify(generateResponse.body)}`);
      
      // Validate image generation response
      if (generateResponse.status === 200) {
        expect(generateResponse.body).to.have.property('success', true);
        expect(generateResponse.body.data).to.have.property('imageId');
        expect(generateResponse.body.data).to.have.property('resultUrl');
        
        // Store the image URL for the next test
        generatedImageUrl = generateResponse.body.data.resultUrl;
        
        // Additional checks for successful image flow
        cy.log(`Generated image URL: ${generateResponse.body.data.resultUrl}`);
        cy.log(`Image ID: ${generateResponse.body.data.imageId}`);
        cy.log(`Remaining wallet balance: ${generateResponse.body.data.remainingBalance}`);
      } else {
        // Log the error but don't fail the test in testing environments
        cy.log(`Image generation returned status: ${generateResponse.status}`);
        cy.log(`Error message: ${generateResponse.body.error || 'No error message'}`);
      }
    });
  });
  
  // Test part 3: Bot send message with generated image
  it('3. should handle bot send message with the generated image', () => {
    // Skip this test if we don't have the required data
    if (!conversationId || !generatedImageUrl) {
      cy.log('Skipping test because conversation ID or image URL was not set');
      return;
    }
    
    cy.log(`Using conversation ID from previous test: ${conversationId}`);
    cy.log(`Using generated image URL: ${generatedImageUrl}`);
    
    const sendMessagePayload = {
      "conversationId": conversationId,
      "text": "Here's your generated image of a sunset!",
      "imageUrl": generatedImageUrl,
      "replyToMessageId": null  // Optional - can be used to specify which message this is replying to
    };
    
    cy.request({
      method: 'POST',
      url: '/api/bot-send-message',
      failOnStatusCode: false,
      body: sendMessagePayload
    }).then((sendResponse) => {
      cy.log(`Bot Send Message Response: ${JSON.stringify(sendResponse.body)}`);
      
      // Validate send message response
      expect(sendResponse.status).to.eq(200);
      expect(sendResponse.body).to.have.property('success', true);
      
      // Additional validations if available
      if (sendResponse.body.data) {
        expect(sendResponse.body.data).to.have.property('messageId');
        cy.log(`Sent message ID: ${sendResponse.body.data.messageId}`);
      }
    });
  });
  
  // Test part 4: Get user data
  it('4. should fetch user data for the telegram user', () => {
    // Skip this test if we don't have a conversation
    if (!conversationId) {
      cy.log('Skipping test because no conversation ID was set');
      return;
    }
    
    cy.log('Fetching user data for telegram-user-123456');
    
    cy.getUserData({ telegramId: 'telegram-user-123456' }).then((userDataResponse) => {
      cy.log(`User Data Response: ${JSON.stringify(userDataResponse.body)}`);
      
      // Validate user data response
      expect(userDataResponse.status).to.eq(200);
      expect(userDataResponse.body).to.have.property('exists', true);
      expect(userDataResponse.body).to.have.property('external_identity');
      expect(userDataResponse.body.external_identity).to.have.property('telegram_id', 'telegram-user-123456');
      
      // Verify wallet and stats data
      expect(userDataResponse.body).to.have.property('wallet');
      expect(userDataResponse.body.wallet).to.have.property('balance');
      expect(userDataResponse.body).to.have.property('stats');
      expect(userDataResponse.body.stats).to.have.property('images_generated');
      expect(userDataResponse.body.stats).to.have.property('conversation_count');
      
      cy.log(`User has ${userDataResponse.body.stats.images_generated} images generated`);
      cy.log(`User has ${userDataResponse.body.stats.conversation_count} conversations`);
      cy.log(`User wallet balance: ${userDataResponse.body.wallet.balance}`);
    });
  });
  
  // Standalone test for error handling
  it('should handle invalid wallet ID gracefully', () => {
    // First get a valid conversation ID
    cy.request({
      method: 'POST',
      url: '/api/bot-receive-message',
      failOnStatusCode: false,
      body: {
        "messageId": "test-invalid-" + Date.now(),
        "firstName": "Test",
        "lastName": "User",
        "languageCode": "en",
        "chatId": "test-chat-id-456",
        "text": "Hello bot!",
        "provider": "telegram",
        "providerUserId": "telegram-user-123456",
        "providerBotId": "telegram-bot-789012"
      }
    }).then((response) => {
      expect(response.status).to.eq(200);
      const conversationId = response.body.data.conversationId;
      
      // Now try with an invalid wallet ID
      cy.request({
        method: 'POST',
        url: '/api/bot-generate-image',
        failOnStatusCode: false,
        body: {
          "conversationId": conversationId,
          "walletId": "00000000-0000-0000-0000-000000000000", // Invalid wallet ID
          "tokenCost": 10,
          "operation": "generate",
          "promptText": "A beautiful sunset over the ocean"
        }
      }).then((errorResponse) => {
        cy.log(`Error response: ${JSON.stringify(errorResponse.body)}`);
        
        // Should return an error for invalid wallet
        expect(errorResponse.status).to.be.oneOf([400, 404, 500]);
        expect(errorResponse.body).to.have.property('success', false);
      });
    });
  });
}); 