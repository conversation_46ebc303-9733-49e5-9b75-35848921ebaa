const { defineConfig } = require('cypress')

module.exports = defineConfig({
  e2e: {
    baseUrl: 'http://localhost:3000',
    supportFile: 'cypress/support/e2e.js',
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    setupNodeEvents(on, config) {
      // implement node event listeners here
    },
  },
  env: {
    // Enable/disable test data cleanup after tests
    CLEANUP_AFTER_TESTS: true,
    // API key for the test cleanup endpoint
    TEST_API_KEY: 'test-api-key',
    
    // Mock data for testing
    mockBotData: {
      // Bot receive message data
      receiveMessage: {
        messageId: 'test-message-id',
        firstName: 'Test',
        lastName: 'User',
        languageCode: 'en',
        chatId: 'test-chat-id',
        text: 'Hello bot!',
        provider: 'telegram',
        providerUserId: 'test-provider-user-id',
        providerBotId: 'test-provider-bot-id'
      },
      // Bot send message data
      sendMessage: {
        conversationId: 'test-conversation-id',
        text: 'Hello from the bot!'
      },
      // Bot generate image data
      generateImage: {
        conversationId: 'test-conversation-id',
        walletId: 'test-wallet-id',
        tokenCost: 10,
        operation: 'generate',
        promptText: 'A beautiful sunset over the ocean',
        imageBase64: 'iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAKElEQVQ4jWNgYGD4Twzu6FhFFGYYNXDUwGFpIAk2E4dHDRw1cDgaCAASFOffhEIO3gAAAABJRU5ErkJggg==',
        imageMimeType: 'image/png',
        parameters: {
          style: 'realistic',
          width: 1024,
          height: 1024
        }
      },
      // Bot upload file data
      uploadFile: {
        conversationId: 'test-conversation-id',
        externalIdentityId: 'test-external-identity-id',
        fileUrl: 'https://example.com/test-image.jpg',
        fileMetadata: {
          originalFilename: 'test-image.jpg',
          mimeType: 'image/jpeg',
          size: 12345
        },
        recordAsMessage: true
      }
    }
  }
}) 