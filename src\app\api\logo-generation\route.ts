import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { fal } from '@fal-ai/client';

// Create a Supabase client specifically for server-side use with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

// Configure the FAL client with the API key
fal.config({
  credentials: process.env.FAL_API_KEY || ''
});

// Style prompts for logo generation
const STYLE_PROMPTS = {
  minimalist: "Create a minimalist logo with clean lines, simple shapes, and minimal colors. Focus on simplicity and elegance.",
  black_white: "Create a black and white logo with high contrast, bold lines, and classic design elements. Use only black and white colors.",
  modern: "Create a modern logo with contemporary design elements, sleek typography, and current design trends.",
  vintage: "Create a vintage-style logo with retro elements, classic typography, and nostalgic design aesthetic.",
  colorful: "Create a vibrant, colorful logo with multiple colors, dynamic elements, and eye-catching design.",
  corporate: "Create a professional corporate logo with clean typography, trustworthy design elements, and business-appropriate styling."
};

// Function to download image from URL and upload to Supabase Storage
async function downloadAndUploadToStorage(imageUrl: string, userId: string, prompt: string): Promise<string> {
  try {
    // Download the image from FAL AI
    console.log('Downloading image from FAL AI:', imageUrl);
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
    }
    
    const imageBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(imageBuffer);
    
    // Get content type from response or default to PNG
    const contentType = response.headers.get('content-type') || 'image/png';
    
    // Create a unique filename
    const timestamp = Date.now();
    const sanitizedPrompt = prompt.slice(0, 50).replace(/[^a-zA-Z0-9]/g, '_');
    const extension = contentType.includes('png') ? 'png' : 'jpg';
    const fileName = `logos/${userId}/${timestamp}-${sanitizedPrompt}.${extension}`;
    
    // Upload to Supabase Storage
    console.log('Uploading to Supabase Storage:', fileName);
    const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
      .from('generated-images')
      .upload(fileName, buffer, {
        contentType: contentType,
        upsert: false
      });

    if (uploadError) {
      console.error('Supabase Storage upload error:', uploadError);
      throw new Error(`Failed to upload to storage: ${uploadError.message}`);
    }

    // Get public URL for the uploaded image
    const { data: { publicUrl } } = supabaseAdmin.storage
      .from('generated-images')
      .getPublicUrl(fileName);

    console.log('Successfully uploaded logo to Supabase Storage:', publicUrl);
    return publicUrl;

  } catch (error) {
    console.error('Error downloading and uploading image:', error);
    throw error;
  }
}

// Logo generation function using GPT-Image-1 text-to-image
async function generateLogoWithGPTImage1(prompt: string, style: string) {
  const fullPrompt = `${prompt}. ${STYLE_PROMPTS[style as keyof typeof STYLE_PROMPTS] || STYLE_PROMPTS.minimalist}. Professional logo design, transparent background, high quality, vector-style.`;
  
  const result = await fal.subscribe("fal-ai/gpt-image-1/text-to-image/byok", {
    input: {
      prompt: fullPrompt,
      image_size: "1024x1024",
      num_images: 1,
      quality: "high",
      openai_api_key: process.env.OPENAI_API_KEY
    },
    logs: true,
    onQueueUpdate: (update) => {
      if (update.status === "IN_PROGRESS") {
        console.log("GPT-Image-1 Processing:", update.logs?.map((log) => log.message).join('\n'));
      }
    },
  });

  return {
    imageUrl: result.data.images[0].url,
    requestId: result.requestId,
    originalResponse: result.data,
    model: 'gpt-image-1/text-to-image/byok',
    tokenCost: 15 // Cost in tokens
  };
}

export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    const token = authHeader.split(' ')[1];

    // Verify the token with Supabase
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    // Parse request body
    const { prompt, style } = await request.json();

    if (!prompt || !prompt.trim()) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    if (!style || !STYLE_PROMPTS[style as keyof typeof STYLE_PROMPTS]) {
      return NextResponse.json(
        { error: 'Valid style is required' },
        { status: 400 }
      );
    }

    // Check required environment variables
    if (!process.env.FAL_API_KEY) {
      return NextResponse.json(
        { error: 'FAL API key not configured' },
        { status: 500 }
      );
    }

    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json(
        { error: 'OpenAI API key not configured' },
        { status: 500 }
      );
    }

    // Generate logo using FAL AI
    console.log('Generating logo with FAL AI...');
    const result = await generateLogoWithGPTImage1(prompt, style);

    // Download image from FAL AI and upload to Supabase Storage
    console.log('Downloading and uploading logo to Supabase Storage...');
    const storedImageUrl = await downloadAndUploadToStorage(result.imageUrl, user.id, prompt.trim());

    // Save logo generation to database with Supabase Storage URL
    const { data: logoGeneration, error: insertError } = await supabaseAdmin
      .from('logo_generations')
      .insert({
        user_id: user.id,
        prompt: prompt.trim(),
        style: style,
        image_url: storedImageUrl, // Use Supabase Storage URL instead of FAL AI URL
        fal_request_id: result.requestId,
        model_used: result.model,
        token_cost: result.tokenCost,
        status: 'completed',
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (insertError) {
      console.error('Failed to save logo generation:', insertError);
      return NextResponse.json(
        { error: 'Failed to save logo generation' },
        { status: 500 }
      );
    }

    // Return success response
    return NextResponse.json({
      success: true,
      logoGeneration: logoGeneration,
      imageUrl: storedImageUrl, // Return Supabase Storage URL
      tokenCost: result.tokenCost
    });

  } catch (error) {
    console.error('Logo generation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 