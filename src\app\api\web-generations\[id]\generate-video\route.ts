import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { fal } from '@fal-ai/client';

// Create a Supabase client specifically for server-side use with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

// Configure the FAL client with the API key
fal.config({
  credentials: process.env.FAL_API_KEY || ''
});

// Video cost mapping (in tokens) - 35 tokens for all resolutions to total 40 tokens with approval
const VIDEO_COSTS = {
  '360p': 35, // $0.35 = 35 tokens
  '540p': 35, // $0.35 = 35 tokens
  '720p': 35, // $0.35 = 35 tokens
  '1080p': 35 // $0.35 = 35 tokens
};

interface VideoGenerationRequest {
  custom_video_prompt?: string;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: requestId } = await params;
    const body: VideoGenerationRequest = await request.json();

    // Get the generation request first to check if it's a free one
    const { data: generationRequest, error: requestError } = await supabaseAdmin
      .from('web_generation_requests')
      .select('*')
      .eq('id', requestId)
      .single();

    if (requestError || !generationRequest) {
      return NextResponse.json(
        { error: 'Generation request not found' },
        { status: 404 }
      );
    }

    let userId: string | null = null;
    let wallet: any = null;

    // If the request has a user_id, we must authenticate the user and check their wallet
    if (generationRequest.user_id) {
        const authHeader = request.headers.get('authorization');
        const token = authHeader?.replace('Bearer ', '') || 
                      request.cookies.get('supabase-auth-token')?.value;

        if (!token) {
          return NextResponse.json(
            { error: 'Authentication required for this request' },
            { status: 401 }
          );
        }

        const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
        
        if (authError || !user || user.id !== generationRequest.user_id) {
          return NextResponse.json(
            { error: 'Invalid or unauthorized user' },
            { status: 403 }
          );
        }
        userId = user.id;

        // Check wallet balance
        const { data: userWallet, error: walletError } = await supabaseAdmin
          .from('wallets')
          .select('*')
          .eq('id', generationRequest.wallet_id)
          .single();

        if (walletError || !userWallet) {
          return NextResponse.json(
            { error: 'Wallet not found' },
            { status: 404 }
          );
        }
        wallet = userWallet;
    }


    // Validate request state (updated for new flow structure)
    if (generationRequest.status !== 'approved' || generationRequest.current_step !== 2) {
      return NextResponse.json(
        { error: 'Request must be approved before video generation' },
        { status: 400 }
      );
    }

    // Check if transformed image exists
    if (!generationRequest.output_data?.transformed_image_url) {
      return NextResponse.json(
        { error: 'Transformed image not found' },
        { status: 400 }
      );
    }

    // Check required environment variables
    if (!process.env.FAL_API_KEY) {
      return NextResponse.json(
        { error: 'FAL API key not configured' },
        { status: 500 }
      );
    }

    // Calculate video cost
    const resolution = generationRequest.input_data.video_settings.resolution;
    const videoCost = VIDEO_COSTS[resolution as keyof typeof VIDEO_COSTS];

    // For authenticated users, check balance and deduct cost
    if (userId && wallet) {
        if (wallet.balance < videoCost) {
          return NextResponse.json(
            { error: 'Insufficient balance for video generation', required: videoCost, available: wallet.balance },
            { status: 402 }
          );
        }

        // Deduct video cost from wallet
        await supabaseAdmin
          .from('wallets')
          .update({ 
            balance: wallet.balance - videoCost,
            updated_at: new Date().toISOString()
          })
          .eq('id', wallet.id);

        // Create token transaction record
        await supabaseAdmin
          .from('token_transactions')
          .insert({
            wallet_id: wallet.id,
            amount: -videoCost,
            description: `Mortal Kombat video generation - ${resolution}`,
            transaction_type: 'consumption'
          });
    }

    // Update request status to in_progress (updated step number)
    await supabaseAdmin
      .from('web_generation_requests')
      .update({ 
        status: 'in_progress',
        current_step: 3, // Updated: now step 3 instead of 4
        actual_cost: (generationRequest.actual_cost || 0) + (videoCost / 100),
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId);

    // Create video generation step
    const videoPrompt = body.custom_video_prompt || 
      `Seamless transition from normal person to ${generationRequest.input_data.character_type} from Mortal Kombat with dramatic transformation effects, mystical energy, and fighting stance`;

    const { data: stepData, error: stepError } = await supabaseAdmin
      .from('web_generation_steps')
      .insert({
        web_request_id: requestId,
        step_number: 3, // Updated: now step 3 instead of 4
        step_type: 'video_generate',
        status: 'in_progress',
        fal_model: 'fal-ai/pixverse/v4.5/transition',
        input_data: {
          first_image_url: generationRequest.input_data.uploaded_image_url,
          last_image_url: generationRequest.output_data.transformed_image_url,
          prompt: videoPrompt,
          duration: generationRequest.input_data.video_settings.duration,
          resolution: resolution,
          style: generationRequest.input_data.video_settings.style
        },
        token_cost: videoCost,
        started_at: new Date().toISOString()
      })
      .select()
      .single();

    if (stepError) {
      console.error('Video step creation error:', stepError);
      
      // Refund tokens if it was a paid user
      if (userId && wallet) {
          await supabaseAdmin
            .from('wallets')
            .update({ 
              balance: wallet.balance,
              updated_at: new Date().toISOString()
            })
            .eq('id', wallet.id);

          // Create refund transaction
          await supabaseAdmin
            .from('token_transactions')
            .insert({
              wallet_id: wallet.id,
              amount: videoCost,
              description: `Refund: Mortal Kombat video generation failed - ${resolution}`,
              transaction_type: 'refund'
            });
      }

      return NextResponse.json(
        { error: 'Failed to create video generation step' },
        { status: 500 }
      );
    }

    // Call FAL API for video generation using transition model
    try {
      const result = await fal.subscribe("fal-ai/pixverse/v4.5/transition", {
        input: {
          prompt: videoPrompt,
          first_image_url: generationRequest.input_data.uploaded_image_url,
          last_image_url: generationRequest.output_data.transformed_image_url,
          duration: generationRequest.input_data.video_settings.duration.toString(),
          resolution: resolution,
          ...(generationRequest.input_data.video_settings.style && {
            style: generationRequest.input_data.video_settings.style
          })
        },
        logs: true,
        onQueueUpdate: (update) => {
          if (update.status === "IN_PROGRESS") {
            console.log("Processing video:", update.logs?.map((log) => log.message).join('\n'));
          }
        },
      });

      if (!result.data || !result.data.video) {
        throw new Error('No video returned from API');
      }

      const videoUrl = result.data.video.url;

      // Update step with success
      await supabaseAdmin
        .from('web_generation_steps')
        .update({
          status: 'completed',
          fal_request_id: result.requestId,
          output_data: {
            video_url: videoUrl,
            original_response: result.data
          },
          completed_at: new Date().toISOString()
        })
        .eq('id', stepData.id);

      // Create video record
      await supabaseAdmin
        .from('web_videos')
        .insert({
          user_id: userId, // Can be null
          wallet_id: wallet?.id, // Can be null
          web_request_id: requestId,
          video_url: videoUrl,
          duration: generationRequest.input_data.video_settings.duration,
          resolution: resolution,
          prompt_text: videoPrompt,
          parameters: {
            style: generationRequest.input_data.video_settings.style,
            character_type: generationRequest.input_data.character_type
          },
          source_image_urls: [
            generationRequest.input_data.uploaded_image_url,
            generationRequest.output_data.transformed_image_url
          ],
          fal_request_id: result.requestId,
          fal_model: 'fal-ai/pixverse/v4.5/transition',
          token_cost: videoCost
        });

      // Update main request to completed
      const finalOutputData = {
        ...generationRequest.output_data,
        video_url: videoUrl
      };

      await supabaseAdmin
        .from('web_generation_requests')
        .update({ 
          status: 'completed',
          current_step: 4, // Set to step 4 for completion
          output_data: finalOutputData,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      return NextResponse.json({
        request_id: requestId,
        status: 'completed',
        current_step: 4,
        video: {
          url: videoUrl,
          duration: generationRequest.input_data.video_settings.duration,
          resolution: resolution,
          prompt: videoPrompt
        },
        final_output: {
          original_image_url: generationRequest.input_data.uploaded_image_url,
          transformed_image_url: generationRequest.output_data.transformed_image_url,
          video_url: videoUrl
        },
        total_cost: generationRequest.actual_cost + (videoCost / 100),
        tokens_charged: videoCost,
        remaining_balance: wallet ? wallet.balance - videoCost : 0
      });

    } catch (falError: any) {
      console.error('FAL video generation error:', falError);

      // Update step with failure
      await supabaseAdmin
        .from('web_generation_steps')
        .update({
          status: 'failed',
          error_message: falError.message || falError.toString(),
          completed_at: new Date().toISOString()
        })
        .eq('id', stepData.id);

      // Update main request
      await supabaseAdmin
        .from('web_generation_requests')
        .update({ 
          status: 'failed',
          error_message: `Video generation failed: ${falError.message || falError.toString()}`,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      // Refund the video cost for paid users
      if (userId && wallet) {
          await supabaseAdmin
            .from('wallets')
            .update({ 
              balance: wallet.balance,
              updated_at: new Date().toISOString()
            })
            .eq('id', wallet.id);

          // Create refund transaction
          await supabaseAdmin
            .from('token_transactions')
            .insert({
              wallet_id: wallet.id,
              amount: videoCost,
              description: `Refund: Video generation failed - ${falError.message || 'Unknown error'}`, 
              transaction_type: 'refund'
            });
      }

      return NextResponse.json(
        { 
          error: 'Video generation failed', 
          details: falError.message || falError.toString(),
          request_id: requestId,
          refunded_tokens: videoCost
        },
        { status: 500 }
      );
    }

  } catch (error: any) {
    console.error('Generate video error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to generate video', 
        details: error.message || error.toString() 
      },
      { status: 500 }
    );
  }
} 