import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Use service role client for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    // Get user ID from the Authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('user_id', user.id)
      .single();

    if (profileError || profile?.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Fetch all video records with related request and user data
    const { data: videos, error } = await supabaseAdmin
      .from('web_videos')
      .select(`
        *,
        web_generation_requests(
          id,
          status,
          flow_type,
          user_id,
          input_data
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching admin video requests:', error);
      return NextResponse.json({ error: 'Failed to fetch video requests' }, { status: 500 });
    }

    console.log(`Admin fetched ${videos?.length || 0} total video requests`);

    // Fetch user profiles
    const userIds = [...new Set(videos?.map(v => v.user_id) || [])].filter(Boolean);
    const { data: profiles, error: profilesError } = await supabaseAdmin
      .from('profiles')
      .select('user_id, email, role')
      .in('user_id', userIds);

    if (profilesError) {
      console.error('Error fetching user profiles:', profilesError);
    }

    // Create a lookup map for profiles
    const profilesMap = new Map();
    profiles?.forEach(profile => {
      profilesMap.set(profile.user_id, profile);
    });

    // Process the video data
    const processedVideos = videos?.map(video => {
      const userProfile = profilesMap.get(video.user_id);
      const userEmail = userProfile?.email;
      const userRole = userProfile?.role;
      const request = video.web_generation_requests;
      
      return {
        id: video.id,
        user_id: video.user_id,
        user_email: userEmail || 'Unknown',
        user_role: userRole || 'user',
        web_request_id: video.web_request_id,
        video_url: video.video_url,
        duration: video.duration,
        resolution: video.resolution,
        prompt_text: video.prompt_text,
        token_cost: video.token_cost,
        character_type: video.parameters?.character_type || request?.input_data?.character_type || 'unknown',
        style: video.parameters?.style,
        source_image_urls: video.source_image_urls || [],
        fal_request_id: video.fal_request_id,
        fal_model: video.fal_model,
        created_at: video.created_at,
        is_regeneration: video.parameters?.regeneration_number || false,
        request_status: request?.status || 'unknown',
        flow_type: request?.flow_type || 'unknown'
      };
    }) || [];

    // Calculate statistics
    const totalTokens = processedVideos.reduce((sum, video) => sum + (video.token_cost || 0), 0);
    const completedCount = processedVideos.filter(v => v.request_status === 'completed').length;
    const regeneratedCount = processedVideos.filter(v => v.is_regeneration).length;

    return NextResponse.json({ 
      videos: processedVideos,
      total_count: processedVideos.length,
      completed_count: completedCount,
      regenerated_count: regeneratedCount,
      total_tokens: totalTokens
    });

  } catch (error) {
    console.error('Error in admin video requests API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 