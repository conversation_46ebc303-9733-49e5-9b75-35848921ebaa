import en from '../locales/en.json';
import ro from '../locales/ro.json';

const translations = { en, ro };

export function useTranslation(lang: 'en' | 'ro') {
  const t = (key: string, vars?: Record<string, any>) => {
    let value = (translations[lang] as Record<string, any>)[key];
    if (Array.isArray(value)) return value;
    if (!value) return key;
    if (vars) {
      Object.entries(vars).forEach(([k, v]) => {
        value = value.replace(new RegExp(`{${k}}`, 'g'), v);
      });
    }
    return value;
  };
  return { t };
} 