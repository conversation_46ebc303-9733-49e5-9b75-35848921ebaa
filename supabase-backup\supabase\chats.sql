-- Enable the uuid-ossp extension if not already enabled (for generating UUIDs)
create extension if not exists "uuid-ossp";

-- Table to store chat sessions
create table if not exists chats (
  id uuid primary key default uuid_generate_v4(),
  user_id uuid not null references auth.users(id),
  start_time timestamp with time zone default now()
);

-- Table to store chat replies
create table if not exists chat_replies (
  id uuid primary key default uuid_generate_v4(),
  chat_id uuid not null references chats(id) on delete cascade,
  -- Indicate the role of the sender: "user" or "admin"
  sender_role text not null,
  message text not null,
  created_at timestamp with time zone default now()
); 