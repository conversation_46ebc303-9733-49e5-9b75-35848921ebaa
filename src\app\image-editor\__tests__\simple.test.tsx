import React from 'react'
import { render, screen } from '@testing-library/react'
import ImageEditor from '../page'

// Mock CSS modules
jest.mock('../../admin.module.css', () => ({}))

describe('ImageEditor Simple Tests', () => {
  it('renders the page title', () => {
    render(<ImageEditor />)
    const title = screen.getByText('Image Editor')
    expect(title).toBeTruthy()
  })

  it('renders the upload section', () => {
    render(<ImageEditor />)
    const uploadText = screen.getByText('Upload an Image')
    expect(uploadText).toBeTruthy()
  })

  it('has a file input', () => {
    render(<ImageEditor />)
    const fileInput = document.querySelector('input[type="file"]')
    expect(fileInput).toBeTruthy()
  })

  it('shows the description text', () => {
    render(<ImageEditor />)
    const description = screen.getByText('Upload an image and use AI to edit it with text prompts')
    expect(description).toBeTruthy()
  })
}) 