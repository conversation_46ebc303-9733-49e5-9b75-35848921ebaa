import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';
import { randomUUID } from 'crypto';
import { fal } from '@fal-ai/client';

// Configure the FAL client with the API key
fal.config({
  credentials: process.env.FAL_API_KEY || ''
});

// Character transformation prompts - same as regular MK flow
const CHARACTER_PROMPTS = {
  scorpion: "Dress this person as <PERSON><PERSON><PERSON> from Mortal Kombat. Keep their face, body, and background unchanged but add: yellow ninja outfit with black trim, <PERSON><PERSON><PERSON>'s signature skull mask covering lower face, kunai weapon with chain rope. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle fire/ember particle effects around the person without changing the background. Photorealistic, professional costume photography.",
  'sub-zero': "Dress this person as Sub-Zero from Mortal Kombat. Keep their face, body, and background unchanged but add: blue ninja outfit with ice-blue accents, Sub-Zero's signature ice mask covering lower face, frost effects around hands only. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle ice particle effects around the person without changing the background. Photorealistic, professional costume photography.",
  raiden: "Dress this person as <PERSON><PERSON> from Mortal Kombat. Keep their face, body, and background unchanged but add: white and blue traditional outfit with lightning patterns, <PERSON>den's conical straw hat, subtle blue glow in eyes. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle electrical spark effects around the person without changing the background. Photorealistic, professional costume photography.",
  'liu-kang': "Dress this person as Liu Kang from Mortal Kombat. Keep their face, body, and background unchanged but add: red martial arts outfit with black trim and dragon motifs, red bandana/headband. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle fire particle effects around fists without changing the background. Photorealistic, professional costume photography.",
  kitana: "Dress this person as Kitana from Mortal Kombat. Keep their face, body, and background unchanged but add: royal blue and black outfit with elegant design, decorative mask covering lower face, steel fans as weapons. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle magical sparkle effects around the person without changing the background. Photorealistic, professional costume photography.",
  'johnny-cage': "Dress this person as Johnny Cage from Mortal Kombat. Keep their face, body, and background unchanged but add: designer sunglasses, black military pants, open vest showing chest, confident Hollywood action star look. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle green energy glow around hands without changing the background. Photorealistic, professional costume photography.",
  mileena: "Dress this person as Mileena from Mortal Kombat. Keep their face, body, and background unchanged but add: purple ninja outfit with revealing design, Mileena's signature sai weapons, pink/purple mask covering lower face. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle dark energy effects around the person without changing the background. Photorealistic, professional costume photography.",
  'kung-lao': "Dress this person as Kung Lao from Mortal Kombat. Keep their face, body, and background unchanged but add: traditional Shaolin monk robes in blue and white, Kung Lao's signature razor-rimmed hat. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle wind effects around the hat without changing the background. Photorealistic, professional costume photography.",
  sindel: "Dress this person as Sindel from Mortal Kombat. Keep their face, body, and background unchanged but add: regal purple and black outfit with silver accents, long flowing white hair, Sindel's signature spiked tiara, and mystical aura. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle purple energy waves around the person without changing the background. Photorealistic, professional costume photography.",
  fujin: "Dress this person as Fujin from Mortal Kombat. Keep their face, body, and background unchanged but add: wind god attire with white and blue robes, Fujin's signature long ponytail, silver armor, and a staff. Maintain the person's original features, skin tone, body type, and preserve the original background completely. Add only subtle wind swirl effects around the person without changing the background. Photorealistic, professional costume photography."
};

interface FreeConfirmResizeRequest {
  resizedImageUrl: string;
  character_type: string;
  video_settings: {
    duration: 5 | 8;
    resolution: '360p' | '540p' | '720p' | '1080p';
    style?: 'anime' | '3d_animation' | 'comic';
  };
  freeGenerationId: string;
}

async function validateVoucher(supabase: any, freeGenerationId: string) {
  console.log('Validating voucher ID for resize confirm:', freeGenerationId);
  
  const { data: voucher, error } = await supabase
    .from('free_generations')
    .select('status')
    .eq('id', freeGenerationId)
    .single();

  console.log('Voucher query result:', { voucher, error });

  if (error || !voucher) {
    console.log('Voucher validation failed: Invalid or expired link');
    return { isValid: false, message: 'Invalid or expired link.' };
  }

  if (voucher.status !== 'new') {
    console.log('Voucher validation failed: Status is', voucher.status, 'but expected "new"');
    return { isValid: false, message: 'This link has already been used.' };
  }

  console.log('Voucher validation successful');
  return { isValid: true };
}

export async function POST(req: Request) {
  // Create a Supabase client specifically for server-side use with service role key
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    process.env.SUPABASE_SERVICE_ROLE_KEY || ''
  );

  try {
    const body: FreeConfirmResizeRequest = await req.json();
    const { resizedImageUrl, character_type, video_settings, freeGenerationId } = body;

    if (!resizedImageUrl || !character_type || !video_settings || !freeGenerationId) {
      return NextResponse.json({ error: 'Missing required fields.' }, { status: 400 });
    }

    // 1. Validate the voucher
    const { isValid, message } = await validateVoucher(supabase, freeGenerationId);
    if (!isValid) {
      return NextResponse.json({ error: message }, { status: 403 });
    }

    // 2. Get the selected image generator
    const { data: configData, error: configError } = await supabase
      .from('config_options')
      .select('value')
      .eq('key', 'mk_image_generator')
      .single();

    const selectedImageGenerator = (configData?.value?.selected) || 'gpt-image-1';

    // 3. Create the Web Generation Request using the resized image
    const { data: webRequest, error: requestError } = await supabase
      .from('web_generation_requests')
      .insert({
        flow_type: 'mortal_kombat',
        status: 'pending',
        current_step: 1,
        total_steps: 4,
        input_data: {
          original_image_url: resizedImageUrl, // Use the resized image
          settings: {
            character_type,
            video_settings
          },
          selected_image_generator: selectedImageGenerator,
          was_resized: true // Flag to indicate this was a resized image
        },
        // user_id and wallet_id are NULL for free generations
      })
      .select()
      .single();

    if (requestError) {
      console.error('Error creating web generation request:', requestError);
      return NextResponse.json({ error: 'Failed to initialize generation.' }, { status: 500 });
    }

    // 4. Mark the voucher as 'claimed' and link it to the request
    const { error: updateVoucherError } = await supabase
      .from('free_generations')
      .update({ status: 'claimed', web_generation_request_id: webRequest.id })
      .eq('id', freeGenerationId);

    if (updateVoucherError) {
      console.error('Failed to update voucher status:', updateVoucherError);
    }

    console.log('Proceeding with transformation using resized image for free generation:', resizedImageUrl);

    return NextResponse.json({
      success: true,
      request_id: webRequest.id,
      id: webRequest.id,
      message: 'Proceeding with resized image...',
      status: 'pending',
      current_step: 1
    });

  } catch (error) {
    console.error('Free generation confirm resize error:', error);
    return NextResponse.json({ error: 'An unexpected error occurred.' }, { status: 500 });
  }
} 