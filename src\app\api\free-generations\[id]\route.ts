import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: requestId } = await params;
    
    // Create a Supabase client specifically for server-side use with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    );

    // Get the generation request
    const { data: generationRequest, error: requestError } = await supabase
      .from('web_generation_requests')
      .select('*')
      .eq('id', requestId)
      .is('user_id', null) // Ensure this is a free generation (user_id is null)
      .single();

    if (requestError || !generationRequest) {
      console.error('Request error:', requestError);
      console.error('Looking for request ID:', requestId);
      return NextResponse.json(
        { error: 'Free generation request not found', details: requestError?.message },
        { status: 404 }
      );
    }

    // Get the associated free_generation record (optional - just for additional info)
    const { data: freeGeneration, error: freeGenError } = await supabase
      .from('free_generations')
      .select('*')
      .eq('web_generation_request_id', requestId)
      .single();

    // Don't fail if no voucher found - just log it
    if (freeGenError) {
      console.log('Free generation voucher not found (this is ok for status checks):', freeGenError);
    }

    // Get all steps for this request
    const { data: steps, error: stepsError } = await supabase
      .from('web_generation_steps')
      .select('*')
      .eq('web_request_id', requestId)
      .order('step_number', { ascending: true });

    if (stepsError) {
      console.error('Error fetching steps:', stepsError);
    }

    // Format the response similar to the regular web-generations endpoint
    const response: any = {
      request_id: generationRequest.id,
      status: generationRequest.status,
      current_step: generationRequest.current_step,
      total_steps: generationRequest.total_steps,
      awaiting_approval_for_step: generationRequest.awaiting_approval_for_step,
      input_data: generationRequest.input_data,
      output_data: generationRequest.output_data,
      error_message: generationRequest.error_message,
      created_at: generationRequest.created_at,
      updated_at: generationRequest.updated_at,
      steps: steps || [],
      // Add transformation data if available
      transformation: null,
      final_output: null
    };

    // Debug logging
    console.log('Free generation status check:', {
      requestId,
      status: generationRequest.status,
      hasOutputData: !!generationRequest.output_data,
      outputData: generationRequest.output_data,
      inputData: generationRequest.input_data
    });

    // Check if we have transformation data
    const transformStep = steps?.find(step => step.step_type === 'image_transform' && step.status === 'awaiting_approval');
    if (transformStep && transformStep.output_data?.transformed_image_url) {
      response.transformation = {
        original_image_url: generationRequest.input_data.uploaded_image_url || generationRequest.input_data.original_image_url,
        transformed_image_url: transformStep.output_data.transformed_image_url,
        character_type: generationRequest.input_data.character_type,
        prompt_used: transformStep.input_data?.prompt || ''
      };
    } else if (generationRequest.output_data?.transformed_image_url) {
      // For free generations, transformation data is stored directly in the main request
      // Check this regardless of status to ensure transformation data is returned when available
      const originalImageUrl = generationRequest.input_data.original_image_url || 
                              generationRequest.input_data.uploaded_image_url ||
                              generationRequest.input_data.image_url;
                              
      const characterType = generationRequest.input_data.settings?.character_type || 
                          generationRequest.input_data.character_type ||
                          generationRequest.output_data.character;
      
      console.log('Setting transformation data for free generation:', {
        original_image_url: originalImageUrl,
        transformed_image_url: generationRequest.output_data.transformed_image_url,
        character_type: characterType,
        prompt_used: generationRequest.output_data.character || characterType || ''
      });
      
      response.transformation = {
        original_image_url: originalImageUrl,
        transformed_image_url: generationRequest.output_data.transformed_image_url,
        character_type: characterType,
        prompt_used: generationRequest.output_data.character || characterType || ''
      };
    }

    // Check if we have final output (completed generation)
    if (generationRequest.status === 'completed' && generationRequest.output_data) {
      response.final_output = {
        original_image_url: generationRequest.input_data.original_image_url,
        transformed_image_url: generationRequest.output_data.transformed_image_url,
        video_url: generationRequest.output_data.video_url
      };
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching free generation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 