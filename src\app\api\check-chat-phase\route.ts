import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// Create a Supabase client using the service role key (kept secret)
const supabaseServiceRole = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: Request) {
  try {
    const { chatId } = await request.json();
    if (!chatId) {
      return NextResponse.json({ error: "chatId is required" }, { status: 400 });
    }
    
    // Fetch the current chat record (we only need the phase)
    const { data: chatData, error: chatError } = await supabaseServiceRole
      .from("chats")
      .select("phase")
      .eq("id", chatId)
      .single();
      
    if (chatError || !chatData) {
      return NextResponse.json({ error: chatError?.message || "Chat not found" }, { status: 500 });
    }
    
    // If the chat is not in phase 1, do nothing and return the current chat data.
    if (chatData.phase !== 1) {
      return NextResponse.json({ success: true, data: chatData });
    }
    
    // Check for at least one text reply (non-empty 'message').
    const { count: textCount, error: textError } = await supabaseServiceRole
      .from("chat_replies")
      .select("id", { count: "exact", head: true })
      .eq("chat_id", chatId)
      .neq("message", "");
      
    if (textError) {
      return NextResponse.json({ error: textError.message }, { status: 500 });
    }
    
    // Check for at least one image reply (where image_url is not null).
    const { count: imageCount, error: imageError } = await supabaseServiceRole
      .from("chat_replies")
      .select("id", { count: "exact", head: true })
      .eq("chat_id", chatId)
      .neq("image_url", null);
      
    if (imageError) {
      return NextResponse.json({ error: imageError.message }, { status: 500 });
    }
    
    // If both conditions are met, update the chat's phase to 2 and add a system reply.
    if (textCount !== null && textCount > 0 && imageCount !== null && imageCount > 0) {
      const { data: updatedChat, error: updateError } = await supabaseServiceRole
        .from("chats")
        .update({ phase: 2 })
        .eq("id", chatId)
        .single();
        
      if (updateError) {
        return NextResponse.json({ error: updateError.message }, { status: 500 });
      }
      
      // Insert a system reply notifying the user.
      const systemMsg = "Thank you for sending your request. We will process what you've sent and get back to you.";
      const { error: sysMsgError } = await supabaseServiceRole
        .from("chat_replies")
        .insert({
          chat_id: chatId,
          sender_role: "system",
          message: systemMsg,
        })
        .select()
        .single();
      
      if (sysMsgError) {
        return NextResponse.json({ error: sysMsgError.message }, { status: 500 });
      }
      
      return NextResponse.json({ success: true, data: updatedChat });
    } else {
      // If conditions aren't met, return the current chat record.
      return NextResponse.json({ success: false, data: chatData });
    }
    
  } catch (err: any) {
    return NextResponse.json({ error: err.message }, { status: 500 });
  }
} 